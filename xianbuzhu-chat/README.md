# 聊天服务 (Chat Service)

## 项目概述

这是社区闲置物品置换平台的实时聊天服务，专门负责处理WebSocket连接、实时消息传递、消息持久化和推送通知等功能。

## 功能职责

### 🎯 **核心职责**
- WebSocket连接管理和维护
- 实时消息传递和广播
- 消息持久化存储
- 在线状态管理
- 消息推送和通知
- 聊天室管理
- 消息审核和过滤

### 🔌 **服务接口**
- WebSocket连接 (`ws://localhost:3001`)
- HTTP API (`/api/chat/*`)
- 内部服务调用接口
- 健康检查接口

## 技术栈

- **运行环境**: Node.js 18+
- **框架**: Express.js + Socket.io + TypeScript
- **数据库**: PostgreSQL (消息存储)
- **缓存**: Redis (在线状态、消息队列)
- **消息队列**: Redis Pub/Sub
- **认证**: JWT验证
- **监控**: Prometheus + Grafana

## 项目结构

```
chat-service/
├── src/
│   ├── controllers/          # HTTP控制器
│   │   ├── chatController.ts
│   │   ├── roomController.ts
│   │   └── messageController.ts
│   ├── services/             # 业务逻辑层
│   │   ├── chatService.ts
│   │   ├── roomService.ts
│   │   ├── messageService.ts
│   │   ├── notificationService.ts
│   │   └── auditService.ts
│   ├── socket/               # WebSocket处理
│   │   ├── socketHandler.ts
│   │   ├── connectionManager.ts
│   │   ├── messageHandler.ts
│   │   └── roomHandler.ts
│   ├── repositories/         # 数据访问层
│   │   ├── chatRepository.ts
│   │   ├── messageRepository.ts
│   │   └── roomRepository.ts
│   ├── middleware/           # 中间件
│   │   ├── auth.ts
│   │   ├── rateLimit.ts
│   │   └── validation.ts
│   ├── utils/                # 工具函数
│   │   ├── redis.ts
│   │   ├── jwt.ts
│   │   ├── logger.ts
│   │   └── metrics.ts
│   ├── config/               # 配置文件
│   │   ├── database.ts
│   │   ├── redis.ts
│   │   └── socket.ts
│   └── types/                # 类型定义
├── tests/                    # 测试文件
└── docs/                     # 文档
```

## 环境配置

### 环境变量
```bash
# .env
# 服务配置
NODE_ENV=development
PORT=3001
SERVICE_NAME=chat-service

# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/community_exchange"
REDIS_URL="redis://localhost:6379"

# JWT配置
JWT_SECRET="your-jwt-secret-key"

# API网关配置
API_GATEWAY_URL="http://localhost:3000"
API_GATEWAY_SECRET="your-api-gateway-secret"

# 消息推送配置
WECHAT_APP_ID="your-wechat-app-id"
WECHAT_APP_SECRET="your-wechat-app-secret"

# 监控配置
METRICS_PORT=9090
LOG_LEVEL=info
```

## 核心功能实现

### 1. WebSocket连接管理

```typescript
// src/socket/connectionManager.ts
import { Server, Socket } from 'socket.io'
import { RedisClient } from '../utils/redis'
import { JWTService } from '../utils/jwt'
import { logger } from '../utils/logger'

export class ConnectionManager {
  private connections = new Map<string, Socket>()
  private userSockets = new Map<string, Set<string>>()

  constructor(
    private io: Server,
    private redis: RedisClient,
    private jwtService: JWTService
  ) {}

  async handleConnection(socket: Socket) {
    try {
      // 1. 验证JWT token
      const token = socket.handshake.auth.token
      const payload = this.jwtService.verify(token)
      const userId = payload.userId

      // 2. 存储连接信息
      socket.userId = userId
      this.connections.set(socket.id, socket)
      
      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, new Set())
      }
      this.userSockets.get(userId)!.add(socket.id)

      // 3. 更新在线状态
      await this.updateUserOnlineStatus(userId, true)

      // 4. 加入用户房间
      socket.join(`user:${userId}`)

      // 5. 获取用户的聊天室列表并加入
      const chatRooms = await this.getChatRooms(userId)
      for (const room of chatRooms) {
        socket.join(`room:${room.id}`)
      }

      logger.info(`User ${userId} connected with socket ${socket.id}`)

      // 6. 设置事件监听器
      this.setupEventListeners(socket)

    } catch (error) {
      logger.error('Connection authentication failed:', error)
      socket.disconnect()
    }
  }

  async handleDisconnection(socket: Socket) {
    const userId = socket.userId
    if (!userId) return

    // 1. 移除连接记录
    this.connections.delete(socket.id)
    const userSockets = this.userSockets.get(userId)
    if (userSockets) {
      userSockets.delete(socket.id)
      if (userSockets.size === 0) {
        this.userSockets.delete(userId)
        // 用户完全离线，更新状态
        await this.updateUserOnlineStatus(userId, false)
      }
    }

    logger.info(`User ${userId} disconnected from socket ${socket.id}`)
  }

  private setupEventListeners(socket: Socket) {
    socket.on('join_room', (roomId: string) => {
      socket.join(`room:${roomId}`)
      logger.info(`User ${socket.userId} joined room ${roomId}`)
    })

    socket.on('leave_room', (roomId: string) => {
      socket.leave(`room:${roomId}`)
      logger.info(`User ${socket.userId} left room ${roomId}`)
    })

    socket.on('disconnect', () => {
      this.handleDisconnection(socket)
    })
  }

  private async updateUserOnlineStatus(userId: string, isOnline: boolean) {
    const key = `user:${userId}:online`
    if (isOnline) {
      await this.redis.setex(key, 300, '1') // 5分钟过期
    } else {
      await this.redis.del(key)
    }
  }

  async isUserOnline(userId: string): Promise<boolean> {
    const result = await this.redis.get(`user:${userId}:online`)
    return result === '1'
  }

  getUserSockets(userId: string): Socket[] {
    const socketIds = this.userSockets.get(userId) || new Set()
    return Array.from(socketIds)
      .map(id => this.connections.get(id))
      .filter(socket => socket !== undefined) as Socket[]
  }
}
```

### 2. 消息处理服务

```typescript
// src/services/messageService.ts
import { PrismaClient } from '@prisma/client'
import { Message, SendMessageRequest } from '../types'
import { ConnectionManager } from '../socket/connectionManager'
import { NotificationService } from './notificationService'
import { AuditService } from './auditService'

export class MessageService {
  constructor(
    private prisma: PrismaClient,
    private connectionManager: ConnectionManager,
    private notificationService: NotificationService,
    private auditService: AuditService
  ) {}

  async sendMessage(
    roomId: string,
    senderId: string,
    messageData: SendMessageRequest
  ): Promise<Message> {
    // 1. 验证聊天室权限
    const room = await this.prisma.chatRoom.findUnique({
      where: { id: roomId },
      include: { participants: true }
    })

    if (!room || !room.participants.some(p => p.userId === senderId)) {
      throw new Error('无权限发送消息到此聊天室')
    }

    // 2. 内容审核
    if (messageData.type === 'text') {
      const auditResult = await this.auditService.auditText(messageData.content)
      if (!auditResult.pass) {
        throw new Error('消息内容不符合规范')
      }
    }

    // 3. 保存消息到数据库
    const message = await this.prisma.message.create({
      data: {
        roomId,
        senderId,
        content: messageData.content,
        type: messageData.type,
        attachments: messageData.attachments || []
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    })

    // 4. 实时广播消息
    await this.broadcastMessage(roomId, message, senderId)

    // 5. 发送推送通知给离线用户
    await this.sendPushNotifications(room, message, senderId)

    // 6. 更新聊天室最后消息
    await this.updateRoomLastMessage(roomId, message)

    return this.formatMessage(message)
  }

  private async broadcastMessage(roomId: string, message: any, senderId: string) {
    const io = this.connectionManager.io
    
    // 广播给房间内的所有用户（除了发送者）
    io.to(`room:${roomId}`).emit('message_received', {
      ...this.formatMessage(message),
      isOwn: false
    })

    // 给发送者发送确认
    const senderSockets = this.connectionManager.getUserSockets(senderId)
    senderSockets.forEach(socket => {
      socket.emit('message_sent', {
        ...this.formatMessage(message),
        isOwn: true
      })
    })
  }

  private async sendPushNotifications(room: any, message: any, senderId: string) {
    const offlineParticipants = []
    
    for (const participant of room.participants) {
      if (participant.userId !== senderId) {
        const isOnline = await this.connectionManager.isUserOnline(participant.userId)
        if (!isOnline) {
          offlineParticipants.push(participant.userId)
        }
      }
    }

    // 发送微信模板消息给离线用户
    for (const userId of offlineParticipants) {
      await this.notificationService.sendChatNotification(userId, {
        senderName: message.sender.name,
        content: this.getNotificationContent(message),
        roomId: room.id
      })
    }
  }

  private async updateRoomLastMessage(roomId: string, message: any) {
    await this.prisma.chatRoom.update({
      where: { id: roomId },
      data: {
        lastMessageId: message.id,
        updatedAt: new Date()
      }
    })
  }

  async getMessages(
    roomId: string,
    userId: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<{ messages: Message[], hasMore: boolean }> {
    // 1. 验证权限
    const room = await this.prisma.chatRoom.findUnique({
      where: { id: roomId },
      include: { participants: true }
    })

    if (!room || !room.participants.some(p => p.userId === userId)) {
      throw new Error('无权限查看此聊天室消息')
    }

    // 2. 获取消息列表
    const messages = await this.prisma.message.findMany({
      where: { roomId },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * pageSize,
      take: pageSize + 1 // 多取一条判断是否还有更多
    })

    const hasMore = messages.length > pageSize
    const resultMessages = hasMore ? messages.slice(0, -1) : messages

    return {
      messages: resultMessages.reverse().map(msg => this.formatMessage(msg)),
      hasMore
    }
  }

  async markAsRead(roomId: string, userId: string): Promise<void> {
    // 更新用户在该聊天室的已读状态
    await this.prisma.chatRoomParticipant.updateMany({
      where: {
        roomId,
        userId
      },
      data: {
        lastReadAt: new Date()
      }
    })

    // 通知其他用户消息已读
    const io = this.connectionManager.io
    io.to(`room:${roomId}`).emit('message_read', {
      roomId,
      userId,
      readAt: new Date()
    })
  }

  private formatMessage(message: any): Message {
    return {
      id: message.id,
      roomId: message.roomId,
      senderId: message.senderId,
      content: message.content,
      type: message.type,
      attachments: message.attachments,
      sender: message.sender,
      createdAt: message.createdAt
    }
  }

  private getNotificationContent(message: any): string {
    switch (message.type) {
      case 'text':
        return message.content.length > 20 
          ? message.content.substring(0, 20) + '...'
          : message.content
      case 'image':
        return '[图片]'
      case 'voice':
        return '[语音]'
      case 'location':
        return '[位置]'
      default:
        return '[消息]'
    }
  }
}
```

### 3. 聊天室管理服务

```typescript
// src/services/roomService.ts
import { PrismaClient } from '@prisma/client'
import { ChatRoom } from '../types'
import { ConnectionManager } from '../socket/connectionManager'

export class RoomService {
  constructor(
    private prisma: PrismaClient,
    private connectionManager: ConnectionManager
  ) {}

  async createChatRoom(data: {
    participants: string[]
    relatedItemId?: string
    exchangeId?: string
  }): Promise<ChatRoom> {
    // 1. 检查是否已存在相同的聊天室
    if (data.relatedItemId) {
      const existingRoom = await this.prisma.chatRoom.findFirst({
        where: {
          relatedItemId: data.relatedItemId,
          participants: {
            every: {
              userId: { in: data.participants }
            }
          }
        }
      })

      if (existingRoom) {
        return this.formatChatRoom(existingRoom)
      }
    }

    // 2. 创建聊天室
    const room = await this.prisma.chatRoom.create({
      data: {
        relatedItemId: data.relatedItemId,
        exchangeId: data.exchangeId,
        participants: {
          create: data.participants.map(userId => ({
            userId,
            joinedAt: new Date()
          }))
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatar: true
              }
            }
          }
        },
        lastMessage: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                avatar: true
              }
            }
          }
        }
      }
    })

    // 3. 通知参与者加入聊天室
    for (const participantId of data.participants) {
      const sockets = this.connectionManager.getUserSockets(participantId)
      sockets.forEach(socket => {
        socket.join(`room:${room.id}`)
        socket.emit('room_created', this.formatChatRoom(room))
      })
    }

    return this.formatChatRoom(room)
  }

  async getChatRooms(userId: string): Promise<ChatRoom[]> {
    const rooms = await this.prisma.chatRoom.findMany({
      where: {
        participants: {
          some: {
            userId
          }
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatar: true
              }
            }
          }
        },
        lastMessage: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                avatar: true
              }
            }
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    return rooms.map(room => this.formatChatRoom(room))
  }

  async getUnreadCount(userId: string, roomId: string): Promise<number> {
    const participant = await this.prisma.chatRoomParticipant.findUnique({
      where: {
        roomId_userId: {
          roomId,
          userId
        }
      }
    })

    if (!participant) return 0

    const unreadCount = await this.prisma.message.count({
      where: {
        roomId,
        senderId: { not: userId },
        createdAt: {
          gt: participant.lastReadAt || participant.joinedAt
        }
      }
    })

    return unreadCount
  }

  private formatChatRoom(room: any): ChatRoom {
    return {
      id: room.id,
      participants: room.participants.map((p: any) => p.user),
      relatedItemId: room.relatedItemId,
      lastMessage: room.lastMessage ? {
        id: room.lastMessage.id,
        content: room.lastMessage.content,
        type: room.lastMessage.type,
        sender: room.lastMessage.sender,
        createdAt: room.lastMessage.createdAt
      } : undefined,
      createdAt: room.createdAt,
      updatedAt: room.updatedAt
    }
  }
}
```

### 4. Socket事件处理

```typescript
// src/socket/socketHandler.ts
import { Server, Socket } from 'socket.io'
import { MessageService } from '../services/messageService'
import { RoomService } from '../services/roomService'
import { logger } from '../utils/logger'

export class SocketHandler {
  constructor(
    private io: Server,
    private messageService: MessageService,
    private roomService: RoomService
  ) {}

  setupEventHandlers(socket: Socket) {
    // 发送消息
    socket.on('send_message', async (data) => {
      try {
        const message = await this.messageService.sendMessage(
          data.roomId,
          socket.userId,
          {
            content: data.content,
            type: data.type,
            attachments: data.attachments
          }
        )
        
        // 发送成功确认
        socket.emit('message_sent_success', { messageId: message.id })
      } catch (error) {
        logger.error('Send message error:', error)
        socket.emit('message_sent_error', { 
          error: error.message,
          requestId: data.requestId 
        })
      }
    })

    // 标记消息已读
    socket.on('mark_as_read', async (data) => {
      try {
        await this.messageService.markAsRead(data.roomId, socket.userId)
      } catch (error) {
        logger.error('Mark as read error:', error)
      }
    })

    // 开始输入
    socket.on('typing_start', (data) => {
      socket.to(`room:${data.roomId}`).emit('user_typing', {
        userId: socket.userId,
        roomId: data.roomId,
        isTyping: true
      })
    })

    // 停止输入
    socket.on('typing_stop', (data) => {
      socket.to(`room:${data.roomId}`).emit('user_typing', {
        userId: socket.userId,
        roomId: data.roomId,
        isTyping: false
      })
    })

    // 加入聊天室
    socket.on('join_room', async (data) => {
      try {
        socket.join(`room:${data.roomId}`)
        
        // 获取未读消息数量
        const unreadCount = await this.roomService.getUnreadCount(
          socket.userId,
          data.roomId
        )
        
        socket.emit('room_joined', {
          roomId: data.roomId,
          unreadCount
        })
      } catch (error) {
        logger.error('Join room error:', error)
      }
    })

    // 离开聊天室
    socket.on('leave_room', (data) => {
      socket.leave(`room:${data.roomId}`)
      socket.emit('room_left', { roomId: data.roomId })
    })

    // 获取聊天历史
    socket.on('get_messages', async (data) => {
      try {
        const result = await this.messageService.getMessages(
          data.roomId,
          socket.userId,
          data.page,
          data.pageSize
        )
        
        socket.emit('messages_loaded', {
          roomId: data.roomId,
          messages: result.messages,
          hasMore: result.hasMore,
          page: data.page
        })
      } catch (error) {
        logger.error('Get messages error:', error)
        socket.emit('messages_load_error', {
          roomId: data.roomId,
          error: error.message
        })
      }
    })
  }
}
```

## HTTP API接口

### 聊天室管理API
```typescript
// src/controllers/chatController.ts
import { Request, Response } from 'express'
import { RoomService } from '../services/roomService'
import { MessageService } from '../services/messageService'

export class ChatController {
  constructor(
    private roomService: RoomService,
    private messageService: MessageService
  ) {}

  async getChatRooms(req: Request, res: Response) {
    try {
      const userId = req.user.userId
      const rooms = await this.roomService.getChatRooms(userId)
      
      res.json({
        success: true,
        data: rooms
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        error: { message: error.message }
      })
    }
  }

  async createChatRoom(req: Request, res: Response) {
    try {
      const { participants, relatedItemId, exchangeId } = req.body
      
      const room = await this.roomService.createChatRoom({
        participants,
        relatedItemId,
        exchangeId
      })
      
      res.status(201).json({
        success: true,
        data: room
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        error: { message: error.message }
      })
    }
  }

  async getMessages(req: Request, res: Response) {
    try {
      const { roomId } = req.params
      const { page = 1, pageSize = 20 } = req.query
      const userId = req.user.userId
      
      const result = await this.messageService.getMessages(
        roomId,
        userId,
        Number(page),
        Number(pageSize)
      )
      
      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        error: { message: error.message }
      })
    }
  }
}
```

## 部署配置

### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 更改文件所有权
RUN chown -R nodejs:nodejs /app
USER nodejs

# 暴露端口
EXPOSE 3001

# 启动应用
CMD ["npm", "start"]
```

## 监控和指标

### 性能指标
```typescript
// src/utils/metrics.ts
import prometheus from 'prom-client'

// WebSocket连接数
export const activeConnections = new prometheus.Gauge({
  name: 'chat_active_connections',
  help: 'Number of active WebSocket connections'
})

// 消息发送速率
export const messagesSent = new prometheus.Counter({
  name: 'chat_messages_sent_total',
  help: 'Total number of messages sent',
  labelNames: ['room_type', 'message_type']
})

// 消息处理延迟
export const messageProcessingDuration = new prometheus.Histogram({
  name: 'chat_message_processing_duration_seconds',
  help: 'Duration of message processing',
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1]
})

// 聊天室数量
export const activeChatRooms = new prometheus.Gauge({
  name: 'chat_active_rooms',
  help: 'Number of active chat rooms'
})
```

## 开发和测试

### 启动开发服务器
```bash
# 安装依赖
npm install

# 配置环境变量
cp .env.example .env

# 启动开发服务器
npm run dev
```

### WebSocket客户端测试
```typescript
// tests/socket.test.ts
import { io, Socket } from 'socket.io-client'

describe('Chat Service WebSocket', () => {
  let clientSocket: Socket

  beforeAll((done) => {
    clientSocket = io('http://localhost:3001', {
      auth: {
        token: 'valid-jwt-token'
      }
    })
    clientSocket.on('connect', done)
  })

  afterAll(() => {
    clientSocket.close()
  })

  test('should send and receive messages', (done) => {
    clientSocket.emit('send_message', {
      roomId: 'test-room-id',
      content: 'Hello, World!',
      type: 'text'
    })

    clientSocket.on('message_sent_success', (data) => {
      expect(data.messageId).toBeDefined()
      done()
    })
  })
})
```

## 相关项目

- [API网关](../api-gateway/README.md)
- [微信小程序](../miniprogram/README.md)
- [管理后台](../admin-dashboard/README.md)

## 许可证

本项目采用 MIT 许可证。