"use strict";
/**
 * 聊天服务配置文件
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
// 加载环境变量
dotenv_1.default.config();
exports.config = {
    // 服务配置
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: parseInt(process.env.PORT || '3001'),
    API_VERSION: process.env.API_VERSION || 'v1',
    // 安全配置
    SECRET_KEY: process.env.SECRET_KEY || 'your-secret-key',
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
    ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS || 'http://localhost:3000',
    // 数据库配置
    REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379',
    // API服务配置
    API_GATEWAY_URL: process.env.API_GATEWAY_URL || 'http://localhost:3000',
    // 日志配置
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    LOG_DIR: process.env.LOG_DIR || './logs',
};
// 验证必要的配置
if (!exports.config.SECRET_KEY) {
    throw new Error('SECRET_KEY 环境变量未设置');
}
//# sourceMappingURL=index.js.map