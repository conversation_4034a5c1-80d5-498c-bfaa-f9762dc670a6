"use strict";
/**
 * 健康检查路由
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
// 基本健康检查
router.get('/', (req, res) => {
    res.json({
        success: true,
        status: 'UP',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: config_1.config.NODE_ENV
    });
});
// 详细健康检查
router.get('/detail', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 这里可以添加数据库连接检查、Redis连接检查等
        const healthStatus = {
            success: true,
            status: 'UP',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: config_1.config.NODE_ENV,
            services: {
                redis: 'CONNECTED', // 模拟Redis连接状态
                apiGateway: 'CONNECTED' // 模拟API网关连接状态
            }
        };
        res.json(healthStatus);
    }
    catch (error) {
        logger_1.logger.error('健康检查失败', { error });
        res.status(503).json({
            success: false,
            status: 'DOWN',
            error: { message: '服务不可用' },
            timestamp: new Date().toISOString()
        });
    }
}));
exports.default = router;
//# sourceMappingURL=health.js.map