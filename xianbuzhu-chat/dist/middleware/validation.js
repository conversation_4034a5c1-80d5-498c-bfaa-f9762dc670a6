"use strict";
/**
 * 请求验证中间件
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateRequest = void 0;
const errorHandler_1 = require("./errorHandler");
const logger_1 = require("../utils/logger");
/**
 * 验证请求中间件
 * @param schema Yup验证模式
 * @returns Express中间件函数
 */
const validateRequest = (schema) => {
    return (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            // 验证请求
            yield schema.validate({
                body: req.body,
                query: req.query,
                params: req.params
            }, {
                abortEarly: false, // 收集所有错误
                stripUnknown: true // 删除未知字段
            });
            // 验证通过，继续处理请求
            next();
        }
        catch (error) {
            // 处理验证错误
            if (error.name === 'ValidationError') {
                const errors = error.inner.map((err) => ({
                    field: err.path,
                    message: err.message
                }));
                logger_1.logger.warn('请求验证失败', { errors, path: req.path });
                next(new errorHandler_1.AppError('请求参数无效', 400, 'INVALID_PARAMETERS', errors));
            }
            else {
                // 其他错误
                next(error);
            }
        }
    });
};
exports.validateRequest = validateRequest;
//# sourceMappingURL=validation.js.map