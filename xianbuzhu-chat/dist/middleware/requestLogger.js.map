{"version": 3, "file": "requestLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/requestLogger.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAGH,4CAAwC;AAExC,UAAU;AACV,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACxE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IACxB,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAE5F,aAAa;IACb,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,CAAA;IAExC,SAAS;IACT,eAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,WAAW,EAAE;QAClD,SAAS;QACT,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,OAAO,EAAE;YACP,YAAY,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACvC,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC;YAC3C,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC/B,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC;SACtC;QACD,EAAE,EAAE,GAAG,CAAC,EAAE;KACX,CAAC,CAAA;IAEF,WAAW;IACX,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;QACnC,eAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,WAAW,EAAE;YAClD,SAAS;YACT,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,QAAQ,EAAE,GAAG,QAAQ,IAAI;SAC1B,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AAEQ,sCAAa"}