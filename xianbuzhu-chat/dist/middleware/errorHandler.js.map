{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAGH,4CAAwC;AAExC,SAAS;AACT,MAAM,QAAS,SAAQ,KAAK;IAK1B,YAAY,OAAe,EAAE,UAAkB,EAAE,SAAkB,EAAE,OAAa;QAChF,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,eAAe,CAAA;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAA;QAEtB,QAAQ;QACR,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACjD,CAAC;CACF;AAyCQ,4BAAQ;AAvCjB,UAAU;AACV,MAAM,YAAY,GAAG,CAAC,GAAU,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACnF,SAAS;IACT,IAAI,UAAU,GAAG,GAAG,CAAA;IACpB,IAAI,SAAS,GAAG,cAAc,CAAA;IAC9B,IAAI,OAAO,GAAG,SAAS,CAAA;IACvB,IAAI,OAAO,GAAG,IAAI,CAAA;IAElB,gBAAgB;IAChB,IAAI,GAAG,YAAY,QAAQ,EAAE,CAAC;QAC5B,UAAU,GAAG,GAAG,CAAC,UAAU,CAAA;QAC3B,SAAS,GAAG,GAAG,CAAC,SAAS,CAAA;QACzB,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;QACrB,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;IACvB,CAAC;IACD,SAAS;SACJ,IAAI,GAAG,YAAY,KAAK,EAAE,CAAC;QAC9B,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;IACvB,CAAC;IAED,SAAS;IACT,eAAM,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,SAAS,KAAK,OAAO,EAAE,EAAE;QACrE,KAAK,EAAE,GAAG;QACV,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC;QACtC,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC;QAChC,OAAO;KACR,CAAC,CAAA;IAEF,SAAS;IACT,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,KAAK,kBACH,IAAI,EAAE,SAAS,EACf,OAAO,IACJ,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC,CAC5B;KACF,CAAC,CAAA;AACJ,CAAC,CAAA;AAEkB,oCAAY"}