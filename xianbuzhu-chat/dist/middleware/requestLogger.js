"use strict";
/**
 * 请求日志中间件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestLogger = void 0;
const logger_1 = require("../utils/logger");
// 请求日志中间件
const requestLogger = (req, res, next) => {
    const start = Date.now();
    const requestId = req.headers['x-request-id'] || Math.random().toString(36).substring(2, 15);
    // 添加请求ID到响应头
    res.setHeader('X-Request-ID', requestId);
    // 记录请求信息
    logger_1.logger.info(`[${req.method}] ${req.path} - 开始处理请求`, {
        requestId,
        method: req.method,
        path: req.path,
        query: req.query,
        body: req.body,
        headers: {
            'user-agent': req.headers['user-agent'],
            'content-type': req.headers['content-type'],
            'accept': req.headers['accept'],
            'x-user-id': req.headers['x-user-id'],
        },
        ip: req.ip
    });
    // 监听响应完成事件
    res.on('finish', () => {
        const duration = Date.now() - start;
        logger_1.logger.info(`[${req.method}] ${req.path} - 请求处理完成`, {
            requestId,
            statusCode: res.statusCode,
            duration: `${duration}ms`
        });
    });
    next();
};
exports.requestLogger = requestLogger;
//# sourceMappingURL=requestLogger.js.map