"use strict";
/**
 * 错误处理中间件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.AppError = void 0;
const logger_1 = require("../utils/logger");
// 自定义错误类
class AppError extends Error {
    constructor(message, statusCode, errorCode, details) {
        super(message);
        this.statusCode = statusCode;
        this.errorCode = errorCode || 'UNKNOWN_ERROR';
        this.details = details;
        this.name = 'AppError';
        // 捕获栈跟踪
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
// 错误处理中间件
const errorHandler = (err, req, res, next) => {
    // 默认错误配置
    let statusCode = 500;
    let errorCode = 'SERVER_ERROR';
    let message = '服务器内部错误';
    let details = null;
    // 处理自定义AppError
    if (err instanceof AppError) {
        statusCode = err.statusCode;
        errorCode = err.errorCode;
        message = err.message;
        details = err.details;
    }
    // 处理其他错误
    else if (err instanceof Error) {
        message = err.message;
    }
    // 记录错误日志
    logger_1.logger.error(`[${req.method}] ${req.path} - ${errorCode}: ${message}`, {
        error: err,
        requestId: req.headers['x-request-id'],
        userId: req.headers['x-user-id'],
        details
    });
    // 返回错误响应
    res.status(statusCode).json({
        success: false,
        error: Object.assign({ code: errorCode, message }, (details && { details }))
    });
};
exports.errorHandler = errorHandler;
//# sourceMappingURL=errorHandler.js.map