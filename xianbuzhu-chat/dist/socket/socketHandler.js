"use strict";
/**
 * Socket处理器
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketHandler = void 0;
const logger_1 = require("../utils/logger");
const errorHandler_1 = require("../middleware/errorHandler");
const jsonwebtoken_1 = require("jsonwebtoken");
const config_1 = require("../config");
class SocketHandler {
    constructor(io, connectionManager) {
        this.io = io;
        this.connectionManager = connectionManager;
    }
    /**
     * 处理Socket连接
     */
    handleConnection(socket) {
        // 验证用户身份
        socket.on('authenticate', (token) => {
            try {
                // 验证JWT令牌
                const decoded = (0, jsonwebtoken_1.verify)(token, config_1.config.SECRET_KEY);
                const userId = decoded.userId;
                // 存储用户连接
                this.connectionManager.addConnection(socket, userId, {
                    userAgent: socket.handshake.headers['user-agent'],
                    ip: socket.handshake.address
                });
                // 发送认证成功响应
                socket.emit('authenticated', {
                    success: true,
                    message: '认证成功',
                    userId
                });
                // 处理后续事件
                this.setupEventListeners(socket, userId);
            }
            catch (error) {
                logger_1.logger.error(`Socket认证失败: ${socket.id}`, { error });
                socket.emit('authentication_error', {
                    success: false,
                    error: {
                        code: 'AUTHENTICATION_FAILED',
                        message: '身份验证失败，请重新登录'
                    }
                });
                socket.disconnect();
            }
        });
        // 处理断开连接
        socket.on('disconnect', () => {
            this.connectionManager.removeConnection(socket.id);
        });
    }
    /**
     * 设置事件监听器
     */
    setupEventListeners(socket, userId) {
        // 处理私聊消息
        socket.on('private_message', (data) => {
            try {
                const { receiverId, content, type } = data;
                // 验证接收者是否存在
                if (!receiverId || receiverId === userId) {
                    throw new errorHandler_1.AppError('无效的接收者', 400, 'INVALID_RECEIVER');
                }
                // 创建消息
                const message = {
                    id: `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
                    senderId: userId,
                    receiverId,
                    content,
                    type,
                    timestamp: new Date(),
                    read: false
                };
                // 发送消息给接收者
                const sent = this.connectionManager.sendToUser(receiverId, 'private_message', message);
                // 发送消息确认给发送者
                socket.emit('message_delivered', {
                    success: true,
                    messageId: message.id,
                    delivered: sent,
                    timestamp: new Date()
                });
                // 记录消息日志
                logger_1.logger.info(`用户 ${userId} 发送消息给用户 ${receiverId}`, { messageId: message.id });
            }
            catch (error) {
                logger_1.logger.error(`处理私聊消息失败`, { error, userId, data });
                socket.emit('error', {
                    success: false,
                    error: {
                        code: 'MESSAGE_PROCESSING_ERROR',
                        message: '消息处理失败'
                    }
                });
            }
        });
        // 处理消息已读
        socket.on('message_read', (data) => {
            try {
                const { messageId, receiverId } = data;
                // 验证接收者是否为当前用户
                if (receiverId !== userId) {
                    throw new errorHandler_1.AppError('无权标记此消息为已读', 403, 'FORBIDDEN');
                }
                // 发送已读通知给发送者
                socket.broadcast.emit('message_read', {
                    messageId,
                    receiverId: userId,
                    timestamp: new Date()
                });
                logger_1.logger.info(`用户 ${userId} 已读消息: ${messageId}`);
            }
            catch (error) {
                logger_1.logger.error(`处理消息已读失败`, { error, userId, data });
            }
        });
        // 处理用户正在输入
        socket.on('typing', (data) => {
            try {
                const { receiverId } = data;
                // 发送正在输入通知给接收者
                this.connectionManager.sendToUser(receiverId, 'typing', {
                    senderId: userId,
                    timestamp: new Date()
                });
            }
            catch (error) {
                logger_1.logger.error(`处理正在输入通知失败`, { error, userId, data });
            }
        });
        // 处理用户停止输入
        socket.on('stop_typing', (data) => {
            try {
                const { receiverId } = data;
                // 发送停止输入通知给接收者
                this.connectionManager.sendToUser(receiverId, 'stop_typing', {
                    senderId: userId,
                    timestamp: new Date()
                });
            }
            catch (error) {
                logger_1.logger.error(`处理停止输入通知失败`, { error, userId, data });
            }
        });
    }
}
exports.SocketHandler = SocketHandler;
//# sourceMappingURL=socketHandler.js.map