{"version": 3, "file": "connectionManager.js", "sourceRoot": "", "sources": ["../../src/socket/connectionManager.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAGH,4CAAwC;AAWxC,MAAM,iBAAiB;IAKrB,YAAY,EAAU;QACpB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAA;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc,EAAE,MAAc,EAAE,QAA8B;;QAC1E,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAA;QAC1B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;QAEtB,SAAS;QACT,MAAM,UAAU,GAAmB;YACjC,MAAM;YACN,QAAQ;YACR,WAAW,EAAE,GAAG;YAChB,YAAY,EAAE,GAAG;YACjB,QAAQ;SACT,CAAA;QAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QAE1C,iBAAiB;QACjB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;QAC3C,CAAC;QACD,MAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,0CAAE,GAAG,CAAC,QAAQ,CAAC,CAAA;QAE7C,eAAM,CAAC,IAAI,CAAC,MAAM,MAAM,WAAW,QAAQ,EAAE,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAgB;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACjD,IAAI,CAAC,UAAU;YAAE,OAAM;QAEvB,mBAAmB;QACnB,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAA;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAClD,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC5B,kBAAkB;YAClB,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;YACnC,CAAC;QACH,CAAC;QAED,WAAW;QACX,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QAEjC,eAAM,CAAC,IAAI,CAAC,MAAM,MAAM,UAAU,QAAQ,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,QAAgB;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAAc,EAAE,KAAa,EAAE,IAAS;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC9C,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,KAAK,CAAA;QACd,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACzB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QACxC,CAAC,CAAC,CAAA;QAEF,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAEQ,8CAAiB"}