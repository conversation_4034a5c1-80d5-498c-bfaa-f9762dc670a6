"use strict";
/**
 * Socket连接管理器
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionManager = void 0;
const logger_1 = require("../utils/logger");
class ConnectionManager {
    constructor(io) {
        this.io = io;
        this.connections = new Map();
        this.userToSockets = new Map();
    }
    /**
     * 添加用户连接
     */
    addConnection(socket, userId, metadata) {
        var _a;
        const socketId = socket.id;
        const now = new Date();
        // 存储连接信息
        const connection = {
            userId,
            socketId,
            connectedAt: now,
            lastActiveAt: now,
            metadata
        };
        this.connections.set(socketId, connection);
        // 更新用户到socket的映射
        if (!this.userToSockets.has(userId)) {
            this.userToSockets.set(userId, new Set());
        }
        (_a = this.userToSockets.get(userId)) === null || _a === void 0 ? void 0 : _a.add(socketId);
        logger_1.logger.info(`用户 ${userId} 建立新连接: ${socketId}`);
    }
    /**
     * 移除用户连接
     */
    removeConnection(socketId) {
        const connection = this.connections.get(socketId);
        if (!connection)
            return;
        // 从用户到socket的映射中移除
        const { userId } = connection;
        const userSockets = this.userToSockets.get(userId);
        if (userSockets) {
            userSockets.delete(socketId);
            // 如果用户没有连接了，移除该用户
            if (userSockets.size === 0) {
                this.userToSockets.delete(userId);
            }
        }
        // 从连接映射中移除
        this.connections.delete(socketId);
        logger_1.logger.info(`用户 ${userId} 断开连接: ${socketId}`);
    }
    /**
     * 获取用户的所有socket连接
     */
    getUserSockets(userId) {
        return this.userToSockets.get(userId);
    }
    /**
     * 获取连接信息
     */
    getConnection(socketId) {
        return this.connections.get(socketId);
    }
    /**
     * 获取所有连接数
     */
    getConnectionsCount() {
        return this.connections.size;
    }
    /**
     * 获取所有用户数
     */
    getUsersCount() {
        return this.userToSockets.size;
    }
    /**
     * 向指定用户发送消息
     */
    sendToUser(userId, event, data) {
        const sockets = this.userToSockets.get(userId);
        if (!sockets || sockets.size === 0) {
            return false;
        }
        sockets.forEach(socketId => {
            this.io.to(socketId).emit(event, data);
        });
        return true;
    }
}
exports.ConnectionManager = ConnectionManager;
//# sourceMappingURL=connectionManager.js.map