{"version": 3, "file": "socketHandler.js", "sourceRoot": "", "sources": ["../../src/socket/socketHandler.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAIH,4CAAwC;AACxC,6DAAqD;AACrD,+CAAqC;AACrC,sCAAkC;AAalC,MAAM,aAAa;IAIjB,YAAY,EAAU,EAAE,iBAAoC;QAC1D,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc;QAC7B,SAAS;QACT,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,KAAa,EAAE,EAAE;YAC1C,IAAI,CAAC;gBACH,UAAU;gBACV,MAAM,OAAO,GAAG,IAAA,qBAAM,EAAC,KAAK,EAAE,eAAM,CAAC,UAAU,CAA2C,CAAA;gBAC1F,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;gBAE7B,SAAS;gBACT,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE;oBACnD,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC;oBACjD,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO;iBAC7B,CAAC,CAAA;gBAEF,WAAW;gBACX,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;oBAC3B,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,MAAM;oBACf,MAAM;iBACP,CAAC,CAAA;gBAEF,SAAS;gBACT,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAE1C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,eAAe,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;gBACnD,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;oBAClC,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,uBAAuB;wBAC7B,OAAO,EAAE,cAAc;qBACxB;iBACF,CAAC,CAAA;gBACF,MAAM,CAAC,UAAU,EAAE,CAAA;YACrB,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,SAAS;QACT,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QACpD,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,MAAc,EAAE,MAAc;QACxD,SAAS;QACT,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,IAAkG,EAAE,EAAE;YAClI,IAAI,CAAC;gBACH,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI,CAAA;gBAE1C,YAAY;gBACZ,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;oBACzC,MAAM,IAAI,uBAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAA;gBACvD,CAAC;gBAED,OAAO;gBACP,MAAM,OAAO,GAAgB;oBAC3B,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;oBAC3D,QAAQ,EAAE,MAAM;oBAChB,UAAU;oBACV,OAAO;oBACP,IAAI;oBACJ,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,IAAI,EAAE,KAAK;iBACZ,CAAA;gBAED,WAAW;gBACX,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAA;gBAEtF,aAAa;gBACb,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBAC/B,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAA;gBAEF,SAAS;gBACT,eAAM,CAAC,IAAI,CAAC,MAAM,MAAM,YAAY,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAA;YAE9E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;gBACjD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,0BAA0B;wBAChC,OAAO,EAAE,QAAQ;qBAClB;iBACF,CAAC,CAAA;YACJ,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,SAAS;QACT,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAA+C,EAAE,EAAE;YAC5E,IAAI,CAAC;gBACH,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,IAAI,CAAA;gBAEtC,eAAe;gBACf,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;oBAC1B,MAAM,IAAI,uBAAQ,CAAC,YAAY,EAAE,GAAG,EAAE,WAAW,CAAC,CAAA;gBACpD,CAAC;gBAED,aAAa;gBACb,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE;oBACpC,SAAS;oBACT,UAAU,EAAE,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAA;gBAEF,eAAM,CAAC,IAAI,CAAC,MAAM,MAAM,UAAU,SAAS,EAAE,CAAC,CAAA;YAEhD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;YACnD,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,WAAW;QACX,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAA4B,EAAE,EAAE;YACnD,IAAI,CAAC;gBACH,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAA;gBAE3B,eAAe;gBACf,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,EAAE;oBACtD,QAAQ,EAAE,MAAM;oBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAA;YAEJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;YACrD,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,WAAW;QACX,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAA4B,EAAE,EAAE;YACxD,IAAI,CAAC;gBACH,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAA;gBAE3B,eAAe;gBACf,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,EAAE,aAAa,EAAE;oBAC3D,QAAQ,EAAE,MAAM;oBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAA;YAEJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;YACrD,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AAEQ,sCAAa"}