"use strict";
/**
 * 聊天相关请求验证器
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendMessageSchema = exports.getConversationSchema = void 0;
const yup_1 = require("yup");
// 获取对话列表验证器
const getConversationSchema = (0, yup_1.object)({
    query: (0, yup_1.object)({
        userId: (0, yup_1.string)()
            .required('用户ID不能为空')
            .matches(/^user_\d+$/, '用户ID格式不正确'),
        page: (0, yup_1.number)()
            .optional()
            .integer('页码必须是整数')
            .min(1, '页码必须大于等于1'),
        pageSize: (0, yup_1.number)()
            .optional()
            .integer('每页数量必须是整数')
            .min(1, '每页数量必须大于等于1')
            .max(100, '每页数量不能超过100')
    })
});
exports.getConversationSchema = getConversationSchema;
// 发送消息验证器
const sendMessageSchema = (0, yup_1.object)({
    body: (0, yup_1.object)({
        receiverId: (0, yup_1.string)()
            .required('接收者ID不能为空')
            .matches(/^user_\d+$/, '接收者ID格式不正确'),
        content: (0, yup_1.string)()
            .required('消息内容不能为空')
            .min(1, '消息内容不能少于1个字符')
            .max(2000, '消息内容不能超过2000个字符'),
        type: (0, yup_1.string)()
            .required('消息类型不能为空')
            .oneOf(['text', 'image', 'voice', 'video', 'file'], '消息类型无效')
    })
});
exports.sendMessageSchema = sendMessageSchema;
//# sourceMappingURL=chatValidators.js.map