"use strict";
/**
 * 聊天服务主入口文件
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = exports.server = exports.app = void 0;
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const errorHandler_1 = require("./middleware/errorHandler");
const requestLogger_1 = require("./middleware/requestLogger");
const connectionManager_1 = require("./socket/connectionManager");
const socketHandler_1 = require("./socket/socketHandler");
// 路由导入
const chat_1 = __importDefault(require("./routes/chat"));
const health_1 = __importDefault(require("./routes/health"));
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
exports.server = server;
// Socket.io配置
const io = new socket_io_1.Server(server, {
    cors: {
        origin: ((_a = config_1.config.ALLOWED_ORIGINS) === null || _a === void 0 ? void 0 : _a.split(',')) || ['http://localhost:3000'],
        methods: ['GET', 'POST'],
        credentials: true
    },
    transports: ['websocket', 'polling']
});
exports.io = io;
// 基础中间件
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: ((_b = config_1.config.ALLOWED_ORIGINS) === null || _b === void 0 ? void 0 : _b.split(',')) || ['http://localhost:3000'],
    credentials: true
}));
// 请求限制
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 500, // 限制每个IP 15分钟内最多500个请求
    message: {
        success: false,
        error: {
            code: 'RATE_LIMIT_ERROR',
            message: '请求过于频繁，请稍后再试'
        }
    }
});
app.use('/api/', limiter);
// 解析中间件
app.use(express_1.default.json({ limit: '1mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '1mb' }));
// 日志中间件
app.use(requestLogger_1.requestLogger);
// 路由注册
app.use('/api/chat', chat_1.default);
app.use('/api/health', health_1.default);
// 根路径
app.get('/', (req, res) => {
    res.json({
        name: 'Community Exchange Chat Service',
        version: '1.0.0',
        status: 'running',
        connections: io.engine.clientsCount,
        timestamp: new Date().toISOString()
    });
});
// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            code: 'NOT_FOUND_ERROR',
            message: `路径 ${req.originalUrl} 不存在`
        }
    });
});
// 错误处理中间件
app.use(errorHandler_1.errorHandler);
// 初始化Socket连接管理器
const connectionManager = new connectionManager_1.ConnectionManager(io);
const socketHandler = new socketHandler_1.SocketHandler(io, connectionManager);
// Socket.io连接处理
io.on('connection', (socket) => {
    logger_1.logger.info(`新的Socket连接: ${socket.id}`);
    // 处理连接
    socketHandler.handleConnection(socket);
    // 处理断开连接
    socket.on('disconnect', (reason) => {
        logger_1.logger.info(`Socket断开连接: ${socket.id}, 原因: ${reason}`);
        connectionManager.removeConnection(socket.id);
    });
});
// 启动服务器
const PORT = config_1.config.PORT || 3001;
server.listen(PORT, () => {
    logger_1.logger.info(`聊天服务启动成功，端口: ${PORT}`);
    logger_1.logger.info(`环境: ${config_1.config.NODE_ENV}`);
    logger_1.logger.info(`Redis: ${config_1.config.REDIS_URL ? '已配置' : '未配置'}`);
    logger_1.logger.info(`WebSocket服务已启动`);
});
// 优雅关闭
process.on('SIGTERM', () => {
    logger_1.logger.info('收到SIGTERM信号，开始优雅关闭...');
    io.close(() => {
        logger_1.logger.info('Socket.io服务已关闭');
        server.close(() => {
            logger_1.logger.info('HTTP服务已关闭');
            process.exit(0);
        });
    });
});
process.on('SIGINT', () => {
    logger_1.logger.info('收到SIGINT信号，开始优雅关闭...');
    io.close(() => {
        logger_1.logger.info('Socket.io服务已关闭');
        server.close(() => {
            logger_1.logger.info('HTTP服务已关闭');
            process.exit(0);
        });
    });
});
//# sourceMappingURL=app.js.map