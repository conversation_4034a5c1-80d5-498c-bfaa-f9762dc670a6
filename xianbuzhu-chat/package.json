{"name": "xianbuzhu-chat", "version": "1.0.0", "description": "社区闲置物品置换平台聊天服务", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "docker:build": "docker build -t xianbuzhu-chat .", "docker:run": "docker run -p 3001:3001 xianbuzhu-chat"}, "keywords": ["nodejs", "socket.io", "websocket", "chat", "realtime", "typescript"], "author": "Community Exchange Team", "license": "MIT", "dependencies": {"@community-exchange/shared": "^1.0.0", "express": "^4.18.2", "socket.io": "^4.6.2", "cors": "^2.8.5", "helmet": "^6.1.5", "express-rate-limit": "^6.7.0", "jsonwebtoken": "^9.0.0", "joi": "^17.9.2", "winston": "^3.8.2", "redis": "^4.6.7", "@prisma/client": "^4.15.0", "prisma": "^4.15.0", "axios": "^1.4.0", "prom-client": "^14.2.0", "dotenv": "^16.1.4"}, "devDependencies": {"@types/node": "^20.2.5", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/jsonwebtoken": "^9.0.2", "@types/joi": "^17.2.3", "@types/jest": "^29.5.2", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "eslint": "^8.41.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "nodemon": "^2.0.22", "supertest": "^6.3.3", "socket.io-client": "^4.6.2", "typescript": "^5.1.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}