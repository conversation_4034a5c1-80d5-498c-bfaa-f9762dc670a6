/**
 * Socket处理器
 */

import { Server, Socket } from 'socket.io'
import { ConnectionManager } from './connectionManager'
import { logger } from '../utils/logger'
import { AppError } from '../middleware/errorHandler'
import { verify } from 'jsonwebtoken'
import { config } from '../config'

// 聊天消息接口
interface ChatMessage {
  id: string
  senderId: string
  receiverId: string
  content: string
  type: 'text' | 'image' | 'voice' | 'video' | 'file'
  timestamp: Date
  read: boolean
}

class SocketHandler {
  private io: Server
  private connectionManager: ConnectionManager

  constructor(io: Server, connectionManager: ConnectionManager) {
    this.io = io
    this.connectionManager = connectionManager
  }

  /**
   * 处理Socket连接
   */
  handleConnection(socket: Socket): void {
    // 验证用户身份
    socket.on('authenticate', (token: string) => {
      try {
        // 验证JWT令牌
        const decoded = verify(token, config.SECRET_KEY) as { userId: string, [key: string]: any }
        const userId = decoded.userId

        // 存储用户连接
        this.connectionManager.addConnection(socket, userId, {
          userAgent: socket.handshake.headers['user-agent'],
          ip: socket.handshake.address
        })

        // 发送认证成功响应
        socket.emit('authenticated', {
          success: true,
          message: '认证成功',
          userId
        })

        // 处理后续事件
        this.setupEventListeners(socket, userId)

      } catch (error) {
        logger.error(`Socket认证失败: ${socket.id}`, { error })
        socket.emit('authentication_error', {
          success: false,
          error: {
            code: 'AUTHENTICATION_FAILED',
            message: '身份验证失败，请重新登录'
          }
        })
        socket.disconnect()
      }
    })

    // 处理断开连接
    socket.on('disconnect', () => {
      this.connectionManager.removeConnection(socket.id)
    })
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(socket: Socket, userId: string): void {
    // 处理私聊消息
    socket.on('private_message', (data: { receiverId: string, content: string, type: 'text' | 'image' | 'voice' | 'video' | 'file' }) => {
      try {
        const { receiverId, content, type } = data

        // 验证接收者是否存在
        if (!receiverId || receiverId === userId) {
          throw new AppError('无效的接收者', 400, 'INVALID_RECEIVER')
        }

        // 创建消息
        const message: ChatMessage = {
          id: `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
          senderId: userId,
          receiverId,
          content,
          type,
          timestamp: new Date(),
          read: false
        }

        // 发送消息给接收者
        const sent = this.connectionManager.sendToUser(receiverId, 'private_message', message)

        // 发送消息确认给发送者
        socket.emit('message_delivered', {
          success: true,
          messageId: message.id,
          delivered: sent,
          timestamp: new Date()
        })

        // 记录消息日志
        logger.info(`用户 ${userId} 发送消息给用户 ${receiverId}`, { messageId: message.id })

      } catch (error) {
        logger.error(`处理私聊消息失败`, { error, userId, data })
        socket.emit('error', {
          success: false,
          error: {
            code: 'MESSAGE_PROCESSING_ERROR',
            message: '消息处理失败'
          }
        })
      }
    })

    // 处理消息已读
    socket.on('message_read', (data: { messageId: string, receiverId: string }) => {
      try {
        const { messageId, receiverId } = data

        // 验证接收者是否为当前用户
        if (receiverId !== userId) {
          throw new AppError('无权标记此消息为已读', 403, 'FORBIDDEN')
        }

        // 发送已读通知给发送者
        socket.broadcast.emit('message_read', {
          messageId,
          receiverId: userId,
          timestamp: new Date()
        })

        logger.info(`用户 ${userId} 已读消息: ${messageId}`)

      } catch (error) {
        logger.error(`处理消息已读失败`, { error, userId, data })
      }
    })

    // 处理用户正在输入
    socket.on('typing', (data: { receiverId: string }) => {
      try {
        const { receiverId } = data

        // 发送正在输入通知给接收者
        this.connectionManager.sendToUser(receiverId, 'typing', {
          senderId: userId,
          timestamp: new Date()
        })

      } catch (error) {
        logger.error(`处理正在输入通知失败`, { error, userId, data })
      }
    })

    // 处理用户停止输入
    socket.on('stop_typing', (data: { receiverId: string }) => {
      try {
        const { receiverId } = data

        // 发送停止输入通知给接收者
        this.connectionManager.sendToUser(receiverId, 'stop_typing', {
          senderId: userId,
          timestamp: new Date()
        })

      } catch (error) {
        logger.error(`处理停止输入通知失败`, { error, userId, data })
      }
    })
  }
}

export { SocketHandler }