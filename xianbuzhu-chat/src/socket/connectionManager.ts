/**
 * Socket连接管理器
 */

import { Server, Socket } from 'socket.io'
import { logger } from '../utils/logger'

// 用户连接信息接口
interface UserConnection {
  userId: string
  socketId: string
  connectedAt: Date
  lastActiveAt: Date
  metadata?: Record<string, any>
}

class ConnectionManager {
  private io: Server
  private connections: Map<string, UserConnection> // socketId -> UserConnection
  private userToSockets: Map<string, Set<string>> // userId -> Set<socketId>

  constructor(io: Server) {
    this.io = io
    this.connections = new Map()
    this.userToSockets = new Map()
  }

  /**
   * 添加用户连接
   */
  addConnection(socket: Socket, userId: string, metadata?: Record<string, any>): void {
    const socketId = socket.id
    const now = new Date()

    // 存储连接信息
    const connection: UserConnection = {
      userId,
      socketId,
      connectedAt: now,
      lastActiveAt: now,
      metadata
    }

    this.connections.set(socketId, connection)

    // 更新用户到socket的映射
    if (!this.userToSockets.has(userId)) {
      this.userToSockets.set(userId, new Set())
    }
    this.userToSockets.get(userId)?.add(socketId)

    logger.info(`用户 ${userId} 建立新连接: ${socketId}`)
  }

  /**
   * 移除用户连接
   */
  removeConnection(socketId: string): void {
    const connection = this.connections.get(socketId)
    if (!connection) return

    // 从用户到socket的映射中移除
    const { userId } = connection
    const userSockets = this.userToSockets.get(userId)
    if (userSockets) {
      userSockets.delete(socketId)
      // 如果用户没有连接了，移除该用户
      if (userSockets.size === 0) {
        this.userToSockets.delete(userId)
      }
    }

    // 从连接映射中移除
    this.connections.delete(socketId)

    logger.info(`用户 ${userId} 断开连接: ${socketId}`)
  }

  /**
   * 获取用户的所有socket连接
   */
  getUserSockets(userId: string): Set<string> | undefined {
    return this.userToSockets.get(userId)
  }

  /**
   * 获取连接信息
   */
  getConnection(socketId: string): UserConnection | undefined {
    return this.connections.get(socketId)
  }

  /**
   * 获取所有连接数
   */
  getConnectionsCount(): number {
    return this.connections.size
  }

  /**
   * 获取所有用户数
   */
  getUsersCount(): number {
    return this.userToSockets.size
  }

  /**
   * 向指定用户发送消息
   */
  sendToUser(userId: string, event: string, data: any): boolean {
    const sockets = this.userToSockets.get(userId)
    if (!sockets || sockets.size === 0) {
      return false
    }

    sockets.forEach(socketId => {
      this.io.to(socketId).emit(event, data)
    })

    return true
  }
}

export { ConnectionManager }