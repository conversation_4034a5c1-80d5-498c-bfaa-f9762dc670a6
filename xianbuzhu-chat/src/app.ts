/**
 * 聊天服务主入口文件
 */

import express from 'express'
import { createServer } from 'http'
import { Server } from 'socket.io'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import { config } from './config'
import { logger } from './utils/logger'
import { errorHandler } from './middleware/errorHandler'
import { requestLogger } from './middleware/requestLogger'
import { ConnectionManager } from './socket/connectionManager'
import { SocketHandler } from './socket/socketHandler'

// 路由导入
import chatRoutes from './routes/chat'
import healthRoutes from './routes/health'

const app = express()
const server = createServer(app)

// Socket.io配置
const io = new Server(server, {
  cors: {
    origin: config.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling']
})

// 基础中间件
app.use(helmet())
app.use(cors({
  origin: config.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}))

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 500, // 限制每个IP 15分钟内最多500个请求
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_ERROR',
      message: '请求过于频繁，请稍后再试'
    }
  }
})
app.use('/api/', limiter)

// 解析中间件
app.use(express.json({ limit: '1mb' }))
app.use(express.urlencoded({ extended: true, limit: '1mb' }))

// 日志中间件
app.use(requestLogger)

// 路由注册
app.use('/api/chat', chatRoutes)
app.use('/api/health', healthRoutes)

// 根路径
app.get('/', (req, res) => {
  res.json({
    name: 'Community Exchange Chat Service',
    version: '1.0.0',
    status: 'running',
    connections: io.engine.clientsCount,
    timestamp: new Date().toISOString()
  })
})

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND_ERROR',
      message: `路径 ${req.originalUrl} 不存在`
    }
  })
})

// 错误处理中间件
app.use(errorHandler)

// 初始化Socket连接管理器
const connectionManager = new ConnectionManager(io)
const socketHandler = new SocketHandler(io, connectionManager)

// Socket.io连接处理
io.on('connection', (socket) => {
  logger.info(`新的Socket连接: ${socket.id}`)
  
  // 处理连接
  socketHandler.handleConnection(socket)
  
  // 处理断开连接
  socket.on('disconnect', (reason) => {
    logger.info(`Socket断开连接: ${socket.id}, 原因: ${reason}`)
    connectionManager.removeConnection(socket.id)
  })
})

// 启动服务器
const PORT = config.PORT || 3001

server.listen(PORT, () => {
  logger.info(`聊天服务启动成功，端口: ${PORT}`)
  logger.info(`环境: ${config.NODE_ENV}`)
  logger.info(`Redis: ${config.REDIS_URL ? '已配置' : '未配置'}`)
  logger.info(`WebSocket服务已启动`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...')
  io.close(() => {
    logger.info('Socket.io服务已关闭')
    server.close(() => {
      logger.info('HTTP服务已关闭')
      process.exit(0)
    })
  })
})

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...')
  io.close(() => {
    logger.info('Socket.io服务已关闭')
    server.close(() => {
      logger.info('HTTP服务已关闭')
      process.exit(0)
    })
  })
})

export { app, server, io }