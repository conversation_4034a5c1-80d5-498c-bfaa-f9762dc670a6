/**
 * 请求验证中间件
 */

import { Request, Response, NextFunction } from 'express'
import { AnySchema } from 'yup'
import { AppError } from './errorHandler'
import { logger } from '../utils/logger'

/**
 * 验证请求中间件
 * @param schema Yup验证模式
 * @returns Express中间件函数
 */
const validateRequest = (schema: AnySchema) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 验证请求
      await schema.validate({
        body: req.body,
        query: req.query,
        params: req.params
      }, {
        abortEarly: false, // 收集所有错误
        stripUnknown: true // 删除未知字段
      })

      // 验证通过，继续处理请求
      next()
    } catch (error: any) {
      // 处理验证错误
      if (error.name === 'ValidationError') {
        const errors = error.inner.map((err: any) => ({
          field: err.path,
          message: err.message
        }))

        logger.warn('请求验证失败', { errors, path: req.path })

        next(new AppError('请求参数无效', 400, 'INVALID_PARAMETERS', errors))
      } else {
        // 其他错误
        next(error)
      }
    }
  }
}

export { validateRequest }