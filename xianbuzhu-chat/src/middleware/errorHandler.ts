/**
 * 错误处理中间件
 */

import { Request, Response, NextFunction } from 'express'
import { logger } from '../utils/logger'

// 自定义错误类
class AppError extends Error {
  statusCode: number
  errorCode: string
  details?: any

  constructor(message: string, statusCode: number, errorCode?: string, details?: any) {
    super(message)
    this.statusCode = statusCode
    this.errorCode = errorCode || 'UNKNOWN_ERROR'
    this.details = details
    this.name = 'AppError'

    // 捕获栈跟踪
    Error.captureStackTrace(this, this.constructor)
  }
}

// 错误处理中间件
const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  // 默认错误配置
  let statusCode = 500
  let errorCode = 'SERVER_ERROR'
  let message = '服务器内部错误'
  let details = null

  // 处理自定义AppError
  if (err instanceof AppError) {
    statusCode = err.statusCode
    errorCode = err.errorCode
    message = err.message
    details = err.details
  } 
  // 处理其他错误
  else if (err instanceof Error) {
    message = err.message
  }

  // 记录错误日志
  logger.error(`[${req.method}] ${req.path} - ${errorCode}: ${message}`, {
    error: err,
    requestId: req.headers['x-request-id'],
    userId: req.headers['x-user-id'],
    details
  })

  // 返回错误响应
  res.status(statusCode).json({
    success: false,
    error: {
      code: errorCode,
      message,
      ...(details && { details })
    }
  })
}

export { AppError, errorHandler }