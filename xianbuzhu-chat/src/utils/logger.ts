/**
 * 日志工具
 */

import winston from 'winston'
import { config } from '../config'

// 定义日志级别
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  verbose: 4,
  debug: 5,
  silly: 6
}

// 定义日志格式
const baseFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return `[${timestamp}] ${level.toUpperCase()}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`
  })
)

// 控制台日志格式（带颜色）
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  baseFormat
)

// 定义日志传输
const transports: winston.transport[] = [
  new winston.transports.Console({
    format: consoleFormat,
    level: config.LOG_LEVEL || 'info'
  })
]

// 如果是生产环境，添加文件传输
if (config.NODE_ENV === 'production') {
  transports.push(
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: baseFormat
    }),
    new winston.transports.File({
      filename: 'logs/combined.log',
      format: baseFormat
    })
  )
}

// 创建logger实例
const loggerOptions = {
  levels,
  transports
}

const logger = winston.createLogger(loggerOptions)

export { logger }