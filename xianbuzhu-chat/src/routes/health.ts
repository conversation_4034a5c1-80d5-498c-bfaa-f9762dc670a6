/**
 * 健康检查路由
 */

import { Router } from 'express'
import { config } from '../config'
import { logger } from '../utils/logger'

const router = Router()

// 基本健康检查
router.get('/', (req, res) => {
  res.json({
    success: true,
    status: 'UP',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: config.NODE_ENV
  })
})

// 详细健康检查
router.get('/detail', async (req, res) => {
  try {
    // 这里可以添加数据库连接检查、Redis连接检查等
    const healthStatus = {
      success: true,
      status: 'UP',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: config.NODE_ENV,
      services: {
        redis: 'CONNECTED', // 模拟Redis连接状态
        apiGateway: 'CONNECTED' // 模拟API网关连接状态
      }
    }

    res.json(healthStatus)
  } catch (error) {
    logger.error('健康检查失败', { error })
    res.status(503).json({
      success: false,
      status: 'DOWN',
      error: { message: '服务不可用' },
      timestamp: new Date().toISOString()
    })
  }
})

export default router