/**
 * 聊天API路由
 */

import { Router } from 'express'
import { logger } from '../utils/logger'
import { AppError } from '../middleware/errorHandler'
import { validateRequest } from '../middleware/validation'
import { sendMessageSchema, getConversationSchema } from '../validators/chatValidators'

const router = Router()

// 获取对话列表
router.get('/conversations', validateRequest(getConversationSchema), (req, res, next) => {
  try {
    const { userId } = req.query
    logger.info(`获取用户对话列表: ${userId}`)

    // 模拟数据 - 实际项目中应从数据库获取
    const conversations = [
      {
        id: 'conv_1',
        participants: [userId as string, 'user_2'],
        lastMessage: {
          id: 'msg_123',
          content: '你好，我对您发布的物品很感兴趣',
          senderId: 'user_2',
          timestamp: new Date('2023-06-15T10:30:00Z'),
          read: false
        },
        unreadCount: 1,
        updatedAt: new Date('2023-06-15T10:30:00Z')
      },
      {
        id: 'conv_2',
        participants: [userId as string, 'user_3'],
        lastMessage: {
          id: 'msg_456',
          content: '请问这个物品还在吗？',
          senderId: 'user_3',
          timestamp: new Date('2023-06-14T15:45:00Z'),
          read: true
        },
        unreadCount: 0,
        updatedAt: new Date('2023-06-14T15:45:00Z')
      }
    ]

    res.json({
      success: true,
      data: conversations
    })
  } catch (error) {
    next(error)
  }
})

// 获取对话消息
router.get('/conversations/:conversationId/messages', (req, res, next) => {
  try {
    const { conversationId } = req.params
    const { page = 1, pageSize = 20 } = req.query
    logger.info(`获取对话消息: ${conversationId}, 分页: ${page}/${pageSize}`)

    // 模拟数据 - 实际项目中应从数据库获取
    const messages = [
      {
        id: 'msg_123',
        senderId: 'user_2',
        receiverId: req.query.userId as string,
        content: '你好，我对您发布的物品很感兴趣',
        type: 'text',
        timestamp: new Date('2023-06-15T10:30:00Z'),
        read: false
      },
      {
        id: 'msg_456',
        senderId: req.query.userId as string,
        receiverId: 'user_2',
        content: '是的，还在的。您什么时候方便看一下？',
        type: 'text',
        timestamp: new Date('2023-06-15T10:25:00Z'),
        read: true
      }
    ]

    res.json({
      success: true,
      data: {
        messages,
        total: 2,
        page: Number(page),
        pageSize: Number(pageSize),
        hasMore: false
      }
    })
  } catch (error) {
    next(error)
  }
})

// 发送消息
router.post('/messages', validateRequest(sendMessageSchema), (req, res, next) => {
  try {
    const { receiverId, content, type } = req.body
    const senderId = req.headers['x-user-id'] as string

    if (!senderId) {
      throw new AppError('未授权访问', 401, 'UNAUTHORIZED')
    }

    logger.info(`用户 ${senderId} 发送消息给 ${receiverId}`)

    // 模拟消息发送 - 实际项目中应通过Socket.io发送
    const messageId = `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`

    res.json({
      success: true,
      data: {
        id: messageId,
        senderId,
        receiverId,
        content,
        type,
        timestamp: new Date(),
        read: false
      }
    })
  } catch (error) {
    next(error)
  }
})

// 标记消息已读
router.put('/messages/:messageId/read', (req, res, next) => {
  try {
    const { messageId } = req.params
    const userId = req.headers['x-user-id'] as string

    if (!userId) {
      throw new AppError('未授权访问', 401, 'UNAUTHORIZED')
    }

    logger.info(`用户 ${userId} 标记消息已读: ${messageId}`)

    res.json({
      success: true,
      data: {
        messageId,
        read: true,
        updatedAt: new Date()
      }
    })
  } catch (error) {
    next(error)
  }
})

export default router