/* 全局样式 */
@import '/variable.less';

/* TDesign图标字体网络错误抑制 - 温和方案 */
/* 注意：保持TDesign图标正常工作，只是抑制网络错误信息 */

/* 可选：如果图标无法加载，提供Unicode字符作为fallback */
.t-icon--chevron-left::before {
  content: attr(data-fallback, '‹');
}

.t-icon--chevron-right::before {
  content: attr(data-fallback, '›');
}

.t-icon--heart::before {
  content: attr(data-fallback, '♡');
}

.t-icon--heart-filled::before {
  content: attr(data-fallback, '♥');
}

.t-icon--star::before {
  content: attr(data-fallback, '☆');
}

.t-icon--star-filled::before {
  content: attr(data-fallback, '★');
}

/* 只在图标字体加载失败时生效的备用方案 */
@supports not (font-family: t) {
  .t-icon::before {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

/* 全局基础样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-center {
  text-align: center;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
