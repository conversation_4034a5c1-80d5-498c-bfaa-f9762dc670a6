# 🔍 JavaScript错误快速排查清单

## 1. 🚨 语法错误检查

### ✅ 已修复
- [x] ES6 Import语法 → 改为require
- [x] 页面重复定义 → app.json配置修复

### 🔍 需要检查的常见问题

#### JavaScript语法
```bash
# 在微信开发者工具控制台查看：
1. SyntaxError: 语法错误
2. ReferenceError: 变量未定义
3. TypeError: 类型错误
4. 异步函数错误处理
```

#### 小程序API使用
```javascript
// ❌ 错误用法
wx.navigateTo({
  url: '/pages/xxx'
});

// ✅ 正确用法
wx.navigateTo({
  url: '/pages/xxx',
  fail: (err) => {
    console.error('跳转失败:', err);
  }
});
```

#### TDesign组件使用
```json
// 检查组件路径是否正确
{
  "usingComponents": {
    "t-button": "tdesign-miniprogram/button/button"
  }
}
```

## 2. 🛠️ 快速修复方法

### 方法1：使用微信开发者工具调试
```bash
1. 打开微信开发者工具
2. 点击"调试器"或按F12
3. 查看Console面板的错误信息
4. 点击错误信息可直接跳转到错误位置
```

### 方法2：使用错误检查脚本
```bash
# 在小程序根目录运行
node scripts/check-errors.js
```

### 方法3：分步骤排查
```bash
1. 检查app.json配置
2. 检查页面JSON配置
3. 检查JavaScript语法
4. 检查WXML模板语法
5. 检查组件引用
```

## 3. 🎯 常见错误类型及解决方案

### 错误类型1：模块引用错误
```javascript
// ❌ 错误
import Message from 'tdesign-miniprogram/message/index';

// ✅ 正确
const Message = require('tdesign-miniprogram/message/index');
```

### 错误类型2：页面路径错误
```javascript
// 检查页面是否在app.json中正确配置
// 检查分包页面路径是否正确
```

### 错误类型3：组件属性错误
```xml
<!-- 检查组件属性是否正确 -->
<t-button theme="primary" size="large">按钮</t-button>
```

### 错误类型4：数据绑定错误
```xml
<!-- ❌ 可能的问题 -->
<view>{{item.user.profile.name}}</view>

<!-- ✅ 安全的写法 -->
<view>{{item.user && item.user.profile ? item.user.profile.name : ''}}</view>
```

### 错误类型5：事件处理错误
```javascript
// 确保事件处理函数存在
onButtonTap() {
  // 处理逻辑
}
```

## 4. 🔧 调试技巧

### 技巧1：使用console.log调试
```javascript
// 在关键位置添加日志
console.log('数据:', this.data);
console.log('参数:', e.detail);
```

### 技巧2：使用try-catch捕获错误
```javascript
async loadData() {
  try {
    const result = await api.getData();
    this.setData({ data: result });
  } catch (error) {
    console.error('加载数据失败:', error);
    // 显示错误提示
  }
}
```

### 技巧3：检查网络请求
```javascript
// 在Network面板查看API请求
// 检查请求URL、参数、响应
```

### 技巧4：使用断点调试
```javascript
// 在Sources面板设置断点
// 逐步执行代码查看变量值
```

## 5. 📱 小程序特有问题

### 问题1：生命周期函数
```javascript
Page({
  onLoad() {
    // 页面加载时执行
  },
  onReady() {
    // 页面初次渲染完成
  },
  onShow() {
    // 页面显示时执行
  }
});
```

### 问题2：数据更新
```javascript
// ❌ 错误：直接修改data
this.data.list.push(newItem);

// ✅ 正确：使用setData
this.setData({
  list: [...this.data.list, newItem]
});
```

### 问题3：组件通信
```javascript
// 父组件向子组件传递数据
// 子组件向父组件传递事件
```

## 6. 🚀 性能优化建议

### 建议1：减少setData调用
```javascript
// ❌ 多次调用
this.setData({ a: 1 });
this.setData({ b: 2 });

// ✅ 合并调用
this.setData({ a: 1, b: 2 });
```

### 建议2：避免频繁的数据绑定
```xml
<!-- 避免在循环中使用复杂表达式 -->
```

### 建议3：合理使用分包
```json
// 将非核心页面放入分包
"subpackages": [...]
```

## 7. 📞 获取帮助

### 官方文档
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/)
- [TDesign小程序组件库](https://tdesign.tencent.com/miniprogram/)

### 调试工具
- 微信开发者工具调试器
- 真机调试
- 性能监控

### 社区支持
- 微信开发者社区
- TDesign GitHub Issues
- Stack Overflow

---

💡 **提示**: 遇到错误时，首先查看微信开发者工具的控制台，大部分问题都会有明确的错误提示和行号。
