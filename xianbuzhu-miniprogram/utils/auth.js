/**
 * 用户认证相关工具函数
 */

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const token = wx.getStorageSync('access_token');
  const userInfo = wx.getStorageSync('user_info');
  return !!(token && userInfo);
}

/**
 * 获取用户信息
 * @returns {object|null} 用户信息
 */
function getUserInfo() {
  return wx.getStorageSync('user_info') || null;
}

/**
 * 获取访问令牌
 * @returns {string|null} 访问令牌
 */
function getAccessToken() {
  return wx.getStorageSync('access_token') || null;
}

/**
 * 清除登录信息
 */
function clearAuth() {
  wx.removeStorageSync('access_token');
  wx.removeStorageSync('user_info');
}

/**
 * 需要登录的页面列表
 */
const PROTECTED_PAGES = [
  '/pages/release/index',
  '/pages/my-items/index',
  '/pages/my/info-edit/index',
  '/pages/chat/index'
];

/**
 * 检查页面是否需要登录
 * @param {string} path 页面路径
 * @returns {boolean} 是否需要登录
 */
function isProtectedPage(path) {
  return PROTECTED_PAGES.some(page => path.includes(page));
}

/**
 * 路由守卫 - 检查是否需要登录
 * @param {string} url 目标页面URL
 * @param {function} navigateFunc 导航函数 (wx.navigateTo, wx.redirectTo等)
 * @returns {boolean} 是否允许导航
 */
function routeGuard(url, navigateFunc = wx.navigateTo) {
  const isProtected = isProtectedPage(url);
  const loggedIn = isLoggedIn();
  
  if (isProtected && !loggedIn) {
    // 需要登录但未登录，跳转到登录页
    wx.showModal({
      title: '提示',
      content: '请先登录后再使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/index'
          });
        }
      }
    });
    return false;
  }
  
  // 允许导航
  navigateFunc({ url });
  return true;
}

/**
 * 安全导航 - 带登录检查的导航
 * @param {string} url 目标页面URL
 * @param {object} options 导航选项
 */
function safeNavigateTo(url, options = {}) {
  if (isProtectedPage(url) && !isLoggedIn()) {
    wx.showModal({
      title: '提示',
      content: '请先登录后再使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/index'
          });
        }
      }
    });
    return;
  }
  
  wx.navigateTo({
    url,
    ...options
  });
}

/**
 * 安全重定向 - 带登录检查的重定向
 * @param {string} url 目标页面URL
 * @param {object} options 导航选项
 */
function safeRedirectTo(url, options = {}) {
  if (isProtectedPage(url) && !isLoggedIn()) {
    wx.redirectTo({
      url: '/pages/login/index'
    });
    return;
  }
  
  wx.redirectTo({
    url,
    ...options
  });
}

/**
 * 登出
 */
function logout() {
  wx.showModal({
    title: '确认登出',
    content: '确定要退出登录吗？',
    confirmText: '确定',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        clearAuth();
        wx.showToast({
          title: '已退出登录',
          icon: 'success'
        });
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1500);
      }
    }
  });
}

/**
 * 更新用户信息
 * @param {object} userInfo 新的用户信息
 */
function updateUserInfo(userInfo) {
  const currentUserInfo = getUserInfo();
  const updatedUserInfo = {
    ...currentUserInfo,
    ...userInfo
  };
  wx.setStorageSync('user_info', updatedUserInfo);
}

module.exports = {
  isLoggedIn,
  getUserInfo,
  getAccessToken,
  clearAuth,
  isProtectedPage,
  routeGuard,
  safeNavigateTo,
  safeRedirectTo,
  logout,
  updateUserInfo,
  PROTECTED_PAGES
};
