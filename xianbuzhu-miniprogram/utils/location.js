/**
 * 地理位置相关工具函数
 */

/**
 * 获取用户当前位置
 * @returns {Promise<object>} 位置信息
 */
function getCurrentLocation() {
  return new Promise((resolve, reject) => {
    wx.getLocation({
      type: 'gcj02', // 返回可以用于wx.openLocation的经纬度
      success: (res) => {
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy
        });
      },
      fail: (error) => {
        console.error('获取位置失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 逆地理编码 - 根据经纬度获取地址信息
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @returns {Promise<object>} 地址信息
 */
function reverseGeocode(latitude, longitude) {
  return new Promise((resolve, reject) => {
    // 这里使用腾讯地图API进行逆地理编码
    // 实际项目中需要申请腾讯地图API密钥
    
    // 模拟API调用
    setTimeout(() => {
      // 模拟返回的地址信息
      const mockAddress = {
        province: '广东省',
        city: '深圳市',
        district: '南山区',
        street: '科技园',
        community: '阳光社区',
        building: '阳光花园',
        address: '广东省深圳市南山区科技园阳光花园',
        formatted_address: '阳光花园'
      };
      
      resolve(mockAddress);
    }, 500);
  });
}

/**
 * 搜索附近的小区/社区
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @param {number} radius 搜索半径(米)
 * @returns {Promise<Array>} 附近的小区列表
 */
function searchNearbyCommunities(latitude, longitude, radius = 3000) {
  return new Promise((resolve) => {
    // 模拟搜索附近小区
    setTimeout(() => {
      const mockCommunities = [
        {
          id: '1',
          name: '阳光花园',
          address: '科技园南路123号',
          distance: 0,
          latitude: latitude,
          longitude: longitude
        },
        {
          id: '2', 
          name: '科技园小区',
          address: '科技园北路456号',
          distance: 500,
          latitude: latitude + 0.005,
          longitude: longitude + 0.005
        },
        {
          id: '3',
          name: '深圳湾花园',
          address: '深圳湾路789号',
          distance: 1200,
          latitude: latitude - 0.01,
          longitude: longitude + 0.01
        },
        {
          id: '4',
          name: '海岸城',
          address: '文心五路101号',
          distance: 1800,
          latitude: latitude + 0.015,
          longitude: longitude - 0.008
        },
        {
          id: '5',
          name: '华侨城',
          address: '华侨城东路202号',
          distance: 2100,
          latitude: latitude - 0.018,
          longitude: longitude - 0.012
        }
      ];
      
      resolve(mockCommunities);
    }, 800);
  });
}

/**
 * 获取用户授权位置信息
 * @returns {Promise<object>} 位置和地址信息
 */
async function getUserLocationWithAddress() {
  try {
    // 检查位置权限
    const authResult = await checkLocationAuth();
    if (!authResult) {
      throw new Error('位置权限被拒绝');
    }
    
    // 获取当前位置
    const location = await getCurrentLocation();
    
    // 获取地址信息
    const address = await reverseGeocode(location.latitude, location.longitude);
    
    return {
      ...location,
      address
    };
  } catch (error) {
    console.error('获取位置信息失败:', error);
    throw error;
  }
}

/**
 * 检查位置权限
 * @returns {Promise<boolean>} 是否有权限
 */
function checkLocationAuth() {
  return new Promise((resolve) => {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === undefined) {
          // 用户未授权，请求授权
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => resolve(true),
            fail: () => resolve(false)
          });
        } else if (res.authSetting['scope.userLocation']) {
          // 用户已授权
          resolve(true);
        } else {
          // 用户拒绝授权
          resolve(false);
        }
      },
      fail: () => resolve(false)
    });
  });
}

/**
 * 请求位置权限
 * @returns {Promise<boolean>} 是否获得权限
 */
function requestLocationAuth() {
  return new Promise((resolve) => {
    wx.showModal({
      title: '位置权限',
      content: '需要获取您的位置信息，以便为您推荐附近的闲置物品',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              resolve(!!settingRes.authSetting['scope.userLocation']);
            },
            fail: () => resolve(false)
          });
        } else {
          resolve(false);
        }
      }
    });
  });
}

/**
 * 保存用户选择的区域
 * @param {object} community 社区信息
 */
function saveSelectedCommunity(community) {
  wx.setStorageSync('selected_community', community);
  
  // 触发全局事件，通知其他页面更新
  const eventChannel = getApp().globalData.eventChannel;
  if (eventChannel) {
    eventChannel.emit('communityChanged', community);
  }
}

/**
 * 获取用户选择的区域
 * @returns {object|null} 社区信息
 */
function getSelectedCommunity() {
  return wx.getStorageSync('selected_community') || null;
}

/**
 * 计算两点间距离
 * @param {number} lat1 纬度1
 * @param {number} lng1 经度1
 * @param {number} lat2 纬度2
 * @param {number} lng2 经度2
 * @returns {number} 距离(米)
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000; // 地球半径(米)
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * 格式化距离显示
 * @param {number} distance 距离(米)
 * @returns {string} 格式化的距离
 */
function formatDistance(distance) {
  if (distance < 1000) {
    return `${Math.round(distance)}m`;
  } else {
    return `${(distance / 1000).toFixed(1)}km`;
  }
}

module.exports = {
  getCurrentLocation,
  reverseGeocode,
  searchNearbyCommunities,
  getUserLocationWithAddress,
  checkLocationAuth,
  requestLocationAuth,
  saveSelectedCommunity,
  getSelectedCommunity,
  calculateDistance,
  formatDistance
};
