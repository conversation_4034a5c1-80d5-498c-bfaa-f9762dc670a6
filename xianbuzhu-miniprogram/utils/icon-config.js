/**
 * 图标配置 - 解决TDesign在线字体加载问题
 * 将TDesign图标映射为Unicode字符或本地图标
 */

// 常用图标映射表
const ICON_MAP = {
  // 导航类图标
  'chevron-left': '‹',
  'chevron-right': '›',
  'chevron-up': '⌃',
  'chevron-down': '⌄',
  'arrow-left': '←',
  'arrow-right': '→',
  'arrow-up': '↑',
  'arrow-down': '↓',
  'back': '‹',
  'left': '‹',
  'right': '›',
  
  // 操作类图标
  'add': '+',
  'minus': '−',
  'close': '×',
  'check': '✓',
  'search': '🔍',
  'filter': '⚙',
  'more': '⋯',
  'menu': '☰',
  
  // 功能类图标
  'home': '🏠',
  'user': '👤',
  'heart': '♡',
  'heart-filled': '♥',
  'star': '☆',
  'star-filled': '★',
  'location': '📍',
  'time': '🕐',
  'phone': '📞',
  'chat': '💬',
  'camera': '📷',
  'image': '🖼',
  'file': '📄',
  'folder': '📁',
  'setting': '⚙',
  'info': 'ℹ',
  'warning': '⚠',
  'error': '❌',
  'success': '✅',
  
  // 社交类图标
  'share': '📤',
  'like': '👍',
  'comment': '💬',
  'message': '✉',
  'notification': '🔔',
  
  // 商务类图标
  'money': '💰',
  'cart': '🛒',
  'gift': '🎁',
  'tag': '🏷',
  'discount': '💸',
  
  // 状态类图标
  'loading': '⟳',
  'refresh': '🔄',
  'sync': '🔄',
  'upload': '⬆',
  'download': '⬇',
  'delete': '🗑',
  'edit': '✏',
  'copy': '📋',
  'paste': '📋',
  
  // 媒体类图标
  'play': '▶',
  'pause': '⏸',
  'stop': '⏹',
  'volume': '🔊',
  'mute': '🔇',
  
  // 其他常用图标
  'calendar': '📅',
  'clock': '🕐',
  'mail': '📧',
  'lock': '🔒',
  'unlock': '🔓',
  'key': '🔑',
  'eye': '👁',
  'eye-off': '🙈',
  'wifi': '📶',
  'battery': '🔋',
  'signal': '📶'
};

/**
 * 获取图标字符
 * @param {string} iconName - 图标名称
 * @returns {string} - 对应的Unicode字符
 */
function getIconChar(iconName) {
  return ICON_MAP[iconName] || iconName;
}

/**
 * 创建自定义图标组件配置
 */
const CUSTOM_ICON_CONFIG = {
  // 禁用在线字体
  useOnlineFont: false,
  
  // 使用本地字符映射
  useLocalMapping: true,
  
  // 默认图标大小
  defaultSize: '24rpx',
  
  // 默认颜色
  defaultColor: '#333'
};

/**
 * 图标样式生成器
 * @param {string} iconName - 图标名称
 * @param {string} size - 图标大小
 * @param {string} color - 图标颜色
 * @returns {object} - 样式对象
 */
function generateIconStyle(iconName, size = '24rpx', color = '#333') {
  return {
    fontSize: size,
    color: color,
    lineHeight: '1',
    display: 'inline-block',
    textAlign: 'center',
    verticalAlign: 'middle'
  };
}

/**
 * 检查是否为TDesign图标
 * @param {string} iconName - 图标名称
 * @returns {boolean}
 */
function isTDesignIcon(iconName) {
  return iconName && typeof iconName === 'string' && iconName.length > 0;
}

/**
 * 图标兼容性处理
 * @param {string} iconName - 原始图标名称
 * @returns {string} - 处理后的图标字符
 */
function processIcon(iconName) {
  if (!iconName) return '';
  
  // 移除可能的前缀
  const cleanName = iconName.replace(/^(t-icon-|icon-|t-)/, '');
  
  // 获取映射字符
  return getIconChar(cleanName);
}

module.exports = {
  ICON_MAP,
  CUSTOM_ICON_CONFIG,
  getIconChar,
  generateIconStyle,
  isTDesignIcon,
  processIcon
};
