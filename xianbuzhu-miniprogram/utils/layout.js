/**
 * 布局相关工具函数 - 基于网络最佳实践
 * 关键点：使用px单位，不要用rpx！
 */

/**
 * 获取系统信息和导航栏信息
 * @returns {object} 系统信息
 */
function getSystemInfo() {
  // 获取系统信息
  const systemInfo = wx.getSystemInfoSync();
  // 获取胶囊按钮信息
  const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

  // 状态栏高度 (px)
  const statusBarHeight = systemInfo.statusBarHeight;

  // 导航栏高度计算 - 关键公式！
  // 导航栏高度 = (胶囊距顶部距离 - 状态栏高度) * 2 + 胶囊高度
  const navBarHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;

  // 胶囊底部位置
  const capsuleBottom = menuButtonInfo.top + menuButtonInfo.height;

  return {
    statusBarHeight,        // 状态栏高度 (px)
    navBarHeight,          // 导航栏高度 (px)
    capsuleBottom,         // 胶囊底部位置 (px)
    totalTopHeight: statusBarHeight + navBarHeight, // 总顶部高度 (px)
    menuButtonInfo,        // 胶囊信息
    systemInfo            // 系统信息
  };
}

/**
 * 获取页面布局信息 - 返回px值用于样式设置
 * @returns {object} 页面布局信息
 */
function getPageLayoutInfo() {
  const systemInfo = getSystemInfo();

  return {
    // 状态栏高度 (px) - 用于padding-top
    statusBarHeight: systemInfo.statusBarHeight,
    // 导航栏高度 (px) - 用于导航栏内容高度
    navBarHeight: systemInfo.navBarHeight,
    // 总顶部高度 (px) - 用于导航栏总高度
    totalTopHeight: systemInfo.totalTopHeight,
    // 胶囊底部位置 (px) - 用于避开胶囊
    capsuleBottom: systemInfo.capsuleBottom,
    // 胶囊信息
    menuButtonInfo: systemInfo.menuButtonInfo
  };
}

module.exports = {
  getSystemInfo,
  getPageLayoutInfo
};


