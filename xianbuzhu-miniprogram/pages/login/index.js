const Message = require('tdesign-miniprogram/message/index');

Page({
  data: {
    showPhoneLogin: false,
    phone: '',
    code: '',
    loginLoading: false,
    phoneLoginLoading: false,
    codeDisabled: false,
    codeText: '获取验证码',
    countdown: 60
  },

  onLoad(options) {
    // 检查是否已经登录
    const token = wx.getStorageSync('access_token');
    if (token) {
      this.redirectToHome();
    }
  },

  // 微信一键登录
  async wxLogin() {
    try {
      this.setData({ loginLoading: true });
      
      // 获取微信登录code
      const loginRes = await wx.login();
      if (!loginRes.code) {
        throw new Error('获取微信登录code失败');
      }

      // 获取用户信息授权
      const userProfile = await wx.getUserProfile({
        desc: '用于完善用户资料'
      });

      // 模拟API调用
      const loginResult = await this.mockWxLogin(loginRes.code, userProfile);
      
      // 保存登录信息
      wx.setStorageSync('access_token', loginResult.token);
      wx.setStorageSync('user_info', loginResult.userInfo);
      
      Message.success({
        context: this,
        offset: [120, 32],
        duration: 2000,
        content: '登录成功',
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        this.redirectToHome();
      }, 1500);

    } catch (error) {
      console.error('微信登录失败:', error);
      Message.error({
        context: this,
        offset: [120, 32],
        duration: 3000,
        content: error.message || '登录失败，请重试',
      });
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  // 手机号登录
  async phoneLogin() {
    try {
      this.setData({ phoneLoginLoading: true });
      
      // 验证手机号格式
      if (!this.validatePhone(this.data.phone)) {
        throw new Error('请输入正确的手机号');
      }

      if (!this.data.code) {
        throw new Error('请输入验证码');
      }

      // 模拟API调用
      const loginResult = await this.mockPhoneLogin(this.data.phone, this.data.code);
      
      // 保存登录信息
      wx.setStorageSync('access_token', loginResult.token);
      wx.setStorageSync('user_info', loginResult.userInfo);
      
      Message.success({
        context: this,
        offset: [120, 32],
        duration: 2000,
        content: '登录成功',
      });

      setTimeout(() => {
        this.redirectToHome();
      }, 1500);

    } catch (error) {
      console.error('手机号登录失败:', error);
      Message.error({
        context: this,
        offset: [120, 32],
        duration: 3000,
        content: error.message || '登录失败，请重试',
      });
    } finally {
      this.setData({ phoneLoginLoading: false });
    }
  },

  // 发送验证码
  async sendCode() {
    if (!this.validatePhone(this.data.phone)) {
      Message.error({
        context: this,
        offset: [120, 32],
        duration: 2000,
        content: '请输入正确的手机号',
      });
      return;
    }

    try {
      // 模拟发送验证码
      await this.mockSendCode(this.data.phone);
      
      Message.success({
        context: this,
        offset: [120, 32],
        duration: 2000,
        content: '验证码已发送',
      });

      // 开始倒计时
      this.startCountdown();

    } catch (error) {
      Message.error({
        context: this,
        offset: [120, 32],
        duration: 2000,
        content: '发送失败，请重试',
      });
    }
  },

  // 开始倒计时
  startCountdown() {
    this.setData({ 
      codeDisabled: true,
      countdown: 60 
    });

    const timer = setInterval(() => {
      const countdown = this.data.countdown - 1;
      
      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          codeDisabled: false,
          codeText: '获取验证码',
          countdown: 60
        });
      } else {
        this.setData({
          countdown,
          codeText: `${countdown}s后重发`
        });
      }
    }, 1000);
  },

  // 切换登录方式
  toggleLoginType() {
    this.setData({
      showPhoneLogin: !this.data.showPhoneLogin
    });
  },

  // 输入事件
  onPhoneChange(e) {
    this.setData({ phone: e.detail.value });
  },

  onCodeChange(e) {
    this.setData({ code: e.detail.value });
  },

  // 显示用户协议
  showUserAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/index?type=user'
    });
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/agreement/index?type=privacy'
    });
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 验证手机号格式
  validatePhone(phone) {
    const phoneReg = /^1[3-9]\d{9}$/;
    return phoneReg.test(phone);
  },

  // 模拟微信登录API
  mockWxLogin(code, userProfile) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          token: 'mock_wx_token_' + Date.now(),
          userInfo: {
            id: 'user_' + Date.now(),
            name: userProfile.userInfo.nickName,
            avatar: userProfile.userInfo.avatarUrl,
            phone: '',
            loginType: 'wechat'
          }
        });
      }, 1000);
    });
  },

  // 模拟手机号登录API
  mockPhoneLogin(phone, code) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (code === '123456') {
          resolve({
            token: 'mock_phone_token_' + Date.now(),
            userInfo: {
              id: 'user_' + Date.now(),
              name: '用户' + phone.slice(-4),
              avatar: '/static/default-avatar.png',
              phone: phone,
              loginType: 'phone'
            }
          });
        } else {
          reject(new Error('验证码错误'));
        }
      }, 1000);
    });
  },

  // 模拟发送验证码API
  mockSendCode(phone) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('验证码已发送到:', phone, '验证码: 123456');
        resolve();
      }, 500);
    });
  }
});
