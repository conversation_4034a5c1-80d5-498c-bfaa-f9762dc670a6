<view class="login-container">
  <!-- 顶部logo和标题 -->
  <view class="login-header">
    <view class="logo">
      <image src="/static/logo.png" class="logo-image" mode="aspectFit" />
    </view>
    <view class="app-title">闲不住</view>
    <view class="app-subtitle">让闲置物品重新焕发价值</view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 微信一键登录 -->
    <t-button 
      theme="primary" 
      size="large" 
      block
      icon="wechat"
      bindtap="wxLogin"
      loading="{{loginLoading}}"
      t-class="login-btn"
    >
      微信一键登录
    </t-button>

    <!-- 手机号登录 -->
    <view class="phone-login" wx:if="{{showPhoneLogin}}">
      <view class="input-group">
        <t-input
          placeholder="请输入手机号"
          type="number"
          maxlength="11"
          value="{{phone}}"
          bind:change="onPhoneChange"
          t-class="phone-input"
        />
      </view>
      
      <view class="input-group">
        <t-input
          placeholder="请输入验证码"
          type="number"
          maxlength="6"
          value="{{code}}"
          bind:change="onCodeChange"
          t-class="code-input"
        >
          <t-button 
            slot="suffix"
            theme="light"
            size="small"
            bindtap="sendCode"
            disabled="{{codeDisabled}}"
            t-class="code-btn"
          >
            {{codeText}}
          </t-button>
        </t-input>
      </view>

      <t-button 
        theme="primary" 
        size="large" 
        block
        bindtap="phoneLogin"
        loading="{{phoneLoginLoading}}"
        disabled="{{!phone || !code}}"
        t-class="phone-login-btn"
      >
        登录
      </t-button>
    </view>

    <!-- 切换登录方式 -->
    <view class="switch-login">
      <text 
        class="switch-text" 
        bindtap="toggleLoginType"
      >
        {{showPhoneLogin ? '使用微信登录' : '使用手机号登录'}}
      </text>
    </view>
  </view>

  <!-- 协议条款 -->
  <view class="agreement">
    <view class="agreement-text">
      登录即表示同意
      <text class="link" bindtap="showUserAgreement">《用户协议》</text>
      和
      <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
    </view>
  </view>

  <!-- 底部装饰 -->
  <view class="login-footer">
    <view class="footer-text">安全 · 便捷 · 环保</view>
  </view>
</view>

<t-message id="t-message" />
