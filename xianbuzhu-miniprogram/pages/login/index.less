@import '/variable.less';

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 80rpx 60rpx 40rpx;
  box-sizing: border-box;
}

.login-header {
  text-align: center;
  margin-bottom: 120rpx;
  
  .logo {
    margin-bottom: 40rpx;
    
    .logo-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 24rpx;
      background: rgba(255, 255, 255, 0.1);
    }
  }
  
  .app-title {
    font-size: 48rpx;
    font-weight: 600;
    color: white;
    margin-bottom: 16rpx;
  }
  
  .app-subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-form {
  flex: 1;
  
  .login-btn {
    margin-bottom: 40rpx;
    height: 96rpx;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 500;
  }
  
  .phone-login {
    .input-group {
      margin-bottom: 32rpx;
      
      .phone-input,
      .code-input {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 48rpx;
        height: 96rpx;
        
        .t-input__content {
          padding: 0 32rpx;
        }
      }
      
      .code-btn {
        margin-right: 16rpx;
        border-radius: 32rpx;
        font-size: 24rpx;
      }
    }
    
    .phone-login-btn {
      height: 96rpx;
      border-radius: 48rpx;
      font-size: 32rpx;
      font-weight: 500;
      margin-bottom: 40rpx;
    }
  }
  
  .switch-login {
    text-align: center;
    
    .switch-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 28rpx;
      text-decoration: underline;
    }
  }
}

.agreement {
  margin-top: 60rpx;
  text-align: center;
  
  .agreement-text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.5;
    
    .link {
      color: rgba(255, 255, 255, 0.9);
      text-decoration: underline;
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 40rpx;
  
  .footer-text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.5);
    letter-spacing: 2rpx;
  }
}
