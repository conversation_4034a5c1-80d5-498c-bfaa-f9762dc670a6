const Message = require('tdesign-miniprogram/message/index');

Page({
  data: {
    currentStatus: 'all',
    currentStatusText: '全部',
    statusList: [
      { label: '全部', value: 'all', count: 8 },
      { label: '可交换', value: 'available', count: 5 },
      { label: '交换中', value: 'exchanging', count: 2 },
      { label: '已交换', value: 'exchanged', count: 1 },
      { label: '草稿', value: 'draft', count: 1 }
    ],
    itemList: [],
    allItems: [], // 存储所有物品数据
    loading: false,
    showDeleteDialog: false,
    deleteItemId: null
  },

  onLoad() {
    this.loadMyItems();
  },

  async loadMyItems() {
    try {
      this.setData({ loading: true });
      
      // 模拟API调用获取我的物品
      const mockItems = [
        {
          id: '1',
          title: '九成新iPhone 14',
          desc: '换新手机了，这台iPhone 14用了半年，功能完好',
          image: '/static/home/<USER>',
          status: 'available',
          statusText: '可交换',
          publishTime: '2小时前',
          viewCount: 128,
          likeCount: 23,
          messageCount: 5
        },
        {
          id: '2',
          title: '小米空气净化器',
          desc: '搬家用不上了，使用一年多，效果很好',
          image: '/static/home/<USER>',
          status: 'exchanging',
          statusText: '交换中',
          publishTime: '1天前',
          viewCount: 156,
          likeCount: 31,
          messageCount: 8
        },
        {
          id: '3',
          title: '编程入门书籍',
          desc: '一套完整的编程学习书籍，适合初学者',
          image: '/static/home/<USER>',
          status: 'exchanged',
          statusText: '已交换',
          publishTime: '2天前',
          viewCount: 67,
          likeCount: 12,
          messageCount: 3
        },
        {
          id: '4',
          title: '宜家书桌',
          desc: '搬家处理，宜家购买的书桌，质量很好',
          image: '/static/home/<USER>',
          status: 'available',
          statusText: '可交换',
          publishTime: '3天前',
          viewCount: 203,
          likeCount: 45,
          messageCount: 12
        },
        {
          id: '5',
          title: '儿童自行车草稿',
          desc: '孩子长大了用不上了，车况很好',
          image: '/static/home/<USER>',
          status: 'draft',
          statusText: '草稿',
          publishTime: '5天前',
          viewCount: 0,
          likeCount: 0,
          messageCount: 0
        }
      ];

      this.setData({
        allItems: mockItems,
        loading: false
      });
      
      this.filterItems();
      
    } catch (error) {
      console.error('加载我的物品失败:', error);
      Message.error({
        context: this,
        offset: [120, 32],
        duration: 3000,
        content: '加载失败，请重试',
      });
      this.setData({ loading: false });
    }
  },

  onStatusChange(e) {
    const { value } = e.currentTarget.dataset;
    const statusItem = this.data.statusList.find(item => item.value === value);
    
    this.setData({
      currentStatus: value,
      currentStatusText: statusItem ? statusItem.label : '全部'
    });
    
    this.filterItems();
  },

  filterItems() {
    const { currentStatus, allItems } = this.data;
    let filteredItems = allItems;
    
    if (currentStatus !== 'all') {
      filteredItems = allItems.filter(item => item.status === currentStatus);
    }
    
    this.setData({
      itemList: filteredItems
    });
  },

  onRefresh() {
    this.loadMyItems();
  },

  editItem(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/release/index?id=${id}&mode=edit`
    });
  },

  viewDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/item-detail/index?id=${id}`
    });
  },

  deleteItem(e) {
    const { id } = e.currentTarget.dataset;
    this.setData({
      showDeleteDialog: true,
      deleteItemId: id
    });
  },

  confirmDelete() {
    const { deleteItemId, allItems } = this.data;
    
    // 从列表中删除物品
    const updatedItems = allItems.filter(item => item.id !== deleteItemId);
    
    this.setData({
      allItems: updatedItems,
      showDeleteDialog: false,
      deleteItemId: null
    });
    
    this.filterItems();
    
    Message.success({
      context: this,
      offset: [120, 32],
      duration: 2000,
      content: '删除成功',
    });
  },

  cancelDelete() {
    this.setData({
      showDeleteDialog: false,
      deleteItemId: null
    });
  },

  goToRelease() {
    wx.navigateTo({
      url: '/pages/release/index'
    });
  },

  onShareAppMessage() {
    return {
      title: '我的闲置物品',
      path: '/pages/my-items/index'
    };
  }
});
