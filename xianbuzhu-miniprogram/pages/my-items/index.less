@import '/variable.less';

.my-items-page {
  height: 100vh;
  background-color: @bg-color;
  display: flex;
  flex-direction: column;
}

.status-filter {
  background: @bg-color-white;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  .filter-scroll {
    white-space: nowrap;
  }
  
  .filter-list {
    display: flex;
    padding: 0 24rpx;
    gap: 24rpx;
  }
  
  .filter-item {
    padding: 12rpx 24rpx;
    border-radius: 32rpx;
    background: #f5f5f5;
    color: #666;
    font-size: 28rpx;
    white-space: nowrap;
    transition: all 0.3s;
    
    &.active {
      background: @brand-color;
      color: @bg-color-white;
    }
    
    .filter-count {
      margin-left: 8rpx;
      font-size: 24rpx;
    }
  }
}

.items-list {
  flex: 1;
  padding: 24rpx;
}

.item-card {
  background: @bg-color-white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  display: flex;
  gap: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .item-image-container {
    position: relative;
    
    .item-image {
      border-radius: 8rpx;
    }
    
    .item-status {
      position: absolute;
      top: 8rpx;
      right: 8rpx;
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
      font-size: 20rpx;
      color: white;
      
      &.available {
        background: #52c41a;
      }
      
      &.exchanging {
        background: #fa8c16;
      }
      
      &.exchanged {
        background: #999;
      }
      
      &.draft {
        background: #1890ff;
      }
    }
  }
  
  .item-content {
    flex: 1;
    min-width: 0;
    
    .item-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 12rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .item-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.4;
      margin-bottom: 16rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .item-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .item-stats {
        display: flex;
        gap: 24rpx;
        
        .stat-item {
          display: flex;
          align-items: center;
          gap: 6rpx;
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .item-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
  
  .item-actions {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
    align-items: flex-end;
    
    .t-button {
      min-width: 120rpx;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin: 24rpx 0 32rpx;
  }
  
  .release-btn {
    width: 200rpx;
  }
}
