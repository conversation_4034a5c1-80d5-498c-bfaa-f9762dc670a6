<t-navbar title="我发布的" left-arrow />

<view class="my-items-page">
  <!-- 状态筛选 -->
  <view class="status-filter">
    <scroll-view scroll-x class="filter-scroll">
      <view class="filter-list">
        <view 
          class="filter-item {{currentStatus === item.value ? 'active' : ''}}"
          wx:for="{{statusList}}"
          wx:key="value"
          bindtap="onStatusChange"
          data-value="{{item.value}}"
        >
          {{item.label}}
          <text class="filter-count" wx:if="{{item.count > 0}}">({{item.count}})</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 物品列表 -->
  <scroll-view class="items-list" scroll-y refresher-enabled bindrefresherrefresh="onRefresh">
    <view 
      class="item-card"
      wx:for="{{itemList}}"
      wx:key="id"
    >
      <!-- 物品图片 -->
      <view class="item-image-container">
        <t-image 
          src="{{item.image}}" 
          width="160rpx" 
          height="160rpx" 
          mode="aspectFill"
          t-class="item-image"
        />
        <view class="item-status {{item.status}}">
          {{item.statusText}}
        </view>
      </view>
      
      <!-- 物品信息 -->
      <view class="item-content">
        <view class="item-title">{{item.title}}</view>
        <view class="item-desc">{{item.desc}}</view>
        <view class="item-meta">
          <view class="item-stats">
            <view class="stat-item">
              <t-icon name="browse" size="24rpx" />
              <text>{{item.viewCount}}</text>
            </view>
            <view class="stat-item">
              <t-icon name="heart" size="24rpx" />
              <text>{{item.likeCount}}</text>
            </view>
            <view class="stat-item">
              <t-icon name="chat" size="24rpx" />
              <text>{{item.messageCount}}</text>
            </view>
          </view>
          <text class="item-time">{{item.publishTime}}</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="item-actions">
        <t-button 
          theme="light" 
          size="small" 
          bindtap="editItem"
          data-id="{{item.id}}"
          wx:if="{{item.status === 'available' || item.status === 'draft'}}"
        >
          编辑
        </t-button>
        <t-button 
          theme="primary" 
          size="small" 
          bindtap="viewDetail"
          data-id="{{item.id}}"
        >
          查看
        </t-button>
        <t-button 
          theme="danger" 
          size="small" 
          bindtap="deleteItem"
          data-id="{{item.id}}"
          wx:if="{{item.status === 'draft'}}"
        >
          删除
        </t-button>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{itemList.length === 0 && !loading}}">
      <t-icon name="inbox" size="120rpx" color="#ccc" />
      <text class="empty-text">暂无{{currentStatusText}}物品</text>
      <t-button 
        theme="primary" 
        size="medium" 
        bindtap="goToRelease"
        t-class="release-btn"
      >
        发布物品
      </t-button>
    </view>
  </scroll-view>
</view>

<!-- 删除确认弹窗 -->
<t-dialog
  visible="{{showDeleteDialog}}"
  title="确认删除"
  content="删除后无法恢复，确定要删除这个物品吗？"
  confirm-btn="删除"
  cancel-btn="取消"
  bind:confirm="confirmDelete"
  bind:cancel="cancelDelete"
/>

<t-message id="t-message" />
