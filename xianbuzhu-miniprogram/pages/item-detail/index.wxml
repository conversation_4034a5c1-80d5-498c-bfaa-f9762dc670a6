<t-navbar title="物品详情" left-arrow />

<scroll-view class="item-detail" scroll-y>
  <!-- 图片轮播 -->
  <view class="item-images">
    <t-swiper
      list="{{item.images}}"
      navigation="{{ { type: 'dots' } }}"
      height="600rpx"
    />
  </view>

  <!-- 基本信息 -->
  <view class="item-info">
    <view class="item-title">{{item.title}}</view>
    <view class="item-meta">
      <view class="meta-item">
        <t-icon name="location" size="28rpx" />
        <text>{{item.location}} · {{item.distance}}</text>
      </view>
      <view class="meta-item">
        <t-icon name="time" size="28rpx" />
        <text>{{item.publishTime}}</text>
      </view>
    </view>
    <view class="item-tags">
      <t-tag 
        wx:for="{{item.tags}}" 
        wx:key="index" 
        size="medium" 
        variant="light" 
        theme="{{item.theme}}"
      >
        {{item.text}}
      </t-tag>
    </view>
  </view>

  <!-- 详细描述 -->
  <view class="item-description">
    <view class="section-title">物品描述</view>
    <view class="description-text">{{item.desc}}</view>
  </view>

  <!-- 发布者信息 -->
  <view class="publisher-info">
    <view class="section-title">发布者</view>
    <t-cell 
      image="{{item.publisher.avatar}}"
      title="{{item.publisher.name}}"
      hover
      arrow
      bindtap="goToUserProfile"
      data-user-id="{{item.publisher.id}}"
    >
      <view slot="description" class="publisher-meta">
        <view class="trust-level">
          <t-icon name="star-filled" size="24rpx" color="#ffa500" />
          <text>信誉等级 {{item.publisher.trustLevel}}</text>
        </view>
      </view>
    </t-cell>
  </view>

  <!-- 统计信息 -->
  <view class="item-stats">
    <view class="stat-item">
      <view class="stat-number">{{item.viewCount}}</view>
      <view class="stat-label">浏览</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{item.likeCount}}</view>
      <view class="stat-label">喜欢</view>
    </view>
  </view>
</scroll-view>

<!-- 底部操作栏 -->
<view class="bottom-actions">
  <view class="action-left">
    <t-button 
      theme="light" 
      size="large" 
      icon="heart"
      bindtap="toggleLike"
      t-class="like-btn {{isLiked ? 'liked' : ''}}"
    >
      {{isLiked ? '已喜欢' : '喜欢'}}
    </t-button>
  </view>
  <view class="action-right">
    <t-button 
      theme="primary" 
      size="large" 
      icon="chat"
      bindtap="startChat"
    >
      联系发布者
    </t-button>
  </view>
</view>

<t-message id="t-message" />
