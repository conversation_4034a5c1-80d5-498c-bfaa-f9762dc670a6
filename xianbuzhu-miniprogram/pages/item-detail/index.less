@import '/variable.less';

.item-detail {
  // 减去导航栏和底部操作栏高度
  height: calc(100vh - 88rpx - 120rpx);
  background: @bg-color;
}

.item-images {
  background: @bg-color-white;
  
  .t-swiper {
    border-radius: 0;
  }
}

.item-info {
  background: @bg-color-white;
  padding: 32rpx;
  margin-bottom: 16rpx;
  
  .item-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    line-height: 1.4;
  }
  
  .item-meta {
    display: flex;
    gap: 32rpx;
    margin-bottom: 24rpx;
    
    .meta-item {
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-size: 28rpx;
      color: #666;
    }
  }
  
  .item-tags {
    display: flex;
    gap: 16rpx;
    flex-wrap: wrap;
  }
}

.item-description {
  background: @bg-color-white;
  padding: 32rpx;
  margin-bottom: 16rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .description-text {
    font-size: 30rpx;
    color: #666;
    line-height: 1.6;
  }
}

.publisher-info {
  background: @bg-color-white;
  padding: 32rpx;
  margin-bottom: 16rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .publisher-meta {
    .trust-level {
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-size: 26rpx;
      color: #ffa500;
    }
  }
}

.item-stats {
  background: @bg-color-white;
  padding: 32rpx;
  display: flex;
  justify-content: space-around;
  margin-bottom: 16rpx;
  
  .stat-item {
    text-align: center;
    
    .stat-number {
      font-size: 36rpx;
      font-weight: 600;
      color: @brand-color;
      margin-bottom: 8rpx;
    }
    
    .stat-label {
      font-size: 26rpx;
      color: #999;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: @bg-color-white;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  gap: 24rpx;
  
  .action-left {
    flex: 1;
    
    .like-btn {
      width: 100%;
      
      &.liked {
        color: @error-color;
        border-color: @error-color;
      }
    }
  }
  
  .action-right {
    flex: 2;
    
    .t-button {
      width: 100%;
    }
  }
}
