const Message = require('tdesign-miniprogram/message/index');

Page({
  data: {
    item: {},
    isLiked: false,
    loading: true
  },

  onLoad(options) {
    const { id } = options;
    if (id) {
      this.loadItemDetail(id);
    }
  },

  async loadItemDetail(id) {
    try {
      this.setData({ loading: true });
      
      // 模拟API调用获取物品详情
      // 实际项目中应该调用真实API
      const mockItem = {
        id: id,
        title: '九成新iPhone 14',
        desc: '换新手机了，这台iPhone 14用了半年，功能完好，有原装盒子和配件。屏幕无划痕，电池健康度95%，平时都有贴膜和带壳保护。包装盒、充电器、数据线都在，还有购买发票。',
        images: [
          '/static/home/<USER>',
          '/static/home/<USER>',
          '/static/home/<USER>'
        ],
        location: '阳光社区',
        distance: '0.5km',
        publishTime: '2小时前',
        publisher: {
          id: 'user1',
          name: '小明',
          avatar: '/static/avatar1.png',
          trustLevel: 5
        },
        tags: [
          {
            text: '数码产品',
            theme: 'primary',
          },
          {
            text: '九成新',
            theme: 'success',
          },
        ],
        status: 'available',
        viewCount: 128,
        likeCount: 23
      };

      this.setData({
        item: mockItem,
        loading: false
      });

      // 增加浏览量
      this.incrementViewCount(id);
      
    } catch (error) {
      console.error('加载物品详情失败:', error);
      Message.error({
        context: this,
        offset: [120, 32],
        duration: 3000,
        content: '加载失败，请重试',
      });
      this.setData({ loading: false });
    }
  },

  incrementViewCount(id) {
    // 模拟增加浏览量的API调用
    console.log('增加浏览量:', id);
  },

  toggleLike() {
    const newLikedState = !this.data.isLiked;
    const likeCountChange = newLikedState ? 1 : -1;
    
    this.setData({
      isLiked: newLikedState,
      'item.likeCount': this.data.item.likeCount + likeCountChange
    });

    // 模拟API调用
    console.log(newLikedState ? '点赞' : '取消点赞');
    
    Message.success({
      context: this,
      offset: [120, 32],
      duration: 2000,
      content: newLikedState ? '已添加到喜欢' : '已取消喜欢',
    });
  },

  startChat() {
    const { item } = this.data;
    
    // 跳转到聊天页面
    wx.navigateTo({
      url: `/pages/chat/index?userId=${item.publisher.id}&itemId=${item.id}`
    });
  },

  goToUserProfile(e) {
    const { userId } = e.currentTarget.dataset;
    
    // 跳转到用户详情页面
    wx.navigateTo({
      url: `/pages/user-profile/index?userId=${userId}`
    });
  },

  onShareAppMessage() {
    const { item } = this.data;
    return {
      title: item.title,
      path: `/pages/item-detail/index?id=${item.id}`,
      imageUrl: item.images[0]
    };
  }
});
