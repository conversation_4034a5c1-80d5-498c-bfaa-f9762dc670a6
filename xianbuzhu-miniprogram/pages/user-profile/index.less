@import '/variable.less';

.user-profile {
  // 减去导航栏和底部操作栏高度
  height: calc(100vh - 88rpx - 120rpx);
  background-color: @bg-color;
}

.user-header {
  background: @bg-color-white;
  padding: 32rpx;
  margin-bottom: 16rpx;
  
  .user-info {
    display: flex;
    gap: 24rpx;
    margin-bottom: 32rpx;
    
    .user-details {
      flex: 1;
      
      .user-name {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 16rpx;
      }
      
      .user-meta {
        display: flex;
        flex-direction: column;
        gap: 12rpx;
        margin-bottom: 16rpx;
        
        .trust-level,
        .join-date {
          display: flex;
          align-items: center;
          gap: 8rpx;
          font-size: 28rpx;
          color: #666;
        }
        
        .trust-level {
          color: #ffa500;
        }
      }
      
      .user-location {
        display: flex;
        align-items: center;
        gap: 8rpx;
        font-size: 28rpx;
        color: #999;
      }
    }
  }
  
  .user-stats {
    display: flex;
    justify-content: space-around;
    padding: 24rpx 0;
    border-top: 1rpx solid #f0f0f0;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 36rpx;
        font-weight: 600;
        color: @brand-color;
        margin-bottom: 8rpx;
        display: block;
      }
      
      .stat-label {
        font-size: 26rpx;
        color: #999;
      }
    }
  }
}

.user-items,
.user-reviews {
  background: @bg-color-white;
  margin-bottom: 16rpx;
  
  .section-title {
    padding: 32rpx 32rpx 24rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    border-bottom: 1rpx solid #f0f0f0;
    
    .item-count,
    .review-count {
      color: #999;
      font-weight: normal;
      font-size: 28rpx;
    }
  }
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 24rpx 32rpx;
  
  .item-card {
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    
    .item-image {
      border-radius: 12rpx 12rpx 0 0;
    }
    
    .item-info {
      padding: 16rpx;
      
      .item-title {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .item-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .item-time {
          font-size: 24rpx;
          color: #999;
        }
        
        .item-status {
          font-size: 22rpx;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
          
          &.available {
            background: #e8f5e8;
            color: #52c41a;
          }
          
          &.exchanging {
            background: #fff7e6;
            color: #fa8c16;
          }
          
          &.exchanged {
            background: #f0f0f0;
            color: #999;
          }
        }
      }
    }
  }
}

.reviews-list {
  padding: 0 32rpx 32rpx;
  
  .review-item {
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .reviewer-info {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 16rpx;
      
      .reviewer-details {
        flex: 1;
        
        .reviewer-name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
          display: block;
        }
        
        .review-rating {
          display: flex;
          gap: 4rpx;
        }
      }
      
      .review-time {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .review-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }
}

.empty-items,
.empty-reviews {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 24rpx;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: @bg-color-white;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  gap: 24rpx;
  
  .t-button {
    flex: 1;
  }
}
