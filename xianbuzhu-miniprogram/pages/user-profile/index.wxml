<t-navbar title="用户详情" left-arrow />

<scroll-view class="user-profile" scroll-y>
  <!-- 用户基本信息 -->
  <view class="user-header">
    <view class="user-info">
      <t-avatar size="extra-large" image="{{userInfo.avatar}}" />
      <view class="user-details">
        <view class="user-name">{{userInfo.name}}</view>
        <view class="user-meta">
          <view class="trust-level">
            <t-icon name="star-filled" size="28rpx" color="#ffa500" />
            <text>信誉等级 {{userInfo.trustLevel}}</text>
          </view>
          <view class="join-date">
            <t-icon name="time" size="28rpx" color="#999" />
            <text>{{userInfo.joinDate}}加入</text>
          </view>
        </view>
        <view class="user-location">
          <t-icon name="location" size="28rpx" color="#999" />
          <text>{{userInfo.location}}</text>
        </view>
      </view>
    </view>
    
    <!-- 用户统计 -->
    <view class="user-stats">
      <view class="stat-item">
        <view class="stat-number">{{userInfo.publishCount}}</view>
        <view class="stat-label">发布物品</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{userInfo.exchangeCount}}</view>
        <view class="stat-label">成功交换</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{userInfo.responseRate}}%</view>
        <view class="stat-label">回复率</view>
      </view>
    </view>
  </view>

  <!-- 用户发布的物品 -->
  <view class="user-items">
    <view class="section-title">
      <text>TA发布的物品</text>
      <text class="item-count">({{userItems.length}})</text>
    </view>
    
    <view class="items-grid" wx:if="{{userItems.length > 0}}">
      <view 
        class="item-card"
        wx:for="{{userItems}}"
        wx:key="id"
        bindtap="goToItemDetail"
        data-id="{{item.id}}"
      >
        <t-image 
          src="{{item.image}}" 
          width="100%" 
          height="240rpx" 
          mode="aspectFill"
          t-class="item-image"
        />
        <view class="item-info">
          <view class="item-title">{{item.title}}</view>
          <view class="item-meta">
            <text class="item-time">{{item.publishTime}}</text>
            <view class="item-status {{item.status}}">
              {{item.status === 'available' ? '可交换' : item.status === 'exchanging' ? '交换中' : '已交换'}}
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="empty-items" wx:else>
      <t-icon name="inbox" size="120rpx" color="#ccc" />
      <text class="empty-text">暂无发布物品</text>
    </view>
  </view>

  <!-- 用户评价 -->
  <view class="user-reviews">
    <view class="section-title">
      <text>用户评价</text>
      <text class="review-count">({{reviews.length}})</text>
    </view>
    
    <view class="reviews-list" wx:if="{{reviews.length > 0}}">
      <view 
        class="review-item"
        wx:for="{{reviews}}"
        wx:key="id"
      >
        <view class="reviewer-info">
          <t-avatar size="small" image="{{item.reviewerAvatar}}" />
          <view class="reviewer-details">
            <text class="reviewer-name">{{item.reviewerName}}</text>
            <view class="review-rating">
              <t-icon 
                wx:for="{{5}}" 
                wx:for-item="star" 
                wx:key="star"
                name="star-filled" 
                size="24rpx" 
                color="{{star < item.rating ? '#ffa500' : '#e0e0e0'}}" 
              />
            </view>
          </view>
          <text class="review-time">{{item.time}}</text>
        </view>
        <view class="review-content">{{item.content}}</view>
      </view>
    </view>
    
    <view class="empty-reviews" wx:else>
      <t-icon name="chat" size="120rpx" color="#ccc" />
      <text class="empty-text">暂无评价</text>
    </view>
  </view>
</scroll-view>

<!-- 底部操作栏 -->
<view class="bottom-actions">
  <t-button 
    theme="light" 
    size="large" 
    icon="chat"
    bindtap="startChat"
  >
    发消息
  </t-button>
  <t-button 
    theme="primary" 
    size="large" 
    icon="phone"
    bindtap="makeCall"
  >
    联系TA
  </t-button>
</view>

<t-message id="t-message" />
