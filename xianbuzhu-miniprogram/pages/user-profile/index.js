const Message = require('tdesign-miniprogram/message/index');

Page({
  data: {
    userInfo: {},
    userItems: [],
    reviews: [],
    loading: true
  },

  onLoad(options) {
    const { userId } = options;
    if (userId) {
      this.loadUserProfile(userId);
    }
  },

  async loadUserProfile(userId) {
    try {
      this.setData({ loading: true });
      
      // 模拟API调用获取用户详情
      const mockUserInfo = {
        id: userId,
        name: '小明',
        avatar: '/static/avatar1.png',
        trustLevel: 5,
        joinDate: '2023年6月',
        location: '阳光社区',
        publishCount: 8,
        exchangeCount: 15,
        responseRate: 95
      };

      // 模拟用户发布的物品
      const mockUserItems = [
        {
          id: '1',
          title: '九成新iPhone 14',
          image: '/static/home/<USER>',
          publishTime: '2小时前',
          status: 'available'
        },
        {
          id: '2',
          title: '小米空气净化器',
          image: '/static/home/<USER>',
          publishTime: '1天前',
          status: 'exchanging'
        },
        {
          id: '3',
          title: '编程入门书籍',
          image: '/static/home/<USER>',
          publishTime: '3天前',
          status: 'exchanged'
        }
      ];

      // 模拟用户评价
      const mockReviews = [
        {
          id: '1',
          reviewerName: '张三',
          reviewerAvatar: '/static/avatar1.png',
          rating: 5,
          content: '交换很顺利，物品描述准确，人也很好！',
          time: '1周前'
        },
        {
          id: '2',
          reviewerName: '李四',
          reviewerAvatar: '/static/avatar1.png',
          rating: 4,
          content: '物品质量不错，交换过程很愉快。',
          time: '2周前'
        }
      ];

      this.setData({
        userInfo: mockUserInfo,
        userItems: mockUserItems,
        reviews: mockReviews,
        loading: false
      });
      
    } catch (error) {
      console.error('加载用户详情失败:', error);
      Message.error({
        context: this,
        offset: [120, 32],
        duration: 3000,
        content: '加载失败，请重试',
      });
      this.setData({ loading: false });
    }
  },

  goToItemDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/item-detail/index?id=${id}`
    });
  },

  startChat() {
    const { userInfo } = this.data;
    wx.navigateTo({
      url: `/pages/chat/index?userId=${userInfo.id}`
    });
  },

  makeCall() {
    // 这里可以实现拨打电话功能
    // 实际项目中需要用户授权获取手机号
    Message.info({
      context: this,
      offset: [120, 32],
      duration: 2000,
      content: '请通过消息联系用户',
    });
  },

  onShareAppMessage() {
    const { userInfo } = this.data;
    return {
      title: `${userInfo.name}的个人主页`,
      path: `/pages/user-profile/index?userId=${userInfo.id}`,
      imageUrl: userInfo.avatar
    };
  }
});
