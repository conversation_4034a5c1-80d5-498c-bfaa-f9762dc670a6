@import '/variable.less';

.map-selector-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: @bg-color;
}

.map-container {
  flex: 1;
  position: relative;
  
  .map-center-marker {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -100%);
    z-index: 10;
    
    .marker-icon {
      width: 60rpx;
      height: 60rpx;
    }
  }
  
  .map-controls {
    position: absolute;
    right: 32rpx;
    bottom: 200rpx;
    z-index: 10;
    
    .control-btn {
      width: 80rpx;
      height: 80rpx;
      background: @bg-color-white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
      margin-bottom: 16rpx;
      
      .control-icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.address-panel {
  background: @bg-color-white;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 50vh;
  display: flex;
  flex-direction: column;
  
  .panel-header {
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .current-address {
      .address-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .address-detail {
        font-size: 26rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }
  
  .nearby-places {
    flex: 1;
    
    .places-title {
      padding: 24rpx 32rpx 16rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }
    
    .place-item {
      display: flex;
      align-items: center;
      padding: 20rpx 32rpx;
      border-bottom: 1rpx solid #f8f8f8;
      transition: background-color 0.2s;
      
      &:active {
        background-color: #f8f8f8;
      }
      
      &.selected {
        background-color: #f0f8ff;
      }
      
      .place-info {
        flex: 1;
        
        .place-name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 6rpx;
        }
        
        .place-address {
          font-size: 24rpx;
          color: #666;
        }
      }
      
      .place-distance {
        font-size: 24rpx;
        color: @brand-color;
      }
    }
    
    .places-loading,
    .places-empty {
      text-align: center;
      padding: 60rpx 0;
      color: #999;
      font-size: 28rpx;
    }
  }
}

.bottom-actions {
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: @bg-color-white;
  border-top: 1rpx solid #f0f0f0;
}
