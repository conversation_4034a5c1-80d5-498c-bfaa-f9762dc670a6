<t-navbar title="选择位置" left-arrow />

<view class="map-selector-page">
  <!-- 地图容器 -->
  <map
    id="map"
    class="map-container"
    latitude="{{mapCenter.latitude}}"
    longitude="{{mapCenter.longitude}}"
    scale="{{mapScale}}"
    markers="{{markers}}"
    bindmarkertap="onMarkerTap"
    bindtap="onMapTap"
    bindregionchange="onRegionChange"
    show-location
    enable-3D
    enable-overlooking
    enable-zoom
    enable-scroll
    enable-rotate
  >
    <!-- 中心点标记 -->
    <cover-view class="map-center-marker">
      <cover-image src="/static/map-marker.png" class="marker-icon" />
    </cover-view>
    
    <!-- 重新定位按钮 -->
    <cover-view class="map-controls">
      <cover-view class="control-btn" bindtap="relocateToUser">
        <cover-image src="/static/location-icon.png" class="control-icon" />
      </cover-view>
    </cover-view>
  </map>
  
  <!-- 地址信息面板 -->
  <view class="address-panel">
    <view class="panel-header">
      <view class="current-address">
        <view class="address-title">{{selectedAddress.name || '选择位置'}}</view>
        <view class="address-detail" wx:if="{{selectedAddress.address}}">
          {{selectedAddress.address}}
        </view>
      </view>
    </view>
    
    <!-- 附近地点列表 -->
    <scroll-view class="nearby-places" scroll-y>
      <view class="places-title">附近地点</view>
      <view 
        class="place-item {{item.selected ? 'selected' : ''}}"
        wx:for="{{nearbyPlaces}}"
        wx:key="id"
        bindtap="selectPlace"
        data-place="{{item}}"
      >
        <view class="place-info">
          <view class="place-name">{{item.name}}</view>
          <view class="place-address">{{item.address}}</view>
        </view>
        <view class="place-distance">{{item.distanceText}}</view>
      </view>
      
      <view class="places-loading" wx:if="{{loadingPlaces}}">
        <text>搜索中...</text>
      </view>
      
      <view class="places-empty" wx:elif="{{nearbyPlaces.length === 0}}">
        <text>暂无附近地点</text>
      </view>
    </scroll-view>
  </view>
  
  <!-- 底部确认按钮 -->
  <view class="bottom-actions">
    <t-button 
      theme="primary" 
      size="large" 
      block
      bindtap="confirmSelection"
      disabled="{{!selectedAddress.name}}"
    >
      确认选择
    </t-button>
  </view>
</view>
