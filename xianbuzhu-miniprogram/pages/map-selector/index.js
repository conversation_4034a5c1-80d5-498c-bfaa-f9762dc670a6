const { 
  getCurrentLocation, 
  reverseGeocode, 
  searchNearbyCommunities,
  saveSelectedCommunity,
  formatDistance,
  calculateDistance
} = require('../../utils/location');

Page({
  data: {
    mapCenter: {
      latitude: 22.5431,
      longitude: 114.0579
    },
    mapScale: 16,
    markers: [],
    selectedAddress: {},
    nearbyPlaces: [],
    loadingPlaces: false,
    userLocation: null
  },

  onLoad() {
    this.initMap();
  },

  // 初始化地图
  async initMap() {
    try {
      // 获取用户当前位置
      const location = await getCurrentLocation();
      
      this.setData({
        mapCenter: {
          latitude: location.latitude,
          longitude: location.longitude
        },
        userLocation: location
      });

      // 获取当前位置的地址信息
      await this.updateAddressInfo(location.latitude, location.longitude);
      
    } catch (error) {
      console.error('初始化地图失败:', error);
      wx.showToast({
        title: '定位失败',
        icon: 'none'
      });
    }
  },

  // 更新地址信息
  async updateAddressInfo(latitude, longitude) {
    try {
      this.setData({ loadingPlaces: true });
      
      // 获取地址信息
      const address = await reverseGeocode(latitude, longitude);
      
      this.setData({
        selectedAddress: {
          name: address.formatted_address,
          address: address.address,
          latitude: latitude,
          longitude: longitude
        }
      });

      // 搜索附近地点
      await this.searchNearbyPlaces(latitude, longitude);
      
    } catch (error) {
      console.error('更新地址信息失败:', error);
    } finally {
      this.setData({ loadingPlaces: false });
    }
  },

  // 搜索附近地点
  async searchNearbyPlaces(latitude, longitude) {
    try {
      const places = await searchNearbyCommunities(latitude, longitude);
      
      // 计算距离并格式化
      const placesWithDistance = places.map(place => {
        const distance = calculateDistance(
          latitude, longitude,
          place.latitude, place.longitude
        );
        
        return {
          ...place,
          distance: distance,
          distanceText: formatDistance(distance),
          selected: false
        };
      });

      this.setData({
        nearbyPlaces: placesWithDistance
      });
      
    } catch (error) {
      console.error('搜索附近地点失败:', error);
    }
  },

  // 地图区域变化
  onRegionChange(e) {
    if (e.type === 'end') {
      // 获取地图中心点
      const mapContext = wx.createMapContext('map');
      mapContext.getCenterLocation({
        success: (res) => {
          this.updateAddressInfo(res.latitude, res.longitude);
        }
      });
    }
  },

  // 点击地图
  onMapTap(e) {
    const { latitude, longitude } = e.detail;
    this.updateAddressInfo(latitude, longitude);
  },

  // 点击标记
  onMarkerTap(e) {
    const markerId = e.detail.markerId;
    const marker = this.data.markers.find(m => m.id === markerId);
    if (marker) {
      this.updateAddressInfo(marker.latitude, marker.longitude);
    }
  },

  // 重新定位到用户位置
  relocateToUser() {
    const { userLocation } = this.data;
    if (userLocation) {
      this.setData({
        mapCenter: {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude
        }
      });
      
      this.updateAddressInfo(userLocation.latitude, userLocation.longitude);
    } else {
      this.initMap();
    }
  },

  // 选择地点
  selectPlace(e) {
    const place = e.currentTarget.dataset.place;
    
    // 更新地图中心
    this.setData({
      mapCenter: {
        latitude: place.latitude,
        longitude: place.longitude
      },
      selectedAddress: {
        name: place.name,
        address: place.address,
        latitude: place.latitude,
        longitude: place.longitude
      }
    });

    // 更新列表选中状态
    const nearbyPlaces = this.data.nearbyPlaces.map(item => ({
      ...item,
      selected: item.id === place.id
    }));
    
    this.setData({ nearbyPlaces });
  },

  // 确认选择
  confirmSelection() {
    const { selectedAddress } = this.data;

    if (!selectedAddress.name) {
      wx.showToast({
        title: '请选择位置',
        icon: 'none'
      });
      return;
    }

    // 构造社区数据
    const community = {
      id: `map_${Date.now()}`,
      name: selectedAddress.name,
      address: selectedAddress.address,
      latitude: selectedAddress.latitude,
      longitude: selectedAddress.longitude,
      distance: 0
    };

    // 保存选择的社区
    saveSelectedCommunity(community);

    wx.showToast({
      title: '位置已选择',
      icon: 'success'
    });

    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  onShareAppMessage() {
    return {
      title: '选择位置',
      path: '/pages/map-selector/index'
    };
  }
});
