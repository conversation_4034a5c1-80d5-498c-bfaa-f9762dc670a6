#!/usr/bin/env node

/**
 * 地理位置功能验证脚本
 * 验证区域选择功能的完整性
 */

const fs = require('fs');
const path = require('path');

console.log('🗺️  验证地理位置和区域选择功能...\n');

// 地理位置功能文件列表
const LOCATION_FILES = [
  // 工具函数
  { path: 'utils/location.js', name: '地理位置工具函数', required: true },
  
  // 区域选择组件
  { path: 'components/location-selector/index.wxml', name: '区域选择器模板', required: true },
  { path: 'components/location-selector/index.js', name: '区域选择器逻辑', required: true },
  { path: 'components/location-selector/index.json', name: '区域选择器配置', required: true },
  { path: 'components/location-selector/index.less', name: '区域选择器样式', required: true },
  
  // 地图选择页面
  { path: 'pages/map-selector/index.wxml', name: '地图选择页面模板', required: true },
  { path: 'pages/map-selector/index.js', name: '地图选择页面逻辑', required: true },
  { path: 'pages/map-selector/index.json', name: '地图选择页面配置', required: true },
  { path: 'pages/map-selector/index.less', name: '地图选择页面样式', required: true },
];

// 检查文件是否存在
function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

// 检查地理位置功能文件
function checkLocationFiles() {
  console.log('📋 检查地理位置功能文件:');
  console.log('='.repeat(50));
  
  let allExists = true;
  
  LOCATION_FILES.forEach((file, index) => {
    const exists = checkFileExists(file.path);
    
    if (exists) {
      console.log(`✅ ${index + 1}. ${file.name}: 存在`);
    } else {
      console.log(`❌ ${index + 1}. ${file.name}: 缺失`);
      allExists = false;
    }
  });
  
  return allExists;
}

// 检查app.json配置
function checkAppJsonConfig() {
  console.log('\n📄 检查app.json配置:');
  console.log('='.repeat(50));
  
  try {
    const appJsonPath = 'app.json';
    if (!fs.existsSync(appJsonPath)) {
      console.log('❌ app.json文件不存在');
      return false;
    }
    
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    // 检查地图选择页面是否在分包中
    const subPackages = appJson.subpackages || [];
    const hasMapSelector = subPackages.some(pkg => 
      pkg.name === 'map-selector' && pkg.pages.includes('index')
    );
    
    console.log(`地图选择页面配置: ${hasMapSelector ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查位置权限配置
    const permission = appJson.permission;
    const hasLocationPermission = permission && 
      permission['scope.userLocation'] && 
      permission['scope.userLocation'].desc;
    
    console.log(`位置权限配置: ${hasLocationPermission ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查隐私信息配置
    const requiredPrivateInfos = appJson.requiredPrivateInfos || [];
    const hasLocationPrivacy = requiredPrivateInfos.includes('getLocation');
    
    console.log(`位置隐私配置: ${hasLocationPrivacy ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查全局组件注册
    const usingComponents = appJson.usingComponents || {};
    const hasLocationSelector = usingComponents['location-selector'];
    
    console.log(`区域选择器组件注册: ${hasLocationSelector ? '✅ 正确' : '❌ 缺失'}`);
    
    return hasMapSelector && hasLocationPermission && hasLocationPrivacy && hasLocationSelector;
    
  } catch (error) {
    console.log(`❌ app.json配置检查失败: ${error.message}`);
    return false;
  }
}

// 检查首页集成
function checkHomePageIntegration() {
  console.log('\n🏠 检查首页集成:');
  console.log('='.repeat(50));
  
  try {
    // 检查首页WXML
    const homeWxmlPath = 'pages/home/<USER>';
    if (!fs.existsSync(homeWxmlPath)) {
      console.log('❌ 首页WXML文件不存在');
      return false;
    }
    
    const homeWxml = fs.readFileSync(homeWxmlPath, 'utf8');
    const hasLocationSelector = homeWxml.includes('<location-selector');
    const hasCustomNavbar = homeWxml.includes('custom-navbar');
    
    console.log(`首页区域选择器: ${hasLocationSelector ? '✅ 已集成' : '❌ 未集成'}`);
    console.log(`自定义导航栏: ${hasCustomNavbar ? '✅ 已集成' : '❌ 未集成'}`);
    
    // 检查首页JS
    const homeJsPath = 'pages/home/<USER>';
    if (fs.existsSync(homeJsPath)) {
      const homeJs = fs.readFileSync(homeJsPath, 'utf8');
      const hasLocationChange = homeJs.includes('onLocationChange');
      const hasGoToSearch = homeJs.includes('goToSearch');
      
      console.log(`位置变化处理: ${hasLocationChange ? '✅ 已实现' : '❌ 未实现'}`);
      console.log(`搜索跳转: ${hasGoToSearch ? '✅ 已实现' : '❌ 未实现'}`);
    }
    
    // 检查首页JSON
    const homeJsonPath = 'pages/home/<USER>';
    if (fs.existsSync(homeJsonPath)) {
      const homeJson = JSON.parse(fs.readFileSync(homeJsonPath, 'utf8'));
      const hasLocationSelectorComponent = homeJson.usingComponents && 
        homeJson.usingComponents['location-selector'];
      
      console.log(`组件引用配置: ${hasLocationSelectorComponent ? '✅ 正确' : '❌ 缺失'}`);
    }
    
    return hasLocationSelector && hasCustomNavbar;
    
  } catch (error) {
    console.log(`❌ 首页集成检查失败: ${error.message}`);
    return false;
  }
}

// 检查工具函数
function checkUtilityFunctions() {
  console.log('\n🔧 检查工具函数:');
  console.log('='.repeat(50));
  
  try {
    const locationUtilPath = 'utils/location.js';
    if (!fs.existsSync(locationUtilPath)) {
      console.log('❌ 位置工具函数文件不存在');
      return false;
    }
    
    const locationUtil = fs.readFileSync(locationUtilPath, 'utf8');
    
    // 检查关键函数
    const functions = [
      'getCurrentLocation',
      'reverseGeocode',
      'searchNearbyCommunities',
      'getUserLocationWithAddress',
      'checkLocationAuth',
      'saveSelectedCommunity',
      'getSelectedCommunity'
    ];
    
    functions.forEach(func => {
      const hasFunction = locationUtil.includes(`function ${func}`) || 
                         locationUtil.includes(`${func}(`);
      console.log(`  ${func}: ${hasFunction ? '✅ 存在' : '❌ 缺失'}`);
    });
    
    return true;
    
  } catch (error) {
    console.log(`❌ 工具函数检查失败: ${error.message}`);
    return false;
  }
}

// 生成功能验证报告
function generateLocationReport(results) {
  console.log('\n📊 地理位置功能验证报告:');
  console.log('='.repeat(50));
  
  const { files, appJson, homePage, utils } = results;
  
  console.log(`功能文件检查: ${files ? '✅ 通过' : '❌ 失败'}`);
  console.log(`app.json配置: ${appJson ? '✅ 正确' : '❌ 错误'}`);
  console.log(`首页集成: ${homePage ? '✅ 完成' : '❌ 未完成'}`);
  console.log(`工具函数: ${utils ? '✅ 完整' : '❌ 不完整'}`);
  
  const allPassed = files && appJson && homePage && utils;
  
  if (allPassed) {
    console.log('\n🎉 地理位置功能验证通过！');
    console.log('\n🚀 功能特性:');
    console.log('  ✅ 用户位置获取和权限管理');
    console.log('  ✅ 区域选择器组件（类似美团）');
    console.log('  ✅ 附近小区/社区搜索');
    console.log('  ✅ 地图选择位置功能');
    console.log('  ✅ 位置信息本地存储');
    console.log('  ✅ 首页区域显示和切换');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 在真机上测试位置权限');
    console.log('  3. 测试区域选择功能');
    console.log('  4. 验证地图选择功能');
    console.log('  5. 检查位置信息存储');
  } else {
    console.log('\n⚠️  部分功能验证未通过，请查看上述详细信息');
    
    console.log('\n🔧 修复建议:');
    if (!files) console.log('  - 检查并创建缺失的功能文件');
    if (!appJson) console.log('  - 完善app.json中的权限和组件配置');
    if (!homePage) console.log('  - 在首页集成区域选择器组件');
    if (!utils) console.log('  - 完善位置工具函数');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    files: checkLocationFiles(),
    appJson: checkAppJsonConfig(),
    homePage: checkHomePageIntegration(),
    utils: checkUtilityFunctions()
  };
  
  generateLocationReport(results);
}

// 运行验证脚本
if (require.main === module) {
  main();
}

module.exports = { 
  checkLocationFiles, 
  checkAppJsonConfig, 
  checkHomePageIntegration,
  checkUtilityFunctions 
};
