#!/usr/bin/env node

/**
 * 基于网络最佳实践的布局验证脚本
 * 验证是否正确实现了网络上找到的解决方案
 */

const fs = require('fs');

console.log('🌐 基于网络最佳实践的布局验证...\n');

// 检查关键公式实现
function checkKeyFormula() {
  console.log('📐 检查关键公式实现:');
  console.log('='.repeat(50));
  
  try {
    const layoutUtilPath = 'utils/layout.js';
    const layoutUtil = fs.readFileSync(layoutUtilPath, 'utf8');
    
    // 检查关键公式：导航栏高度 = (胶囊距顶部距离 - 状态栏高度) * 2 + 胶囊高度
    const hasKeyFormula = layoutUtil.includes('(menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height');
    console.log(`关键公式实现: ${hasKeyFormula ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查使用px单位
    const usesPxUnit = layoutUtil.includes('使用px单位，不要用rpx');
    console.log(`px单位说明: ${usesPxUnit ? '✅ 已说明' : '❌ 未说明'}`);
    
    // 检查获取系统信息
    const hasGetSystemInfoSync = layoutUtil.includes('wx.getSystemInfoSync()');
    console.log(`获取系统信息: ${hasGetSystemInfoSync ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查获取胶囊信息
    const hasGetMenuButton = layoutUtil.includes('wx.getMenuButtonBoundingClientRect()');
    console.log(`获取胶囊信息: ${hasGetMenuButton ? '✅ 正确' : '❌ 错误'}`);
    
    return hasKeyFormula && hasGetSystemInfoSync && hasGetMenuButton;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查全局布局信息
function checkGlobalLayoutInfo() {
  console.log('\n🌍 检查全局布局信息:');
  console.log('='.repeat(50));
  
  try {
    const appJsPath = 'app.js';
    const appJs = fs.readFileSync(appJsPath, 'utf8');
    
    // 检查是否引入布局工具
    const hasLayoutImport = appJs.includes('getSystemInfo');
    console.log(`布局工具引入: ${hasLayoutImport ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查是否在onLaunch中获取布局信息
    const hasOnLaunchLayout = appJs.includes('this.globalData.layoutInfo = layoutInfo');
    console.log(`onLaunch获取布局: ${hasOnLaunchLayout ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查全局数据中是否有layoutInfo
    const hasGlobalLayoutInfo = appJs.includes('layoutInfo: null');
    console.log(`全局布局数据: ${hasGlobalLayoutInfo ? '✅ 存在' : '❌ 缺失'}`);
    
    return hasLayoutImport && hasOnLaunchLayout && hasGlobalLayoutInfo;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查首页使用px单位
function checkHomePxUsage() {
  console.log('\n📱 检查首页px单位使用:');
  console.log('='.repeat(50));
  
  try {
    const homeWxmlPath = 'pages/home/<USER>';
    const homeWxml = fs.readFileSync(homeWxmlPath, 'utf8');
    
    // 检查导航栏高度使用px
    const hasNavbarPx = homeWxml.includes('{{layoutInfo.totalTopHeight}}px');
    console.log(`导航栏高度px: ${hasNavbarPx ? '✅ 使用px' : '❌ 未使用px'}`);
    
    // 检查padding-top使用px
    const hasPaddingTopPx = homeWxml.includes('{{layoutInfo.statusBarHeight}}px');
    console.log(`状态栏padding px: ${hasPaddingTopPx ? '✅ 使用px' : '❌ 未使用px'}`);
    
    // 检查内容区域padding-top使用px
    const hasContentPaddingPx = homeWxml.includes('padding-top: {{layoutInfo.totalTopHeight}}px');
    console.log(`内容区域padding px: ${hasContentPaddingPx ? '✅ 使用px' : '❌ 未使用px'}`);
    
    // 检查导航栏内容高度使用px
    const hasNavContentPx = homeWxml.includes('{{layoutInfo.navBarHeight}}px');
    console.log(`导航栏内容高度px: ${hasNavContentPx ? '✅ 使用px' : '❌ 未使用px'}`);
    
    return hasNavbarPx && hasPaddingTopPx && hasContentPaddingPx && hasNavContentPx;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查首页JS使用全局数据
function checkHomeJsGlobalData() {
  console.log('\n💻 检查首页JS使用全局数据:');
  console.log('='.repeat(50));
  
  try {
    const homeJsPath = 'pages/home/<USER>';
    const homeJs = fs.readFileSync(homeJsPath, 'utf8');
    
    // 检查是否获取app实例
    const hasGetApp = homeJs.includes('const app = getApp()');
    console.log(`获取app实例: ${hasGetApp ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查是否使用全局布局信息
    const hasGlobalLayoutInfo = homeJs.includes('app.globalData.layoutInfo');
    console.log(`使用全局布局信息: ${hasGlobalLayoutInfo ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查是否移除了本地获取布局信息的代码
    const hasLocalLayoutInfo = homeJs.includes('getPageLayoutInfo()');
    console.log(`移除本地获取: ${!hasLocalLayoutInfo ? '✅ 已移除' : '❌ 仍存在'}`);
    
    return hasGetApp && hasGlobalLayoutInfo && !hasLocalLayoutInfo;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查样式优化
function checkStyleOptimization() {
  console.log('\n🎨 检查样式优化:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查导航栏fixed定位
    const hasFixedPosition = homeLess.includes('position: fixed');
    console.log(`导航栏fixed定位: ${hasFixedPosition ? '✅ 使用' : '❌ 未使用'}`);
    
    // 检查navbar-content样式
    const hasNavbarContent = homeLess.includes('.navbar-content');
    console.log(`导航栏内容容器: ${hasNavbarContent ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查是否移除了固定高度
    const hasFixedHeight = homeLess.includes('height: 88rpx');
    console.log(`移除固定高度: ${!hasFixedHeight ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查动态高度注释
    const hasDynamicComment = homeLess.includes('高度通过动态样式设置');
    console.log(`动态高度注释: ${hasDynamicComment ? '✅ 已添加' : '❌ 未添加'}`);
    
    return hasFixedPosition && hasNavbarContent && !hasFixedHeight && hasDynamicComment;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 生成网络解决方案验证报告
function generateNetworkSolutionReport(results) {
  console.log('\n📊 网络解决方案验证报告:');
  console.log('='.repeat(50));
  
  const { keyFormula, globalLayout, pxUsage, homeJsGlobal, styleOptimization } = results;
  
  console.log(`关键公式实现: ${keyFormula ? '✅ 正确' : '❌ 错误'}`);
  console.log(`全局布局信息: ${globalLayout ? '✅ 正确' : '❌ 错误'}`);
  console.log(`px单位使用: ${pxUsage ? '✅ 正确' : '❌ 错误'}`);
  console.log(`首页JS全局数据: ${homeJsGlobal ? '✅ 正确' : '❌ 错误'}`);
  console.log(`样式优化: ${styleOptimization ? '✅ 正确' : '❌ 错误'}`);
  
  const allPassed = keyFormula && globalLayout && pxUsage && homeJsGlobal && styleOptimization;
  
  if (allPassed) {
    console.log('\n🎉 网络解决方案验证通过！');
    console.log('\n✨ 实现特点:');
    console.log('  📐 使用网络最佳实践的关键公式');
    console.log('  🌍 在app.js中全局获取布局信息');
    console.log('  📱 所有尺寸使用px单位，不使用rpx');
    console.log('  💻 页面使用全局布局数据');
    console.log('  🎨 样式完全基于动态计算');
    
    console.log('\n📐 关键公式:');
    console.log('  导航栏高度 = (胶囊距顶部距离 - 状态栏高度) * 2 + 胶囊高度');
    console.log('  总顶部高度 = 状态栏高度 + 导航栏高度');
    console.log('  胶囊底部位置 = 胶囊top + 胶囊高度');
    
    console.log('\n📱 布局结构:');
    console.log('  .custom-navbar (fixed, 动态高度px)');
    console.log('  ├─ padding-top: 状态栏高度px');
    console.log('  └─ .navbar-content (动态高度px)');
    console.log('      ├─ location-selector');
    console.log('      └─ search-container');
    console.log('  .home-container (padding-top: 总顶部高度px)');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查导航栏是否正确显示');
    console.log('  3. 验证在不同设备上的适配效果');
    console.log('  4. 确认与胶囊按钮对齐');
    console.log('  5. 测试所有交互功能');
  } else {
    console.log('\n⚠️  部分验证未通过');
    
    console.log('\n🔧 修复建议:');
    if (!keyFormula) console.log('  - 实现正确的关键公式');
    if (!globalLayout) console.log('  - 在app.js中全局获取布局信息');
    if (!pxUsage) console.log('  - 所有尺寸使用px单位');
    if (!homeJsGlobal) console.log('  - 首页使用全局布局数据');
    if (!styleOptimization) console.log('  - 优化样式配置');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    keyFormula: checkKeyFormula(),
    globalLayout: checkGlobalLayoutInfo(),
    pxUsage: checkHomePxUsage(),
    homeJsGlobal: checkHomeJsGlobalData(),
    styleOptimization: checkStyleOptimization()
  };
  
  generateNetworkSolutionReport(results);
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = { 
  checkKeyFormula, 
  checkGlobalLayoutInfo, 
  checkHomePxUsage,
  checkHomeJsGlobalData,
  checkStyleOptimization 
};
