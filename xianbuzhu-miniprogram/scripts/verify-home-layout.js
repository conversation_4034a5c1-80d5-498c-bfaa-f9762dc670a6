#!/usr/bin/env node

/**
 * 主页布局验证脚本
 * 验证主页布局修复是否正确
 */

const fs = require('fs');

console.log('🏠 验证主页布局修复...\n');

// 检查主页WXML结构
function checkHomeWxmlStructure() {
  console.log('📄 检查主页WXML结构:');
  console.log('='.repeat(50));
  
  try {
    const homeWxmlPath = 'pages/home/<USER>';
    const homeWxml = fs.readFileSync(homeWxmlPath, 'utf8');
    
    // 检查是否有区域选择器
    const hasLocationSelector = homeWxml.includes('<location-selector');
    console.log(`区域选择器: ${hasLocationSelector ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查是否有搜索框
    const hasSearchBox = homeWxml.includes('search-box');
    console.log(`搜索框: ${hasSearchBox ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查是否移除了消息图标
    const hasMessageIcon = homeWxml.includes('message-icon');
    console.log(`消息图标: ${!hasMessageIcon ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查是否有自定义导航栏
    const hasCustomNavbar = homeWxml.includes('custom-navbar');
    console.log(`自定义导航栏: ${hasCustomNavbar ? '✅ 存在' : '❌ 缺失'}`);
    
    return hasLocationSelector && hasSearchBox && !hasMessageIcon && hasCustomNavbar;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查主页JS逻辑
function checkHomeJsLogic() {
  console.log('\n💻 检查主页JS逻辑:');
  console.log('='.repeat(50));
  
  try {
    const homeJsPath = 'pages/home/<USER>';
    const homeJs = fs.readFileSync(homeJsPath, 'utf8');
    
    // 检查是否移除了unreadCount数据
    const hasUnreadCount = homeJs.includes('unreadCount:');
    console.log(`未读消息数据: ${!hasUnreadCount ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查是否移除了goToMessage方法
    const hasGoToMessage = homeJs.includes('goToMessage');
    console.log(`消息跳转方法: ${!hasGoToMessage ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查是否有区域选择处理
    const hasLocationChange = homeJs.includes('onLocationChange');
    console.log(`区域选择处理: ${hasLocationChange ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查是否有搜索跳转
    const hasGoToSearch = homeJs.includes('goToSearch');
    console.log(`搜索跳转方法: ${hasGoToSearch ? '✅ 存在' : '❌ 缺失'}`);
    
    return !hasUnreadCount && !hasGoToMessage && hasLocationChange && hasGoToSearch;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查主页样式
function checkHomeStyles() {
  console.log('\n🎨 检查主页样式:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查是否移除了消息图标样式
    const hasMessageIconStyle = homeLess.includes('.message-icon');
    console.log(`消息图标样式: ${!hasMessageIconStyle ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查导航栏样式
    const hasCustomNavbarStyle = homeLess.includes('.custom-navbar');
    console.log(`自定义导航栏样式: ${hasCustomNavbarStyle ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查搜索框样式
    const hasSearchBoxStyle = homeLess.includes('.search-box');
    console.log(`搜索框样式: ${hasSearchBoxStyle ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查导航栏高度
    const minHeightMatch = homeLess.match(/min-height:\s*(\d+)rpx/);
    const minHeight = minHeightMatch ? parseInt(minHeightMatch[1]) : 0;
    console.log(`导航栏最小高度: ${minHeight}rpx ${minHeight >= 96 ? '✅ 足够' : '❌ 过小'}`);
    
    return !hasMessageIconStyle && hasCustomNavbarStyle && hasSearchBoxStyle && minHeight >= 96;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查TabBar消息配置
function checkTabBarMessage() {
  console.log('\n📋 检查TabBar消息配置:');
  console.log('='.repeat(50));
  
  try {
    // 检查自定义TabBar
    const tabBarJsPath = 'custom-tab-bar/index.js';
    if (!fs.existsSync(tabBarJsPath)) {
      console.log('❌ 自定义TabBar文件不存在');
      return false;
    }
    
    const tabBarJs = fs.readFileSync(tabBarJsPath, 'utf8');
    
    // 检查是否有未读消息数量
    const hasUnreadNum = tabBarJs.includes('unreadNum');
    console.log(`TabBar未读数量: ${hasUnreadNum ? '✅ 支持' : '❌ 不支持'}`);
    
    // 检查是否有设置未读数量的方法
    const hasSetUnreadNum = tabBarJs.includes('setUnreadNum');
    console.log(`设置未读数量方法: ${hasSetUnreadNum ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查TabBar WXML
    const tabBarWxmlPath = 'custom-tab-bar/index.wxml';
    if (fs.existsSync(tabBarWxmlPath)) {
      const tabBarWxml = fs.readFileSync(tabBarWxmlPath, 'utf8');
      
      // 检查是否有徽章配置
      const hasBadgeProps = tabBarWxml.includes('badge-props');
      console.log(`TabBar徽章配置: ${hasBadgeProps ? '✅ 已配置' : '❌ 未配置'}`);
      
      return hasUnreadNum && hasSetUnreadNum && hasBadgeProps;
    }
    
    return hasUnreadNum && hasSetUnreadNum;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查全局消息管理
function checkGlobalMessageManagement() {
  console.log('\n🌐 检查全局消息管理:');
  console.log('='.repeat(50));
  
  try {
    const appJsPath = 'app.js';
    const appJs = fs.readFileSync(appJsPath, 'utf8');
    
    // 检查全局数据中是否有未读消息数量
    const hasGlobalUnreadNum = appJs.includes('unreadNum:');
    console.log(`全局未读数量: ${hasGlobalUnreadNum ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查是否有获取未读消息数量的方法
    const hasGetUnreadNum = appJs.includes('getUnreadNum');
    console.log(`获取未读数量方法: ${hasGetUnreadNum ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查是否有设置未读消息数量的方法
    const hasSetUnreadNum = appJs.includes('setUnreadNum');
    console.log(`设置未读数量方法: ${hasSetUnreadNum ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查是否有事件总线
    const hasEventBus = appJs.includes('eventBus');
    console.log(`事件总线: ${hasEventBus ? '✅ 存在' : '❌ 缺失'}`);
    
    return hasGlobalUnreadNum && hasGetUnreadNum && hasSetUnreadNum && hasEventBus;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 生成主页布局验证报告
function generateHomeLayoutReport(results) {
  console.log('\n📊 主页布局验证报告:');
  console.log('='.repeat(50));
  
  const { wxml, js, styles, tabBar, globalMessage } = results;
  
  console.log(`WXML结构: ${wxml ? '✅ 正确' : '❌ 错误'}`);
  console.log(`JS逻辑: ${js ? '✅ 正确' : '❌ 错误'}`);
  console.log(`样式配置: ${styles ? '✅ 正确' : '❌ 错误'}`);
  console.log(`TabBar消息: ${tabBar ? '✅ 正确' : '❌ 错误'}`);
  console.log(`全局消息管理: ${globalMessage ? '✅ 正确' : '❌ 错误'}`);
  
  const allPassed = wxml && js && styles && tabBar && globalMessage;
  
  if (allPassed) {
    console.log('\n🎉 主页布局修复验证通过！');
    console.log('\n✨ 修复效果:');
    console.log('  📱 主页右上角不再有消息图标，避免与小程序自带图标冲突');
    console.log('  📍 区域选择器正常显示在导航栏左侧');
    console.log('  🔍 搜索框正常显示在导航栏右侧，占据更多空间');
    console.log('  📋 TabBar消息栏显示未读消息数量徽章');
    console.log('  🌐 全局消息数量管理正常工作');
    console.log('  🎨 导航栏布局简洁美观，不会被遮挡');
    
    console.log('\n📱 布局结构:');
    console.log('  ┌─────────────────────────────────┐');
    console.log('  │ 📶 状态栏 (系统)                │');
    console.log('  ├─────────────────────────────────┤');
    console.log('  │ 📍阳光花园    🔍搜索物品        │ ← 自定义导航栏');
    console.log('  ├─────────────────────────────────┤');
    console.log('  │        页面内容区域              │');
    console.log('  │                                 │');
    console.log('  ├─────────────────────────────────┤');
    console.log('  │ 首页    消息(3)    我的         │ ← TabBar');
    console.log('  └─────────────────────────────────┘');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查主页导航栏是否只有区域选择器和搜索框');
    console.log('  3. 验证TabBar消息栏是否显示未读数量');
    console.log('  4. 测试区域选择和搜索功能');
    console.log('  5. 确认不与小程序自带图标冲突');
  } else {
    console.log('\n⚠️  部分验证未通过');
    
    console.log('\n🔧 修复建议:');
    if (!wxml) console.log('  - 检查主页WXML结构，确保移除消息图标');
    if (!js) console.log('  - 检查主页JS逻辑，移除消息相关代码');
    if (!styles) console.log('  - 检查主页样式，移除消息图标样式');
    if (!tabBar) console.log('  - 检查TabBar消息配置');
    if (!globalMessage) console.log('  - 检查全局消息管理');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    wxml: checkHomeWxmlStructure(),
    js: checkHomeJsLogic(),
    styles: checkHomeStyles(),
    tabBar: checkTabBarMessage(),
    globalMessage: checkGlobalMessageManagement()
  };
  
  generateHomeLayoutReport(results);
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = { 
  checkHomeWxmlStructure, 
  checkHomeJsLogic, 
  checkHomeStyles,
  checkTabBarMessage,
  checkGlobalMessageManagement 
};
