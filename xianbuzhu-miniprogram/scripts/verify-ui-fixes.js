#!/usr/bin/env node

/**
 * UI修复验证脚本
 * 验证发布按钮图标和消息栏未读数显示修复
 */

const fs = require('fs');

console.log('🔧 验证UI修复...\n');

// 检查发布按钮图标修复
function checkReleaseButtonIcon() {
  console.log('🚀 检查发布按钮图标修复:');
  console.log('='.repeat(50));
  
  try {
    const homeWxmlPath = 'pages/home/<USER>';
    const homeWxml = fs.readFileSync(homeWxmlPath, 'utf8');
    
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查是否使用自定义图标
    const hasCustomIcon = homeWxml.includes('<custom-icon name="add"');
    console.log(`使用自定义图标: ${hasCustomIcon ? '✅ 已使用' : '❌ 未使用'}`);
    
    // 检查是否有release-content容器
    const hasReleaseContent = homeWxml.includes('class="release-content"');
    console.log(`发布内容容器: ${hasReleaseContent ? '✅ 已添加' : '❌ 未添加'}`);
    
    // 检查图标尺寸和颜色
    const hasCorrectIconProps = homeWxml.includes('size="32rpx" color="#fff"');
    console.log(`图标属性正确: ${hasCorrectIconProps ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查样式定义
    const hasReleaseContentStyle = homeLess.includes('.release-content');
    console.log(`发布内容样式: ${hasReleaseContentStyle ? '✅ 已定义' : '❌ 未定义'}`);
    
    // 检查flex布局
    const hasFlexLayout = homeLess.includes('display: flex') && homeLess.includes('align-items: center');
    console.log(`Flex布局设置: ${hasFlexLayout ? '✅ 已设置' : '❌ 未设置'}`);
    
    // 检查间距设置
    const hasGap = homeLess.includes('gap: 8rpx');
    console.log(`图标文字间距: ${hasGap ? '✅ 已设置' : '❌ 未设置'}`);
    
    return hasCustomIcon && hasReleaseContent && hasCorrectIconProps && 
           hasReleaseContentStyle && hasFlexLayout && hasGap;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查消息栏未读数显示
function checkMessageUnreadBadge() {
  console.log('\n💬 检查消息栏未读数显示:');
  console.log('='.repeat(50));
  
  try {
    const messageWxmlPath = 'pages/message/index.wxml';
    const messageWxml = fs.readFileSync(messageWxmlPath, 'utf8');
    
    const messageLessPath = 'pages/message/index.less';
    const messageLess = fs.readFileSync(messageLessPath, 'utf8');
    
    // 检查头像区域结构
    const hasAvatarSection = messageWxml.includes('class="avatar-section"');
    console.log(`头像区域容器: ${hasAvatarSection ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查徽章组件
    const hasBadgeComponent = messageWxml.includes('<t-badge');
    console.log(`徽章组件: ${hasBadgeComponent ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查偏移设置
    const hasOffset = messageWxml.includes('offset="{{[-8, 8]}}"');
    console.log(`徽章偏移设置: ${hasOffset ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查条件显示
    const hasConditionalDisplay = messageWxml.includes('wx:if="{{ utils.computeUnreadNum(item.messages) > 0 }}"');
    console.log(`条件显示逻辑: ${hasConditionalDisplay ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查头像区域样式
    const hasAvatarSectionStyle = messageLess.includes('.avatar-section');
    console.log(`头像区域样式: ${hasAvatarSectionStyle ? '✅ 已定义' : '❌ 未定义'}`);
    
    // 检查相对定位
    const hasRelativePosition = messageLess.includes('position: relative');
    console.log(`相对定位设置: ${hasRelativePosition ? '✅ 已设置' : '❌ 未设置'}`);
    
    // 检查右边距
    const hasMarginRight = messageLess.includes('margin-right: 24rpx');
    console.log(`头像右边距: ${hasMarginRight ? '✅ 已设置' : '❌ 未设置'}`);
    
    // 检查不压缩设置
    const hasFlexShrink = messageLess.includes('flex-shrink: 0');
    console.log(`防止压缩设置: ${hasFlexShrink ? '✅ 已设置' : '❌ 未设置'}`);
    
    return hasAvatarSection && hasBadgeComponent && hasOffset && hasConditionalDisplay &&
           hasAvatarSectionStyle && hasRelativePosition && hasMarginRight && hasFlexShrink;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查内容区域布局
function checkContentAreaLayout() {
  console.log('\n📄 检查内容区域布局:');
  console.log('='.repeat(50));
  
  try {
    const messageLessPath = 'pages/message/index.less';
    const messageLess = fs.readFileSync(messageLessPath, 'utf8');
    
    // 检查内容区域样式
    const hasContentSection = messageLess.includes('.content-section');
    console.log(`内容区域样式: ${hasContentSection ? '✅ 已定义' : '❌ 未定义'}`);
    
    // 检查flex: 1设置
    const hasFlexOne = messageLess.includes('flex: 1');
    console.log(`内容区域flex: 1: ${hasFlexOne ? '✅ 已设置' : '❌ 未设置'}`);
    
    // 检查最小宽度设置
    const hasMinWidth = messageLess.includes('min-width: 0');
    console.log(`最小宽度设置: ${hasMinWidth ? '✅ 已设置' : '❌ 未设置'}`);
    
    console.log('\n布局分析:');
    console.log('  头像区域: position: relative + margin-right: 24rpx');
    console.log('  徽章: 使用offset偏移，嵌入头像右上角');
    console.log('  内容区域: flex: 1，占据剩余空间');
    console.log('  效果: 未读数不占用右侧内容空间');
    
    return hasContentSection && hasFlexOne && hasMinWidth;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查整体布局效果
function checkOverallLayoutEffect() {
  console.log('\n🎨 检查整体布局效果:');
  console.log('='.repeat(50));
  
  console.log('发布按钮效果:');
  console.log('  ┌─────────────┐');
  console.log('  │ ➕ 发布    │ ← 图标 + 文字');
  console.log('  └─────────────┘');
  
  console.log('\n消息列表效果:');
  console.log('  ┌─────────────────────────────────┐');
  console.log('  │ 👤③  张三        2分钟前      │ ← 徽章嵌入头像');
  console.log('  │      你好，这个还在吗？         │   不占用右侧空间');
  console.log('  ├─────────────────────────────────┤');
  console.log('  │ 👤①  李四        5分钟前      │');
  console.log('  │      好的，谢谢！               │');
  console.log('  └─────────────────────────────────┘');
  
  return true;
}

// 生成UI修复验证报告
function generateUIFixesReport(results) {
  console.log('\n📊 UI修复验证报告:');
  console.log('='.repeat(50));
  
  const { releaseButton, messageBadge, contentLayout, overallEffect } = results;
  
  console.log(`发布按钮图标: ${releaseButton ? '✅ 修复完成' : '❌ 需要修复'}`);
  console.log(`消息未读徽章: ${messageBadge ? '✅ 正确显示' : '❌ 显示错误'}`);
  console.log(`内容区域布局: ${contentLayout ? '✅ 布局正确' : '❌ 布局错误'}`);
  console.log(`整体效果: ${overallEffect ? '✅ 效果良好' : '❌ 需要改进'}`);
  
  const allPassed = releaseButton && messageBadge && contentLayout && overallEffect;
  
  if (allPassed) {
    console.log('\n🎉 UI修复验证通过！');
    console.log('\n✨ 修复效果:');
    console.log('  🚀 发布按钮显示自定义图标，图标和文字正确排列');
    console.log('  💬 消息未读数嵌入头像右上角，不占用右侧内容空间');
    console.log('  📄 内容区域布局合理，文字显示完整');
    console.log('  🎨 整体视觉效果美观，用户体验良好');
    
    console.log('\n🔧 技术实现:');
    console.log('  发布按钮:');
    console.log('    - 使用custom-icon组件替代TDesign图标');
    console.log('    - flex布局排列图标和文字');
    console.log('    - 8rpx间距，32rpx图标尺寸');
    console.log('  消息徽章:');
    console.log('    - t-badge组件 + offset偏移');
    console.log('    - 头像区域position: relative');
    console.log('    - 内容区域flex: 1自适应');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查发布按钮是否显示图标');
    console.log('  3. 验证消息列表未读数显示位置');
    console.log('  4. 确认内容区域不被挤压');
    console.log('  5. 测试各种消息数量的显示效果');
  } else {
    console.log('\n⚠️  部分修复验证未通过');
    
    console.log('\n🔧 修复建议:');
    if (!releaseButton) console.log('  - 修复发布按钮图标显示');
    if (!messageBadge) console.log('  - 调整消息未读徽章位置');
    if (!contentLayout) console.log('  - 优化内容区域布局');
    if (!overallEffect) console.log('  - 改进整体视觉效果');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    releaseButton: checkReleaseButtonIcon(),
    messageBadge: checkMessageUnreadBadge(),
    contentLayout: checkContentAreaLayout(),
    overallEffect: checkOverallLayoutEffect()
  };
  
  generateUIFixesReport(results);
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = { 
  checkReleaseButtonIcon, 
  checkMessageUnreadBadge, 
  checkContentAreaLayout,
  checkOverallLayoutEffect 
};
