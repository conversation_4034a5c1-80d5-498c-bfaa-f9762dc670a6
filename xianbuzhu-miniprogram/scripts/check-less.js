#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 检查LESS文件中的变量使用
function checkLessVariables() {
  console.log('🔍 检查LESS变量定义和使用...\n');
  
  // 读取variable.less文件
  const variablePath = 'variable.less';
  if (!fs.existsSync(variablePath)) {
    console.error('❌ variable.less文件不存在');
    return;
  }
  
  const variableContent = fs.readFileSync(variablePath, 'utf8');
  
  // 提取定义的变量
  const definedVariables = new Set();
  const variableMatches = variableContent.match(/@[a-zA-Z0-9-]+(?=\s*:)/g);
  if (variableMatches) {
    variableMatches.forEach(v => definedVariables.add(v));
  }
  
  console.log('📋 已定义的变量:');
  Array.from(definedVariables).sort().forEach(v => {
    console.log(`  ${v}`);
  });
  
  // 检查所有LESS文件中使用的变量
  const lessFiles = [
    'pages/item-detail/index.less',
    'pages/user-profile/index.less', 
    'pages/my-items/index.less',
    'pages/release/index.less',
    'components/card/index.less'
  ];
  
  console.log('\n🔍 检查变量使用:');
  
  const usedVariables = new Set();
  const undefinedVariables = new Set();
  
  lessFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 提取使用的变量（排除@import）
      const usedMatches = content.match(/@[a-zA-Z0-9-]+(?!\s*['"])/g);
      if (usedMatches) {
        usedMatches.forEach(v => {
          if (v !== '@import') {
            usedVariables.add(v);
            if (!definedVariables.has(v)) {
              undefinedVariables.add(v);
              console.log(`  ❌ ${filePath}: 未定义变量 ${v}`);
            }
          }
        });
      }
      
      console.log(`  ✅ ${filePath}: 检查完成`);
    } else {
      console.log(`  ⚠️  ${filePath}: 文件不存在`);
    }
  });
  
  console.log('\n📊 统计结果:');
  console.log(`  定义的变量数: ${definedVariables.size}`);
  console.log(`  使用的变量数: ${usedVariables.size}`);
  console.log(`  未定义的变量数: ${undefinedVariables.size}`);
  
  if (undefinedVariables.size > 0) {
    console.log('\n❌ 未定义的变量:');
    Array.from(undefinedVariables).sort().forEach(v => {
      console.log(`  ${v}`);
    });
    
    console.log('\n💡 建议在variable.less中添加这些变量定义');
  } else {
    console.log('\n✅ 所有变量都已正确定义！');
  }
  
  // 检查LESS语法
  console.log('\n🔍 检查LESS语法问题...');
  
  lessFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        const lineNum = index + 1;
        
        // 检查常见语法问题
        if (line.includes('//') && !line.trim().startsWith('//')) {
          // 检查是否在CSS属性中使用了//注释
          if (line.includes(':') && line.indexOf('//') > line.indexOf(':')) {
            console.log(`  ⚠️  ${filePath}:${lineNum} - 可能的注释问题: ${line.trim()}`);
          }
        }
        
        // 检查未闭合的括号
        const openBraces = (line.match(/{/g) || []).length;
        const closeBraces = (line.match(/}/g) || []).length;
        if (openBraces !== closeBraces && (openBraces > 0 || closeBraces > 0)) {
          // 这是正常的，因为括号可能跨行
        }
        
        // 检查分号问题
        if (line.includes(':') && !line.includes('{') && !line.includes('}') && 
            !line.trim().endsWith(';') && !line.trim().endsWith(',') && 
            line.trim() !== '' && !line.trim().startsWith('//')) {
          console.log(`  ⚠️  ${filePath}:${lineNum} - 可能缺少分号: ${line.trim()}`);
        }
      });
    }
  });
  
  console.log('\n🎯 修复建议:');
  console.log('  1. 确保所有使用的变量都在variable.less中定义');
  console.log('  2. 检查LESS语法是否正确');
  console.log('  3. 在微信开发者工具中清除缓存并重新编译');
  console.log('  4. 确保@import路径正确');
}

// 运行检查
if (require.main === module) {
  checkLessVariables();
}

module.exports = { checkLessVariables };
