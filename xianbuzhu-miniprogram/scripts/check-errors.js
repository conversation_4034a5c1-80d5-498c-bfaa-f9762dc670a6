#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 错误检查配置
const CHECK_CONFIG = {
  // 需要检查的文件扩展名
  extensions: ['.js', '.wxml', '.json', '.wxss', '.less'],
  // 排除的目录
  excludeDirs: ['node_modules', 'miniprogram_npm', '.git'],
  // 常见错误模式
  errorPatterns: {
    // JavaScript语法错误
    js: [
      /console\.log\(/g, // 遗留的console.log
      /debugger;?/g, // 遗留的debugger
      /\bvar\s+/g, // 使用var而不是let/const
      /==\s*null/g, // 使用==而不是===
      /!=\s*null/g, // 使用!=而不是!==
      /function\s*\(\s*\)\s*{[^}]*}/g, // 空函数
    ],
    // WXML模板错误
    wxml: [
      /wx:for="{{.*}}".*wx:key="index"/g, // 使用index作为key
      /bindtap="[^"]*"[^>]*data-[^=]*="{{[^}]*}}"/g, // 事件绑定数据传递问题
      /{{.*\..*\..*}}/g, // 过深的数据绑定
    ],
    // JSON配置错误
    json: [
      /,\s*}/g, // JSON尾部逗号
      /,\s*]/g, // 数组尾部逗号
    ]
  }
};

class ErrorChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.stats = {
      filesChecked: 0,
      errorsFound: 0,
      warningsFound: 0
    };
  }

  // 检查单个文件
  checkFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const ext = path.extname(filePath);
      const relativePath = path.relative(process.cwd(), filePath);

      this.stats.filesChecked++;

      // 检查JSON文件语法
      if (ext === '.json') {
        this.checkJsonSyntax(filePath, content, relativePath);
      }

      // 检查JavaScript文件
      if (ext === '.js') {
        this.checkJavaScript(content, relativePath);
      }

      // 检查WXML文件
      if (ext === '.wxml') {
        this.checkWxml(content, relativePath);
      }

      // 检查通用问题
      this.checkCommonIssues(content, relativePath);

    } catch (error) {
      this.addError(filePath, 0, `文件读取失败: ${error.message}`);
    }
  }

  // 检查JSON语法
  checkJsonSyntax(filePath, content, relativePath) {
    try {
      JSON.parse(content);
    } catch (error) {
      this.addError(relativePath, this.getLineNumber(content, error.message), 
        `JSON语法错误: ${error.message}`);
    }

    // 检查JSON特定问题
    const jsonPatterns = CHECK_CONFIG.errorPatterns.json;
    jsonPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const line = this.getLineNumberByContent(content, match);
          this.addWarning(relativePath, line, `JSON格式问题: ${match}`);
        });
      }
    });
  }

  // 检查JavaScript代码
  checkJavaScript(content, relativePath) {
    const jsPatterns = CHECK_CONFIG.errorPatterns.js;
    
    jsPatterns.forEach((pattern, index) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const line = this.getLineNumberByIndex(content, match.index);
        const issue = match[0];
        
        switch (index) {
          case 0: // console.log
            this.addWarning(relativePath, line, `遗留的console.log: ${issue}`);
            break;
          case 1: // debugger
            this.addWarning(relativePath, line, `遗留的debugger语句: ${issue}`);
            break;
          case 2: // var
            this.addWarning(relativePath, line, `建议使用let/const替代var: ${issue}`);
            break;
          case 3: // == null
          case 4: // != null
            this.addWarning(relativePath, line, `建议使用严格相等: ${issue}`);
            break;
          case 5: // empty function
            this.addWarning(relativePath, line, `空函数: ${issue}`);
            break;
        }
        
        // 重置正则表达式的lastIndex以避免无限循环
        pattern.lastIndex = 0;
      }
    });

    // 检查常见的小程序API使用问题
    this.checkMiniProgramAPI(content, relativePath);
  }

  // 检查小程序API使用
  checkMiniProgramAPI(content, relativePath) {
    // 检查wx.navigateTo是否有错误处理
    const navigatePattern = /wx\.navigateTo\s*\(\s*{[^}]*}\s*\)/g;
    let match;
    while ((match = navigatePattern.exec(content)) !== null) {
      const line = this.getLineNumberByIndex(content, match.index);
      if (!match[0].includes('fail') && !match[0].includes('catch')) {
        this.addWarning(relativePath, line, 'wx.navigateTo缺少错误处理');
      }
    }

    // 检查异步函数是否有错误处理
    const asyncPattern = /async\s+function|async\s+\w+\s*=>/g;
    while ((match = asyncPattern.exec(content)) !== null) {
      const line = this.getLineNumberByIndex(content, match.index);
      const functionContent = this.getFunctionContent(content, match.index);
      if (!functionContent.includes('try') && !functionContent.includes('catch')) {
        this.addWarning(relativePath, line, '异步函数缺少错误处理');
      }
    }
  }

  // 检查WXML模板
  checkWxml(content, relativePath) {
    const wxmlPatterns = CHECK_CONFIG.errorPatterns.wxml;
    
    wxmlPatterns.forEach((pattern, index) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const line = this.getLineNumberByIndex(content, match.index);
        const issue = match[0];
        
        switch (index) {
          case 0: // wx:key="index"
            this.addWarning(relativePath, line, `避免使用index作为key: ${issue}`);
            break;
          case 1: // 事件绑定问题
            this.addWarning(relativePath, line, `事件绑定数据传递可能有问题: ${issue}`);
            break;
          case 2: // 过深的数据绑定
            this.addWarning(relativePath, line, `数据绑定层级过深: ${issue}`);
            break;
        }
        
        pattern.lastIndex = 0;
      }
    });
  }

  // 检查通用问题
  checkCommonIssues(content, relativePath) {
    // 检查中文编码问题
    const chinesePattern = /[\u4e00-\u9fa5]/g;
    if (relativePath.endsWith('.js') && chinesePattern.test(content)) {
      // 检查是否在字符串外使用了中文
      const lines = content.split('\n');
      lines.forEach((line, index) => {
        if (chinesePattern.test(line) && !this.isInString(line)) {
          this.addWarning(relativePath, index + 1, '代码中包含中文字符（非字符串）');
        }
      });
    }
  }

  // 辅助方法
  getLineNumber(content, errorMessage) {
    const match = errorMessage.match(/line (\d+)/);
    return match ? parseInt(match[1]) : 1;
  }

  getLineNumberByIndex(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  getLineNumberByContent(content, searchContent) {
    const index = content.indexOf(searchContent);
    return index !== -1 ? this.getLineNumberByIndex(content, index) : 1;
  }

  getFunctionContent(content, startIndex) {
    // 简单的函数内容提取（可以改进）
    const remaining = content.substring(startIndex);
    const braceIndex = remaining.indexOf('{');
    if (braceIndex === -1) return '';
    
    let braceCount = 1;
    let endIndex = braceIndex + 1;
    
    while (braceCount > 0 && endIndex < remaining.length) {
      if (remaining[endIndex] === '{') braceCount++;
      if (remaining[endIndex] === '}') braceCount--;
      endIndex++;
    }
    
    return remaining.substring(0, endIndex);
  }

  isInString(line) {
    // 简单检查是否在字符串内（可以改进）
    const stringPattern = /(['"`]).*?\1/g;
    let match;
    const stringRanges = [];
    
    while ((match = stringPattern.exec(line)) !== null) {
      stringRanges.push([match.index, match.index + match[0].length]);
    }
    
    return stringRanges.length > 0;
  }

  addError(file, line, message) {
    this.errors.push({ file, line, message, type: 'error' });
    this.stats.errorsFound++;
  }

  addWarning(file, line, message) {
    this.warnings.push({ file, line, message, type: 'warning' });
    this.stats.warningsFound++;
  }

  // 递归检查目录
  checkDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        if (!CHECK_CONFIG.excludeDirs.includes(item)) {
          this.checkDirectory(itemPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (CHECK_CONFIG.extensions.includes(ext)) {
          this.checkFile(itemPath);
        }
      }
    });
  }

  // 生成报告
  generateReport() {
    console.log('\n🔍 JavaScript错误检查报告');
    console.log('='.repeat(50));
    
    console.log(`\n📊 统计信息:`);
    console.log(`  检查文件数: ${this.stats.filesChecked}`);
    console.log(`  发现错误数: ${this.stats.errorsFound}`);
    console.log(`  发现警告数: ${this.stats.warningsFound}`);
    
    if (this.errors.length > 0) {
      console.log(`\n❌ 错误 (${this.errors.length}):`);
      this.errors.forEach(error => {
        console.log(`  ${error.file}:${error.line} - ${error.message}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log(`\n⚠️  警告 (${this.warnings.length}):`);
      this.warnings.forEach(warning => {
        console.log(`  ${warning.file}:${warning.line} - ${warning.message}`);
      });
    }
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('\n✅ 未发现明显问题！');
    }
    
    console.log('\n💡 建议:');
    console.log('  1. 在微信开发者工具中查看详细错误信息');
    console.log('  2. 检查控制台(Console)面板的错误日志');
    console.log('  3. 确保所有依赖包已正确安装');
    console.log('  4. 检查TDesign组件的使用是否正确');
  }

  // 运行检查
  run() {
    console.log('🚀 开始检查JavaScript错误...\n');
    this.checkDirectory('.');
    this.generateReport();
  }
}

// 运行检查
if (require.main === module) {
  const checker = new ErrorChecker();
  checker.run();
}

module.exports = ErrorChecker;
