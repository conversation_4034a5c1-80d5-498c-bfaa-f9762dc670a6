#!/usr/bin/env node

/**
 * 消息页面样式修复验证脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 检查消息页面修复状态...\n');

// 检查项目列表
const checks = [
  {
    name: '消息页面WXML结构',
    file: 'pages/message/index.wxml',
    check: (content) => {
      return content.includes('<t-navbar title="全部消息" />') && 
             !content.includes('<nav nav-type="title"');
    },
    fix: '使用TDesign导航栏替代自定义nav组件'
  },
  {
    name: '消息页面样式文件',
    file: 'pages/message/index.less',
    check: (content) => {
      return content.includes('.message-item') && 
             content.includes('.avatar-section') &&
             content.includes('.content-section');
    },
    fix: '完整的消息列表样式定义'
  },
  {
    name: '消息页面组件配置',
    file: 'pages/message/index.json',
    check: (content) => {
      const config = JSON.parse(content);
      return config.usingComponents && 
             config.usingComponents['t-navbar'] &&
             !config.usingComponents['nav'];
    },
    fix: '移除自定义nav组件，使用TDesign组件'
  },
  {
    name: '聊天页面导航栏',
    file: 'pages/chat/index.wxml',
    check: (content) => {
      return content.includes('left-arrow');
    },
    fix: '聊天页面导航栏配置正确'
  },
  {
    name: '全局图标修复',
    file: 'app.wxss',
    check: (content) => {
      return content.includes('.t-navbar .t-icon--chevron-left::before') &&
             content.includes("content: '‹' !important;");
    },
    fix: '导航栏返回图标修复'
  }
];

// 执行检查
function runChecks() {
  console.log('📋 检查结果:');
  console.log('='.repeat(50));
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  
  checks.forEach((check, index) => {
    try {
      if (!fs.existsSync(check.file)) {
        console.log(`❌ ${index + 1}. ${check.name}: 文件不存在`);
        return;
      }
      
      const content = fs.readFileSync(check.file, 'utf8');
      const passed = check.check(content);
      
      if (passed) {
        console.log(`✅ ${index + 1}. ${check.name}: 通过`);
        passedChecks++;
      } else {
        console.log(`❌ ${index + 1}. ${check.name}: 需要修复`);
        console.log(`   修复内容: ${check.fix}`);
      }
      
    } catch (error) {
      console.log(`❌ ${index + 1}. ${check.name}: 检查失败 - ${error.message}`);
    }
  });
  
  console.log('\n📊 检查统计:');
  console.log(`通过: ${passedChecks}/${totalChecks}`);
  console.log(`失败: ${totalChecks - passedChecks}/${totalChecks}`);
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 所有检查都通过了！');
    console.log('\n🚀 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查消息页面样式是否正常');
    console.log('  3. 测试聊天页面返回图标是否正确显示');
  } else {
    console.log('\n⚠️  部分检查未通过，请查看上述修复建议');
  }
}

// 提供修复建议
function showFixSuggestions() {
  console.log('\n💡 修复建议:');
  console.log('='.repeat(50));
  
  console.log('\n1. 消息页面样式问题:');
  console.log('   - 确保使用了完整的LESS样式文件');
  console.log('   - 检查@import变量是否正确导入');
  console.log('   - 验证TDesign组件是否正确引用');
  
  console.log('\n2. 返回图标问题:');
  console.log('   - 检查app.wxss中的图标修复代码');
  console.log('   - 确保字体修复没有影响导航栏图标');
  console.log('   - 可以使用自定义图标组件替代');
  
  console.log('\n3. 通用解决方案:');
  console.log('   - 清除微信开发者工具缓存');
  console.log('   - 重新编译项目');
  console.log('   - 检查控制台错误信息');
  
  console.log('\n4. 如果问题仍然存在:');
  console.log('   - 使用自定义图标: <custom-icon name="chevron-left" />');
  console.log('   - 直接使用Unicode字符: <text>‹</text>');
  console.log('   - 检查TDesign组件版本兼容性');
}

// 主函数
function main() {
  runChecks();
  showFixSuggestions();
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { runChecks, showFixSuggestions };
