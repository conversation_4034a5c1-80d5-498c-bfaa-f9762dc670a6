#!/usr/bin/env node

/**
 * 消息页面布局验证脚本
 * 检查消息页面的布局和标题配置是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 检查消息页面布局配置...\n');

// 检查项目列表
const checks = [
  {
    name: '消息页面WXML结构',
    file: 'pages/message/index.wxml',
    check: (content) => {
      return content.includes('<nav nav-type="title" title-text="消息"></nav>') && 
             !content.includes('<t-navbar');
    },
    fix: '使用自定义nav组件，标题显示为"消息"'
  },
  {
    name: '消息页面JSON配置',
    file: 'pages/message/index.json',
    check: (content) => {
      const config = JSON.parse(content);
      return config.navigationStyle === 'custom' && 
             config.usingComponents && 
             config.usingComponents['nav'] === '/components/nav' &&
             config.disableScroll === true;
    },
    fix: '配置自定义导航栏和nav组件'
  },
  {
    name: '消息页面样式布局',
    file: 'pages/message/index.less',
    check: (content) => {
      return content.includes('display: flex') && 
             content.includes('flex-direction: column') &&
             content.includes('height: 100vh') &&
             content.includes('flex-grow: 1');
    },
    fix: '使用flex布局，确保从顶部开始显示'
  },
  {
    name: '页面标题一致性',
    file: 'pages/message/index.wxml',
    check: (content) => {
      return content.includes('title-text="消息"') && 
             !content.includes('全部消息') &&
             !content.includes('小程序名称');
    },
    fix: '标题显示为"消息"，不显示小程序名称'
  }
];

// 执行检查
function runChecks() {
  console.log('📋 检查结果:');
  console.log('='.repeat(50));
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  
  checks.forEach((check, index) => {
    try {
      if (!fs.existsSync(check.file)) {
        console.log(`❌ ${index + 1}. ${check.name}: 文件不存在`);
        return;
      }
      
      const content = fs.readFileSync(check.file, 'utf8');
      const passed = check.check(content);
      
      if (passed) {
        console.log(`✅ ${index + 1}. ${check.name}: 通过`);
        passedChecks++;
      } else {
        console.log(`❌ ${index + 1}. ${check.name}: 需要修复`);
        console.log(`   修复内容: ${check.fix}`);
      }
      
    } catch (error) {
      console.log(`❌ ${index + 1}. ${check.name}: 检查失败 - ${error.message}`);
    }
  });
  
  console.log('\n📊 检查统计:');
  console.log(`通过: ${passedChecks}/${totalChecks}`);
  console.log(`失败: ${totalChecks - passedChecks}/${totalChecks}`);
  
  return passedChecks === totalChecks;
}

// 检查其他页面的一致性
function checkConsistency() {
  console.log('\n🔍 检查页面一致性:');
  console.log('='.repeat(50));
  
  const pages = [
    { name: '首页', path: 'pages/home/<USER>' },
    { name: '我的页面', path: 'pages/my/index.json' },
    { name: '消息页面', path: 'pages/message/index.json' }
  ];
  
  pages.forEach(page => {
    try {
      if (fs.existsSync(page.path)) {
        const config = JSON.parse(fs.readFileSync(page.path, 'utf8'));
        const hasCustomNav = config.navigationStyle === 'custom';
        const hasNavComponent = config.usingComponents && config.usingComponents['nav'];
        
        console.log(`  ${page.name}:`);
        console.log(`    自定义导航栏: ${hasCustomNav ? '✅' : '❌'}`);
        console.log(`    nav组件: ${hasNavComponent ? '✅' : '❌'}`);
      } else {
        console.log(`  ${page.name}: 文件不存在`);
      }
    } catch (error) {
      console.log(`  ${page.name}: 检查失败 - ${error.message}`);
    }
  });
}

// 提供布局修复建议
function showLayoutAdvice() {
  console.log('\n💡 布局修复建议:');
  console.log('='.repeat(50));
  
  console.log('\n1. 页面布局结构:');
  console.log('   - 使用flex布局确保内容从顶部开始');
  console.log('   - 设置height: 100vh确保全屏显示');
  console.log('   - 使用flex-grow: 1让内容区域自适应');
  
  console.log('\n2. 导航栏配置:');
  console.log('   - 使用自定义nav组件保持与其他页面一致');
  console.log('   - 设置navigationStyle: "custom"');
  console.log('   - 标题显示为"消息"而不是小程序名称');
  
  console.log('\n3. 样式优化:');
  console.log('   - 确保消息列表容器正确设置');
  console.log('   - 添加适当的padding和margin');
  console.log('   - 处理底部安全区域');
  
  console.log('\n4. 功能一致性:');
  console.log('   - 保持与首页、我的页面相同的导航栏风格');
  console.log('   - 确保页面切换动画流畅');
  console.log('   - 维护统一的用户体验');
}

// 生成修复报告
function generateReport(allPassed) {
  console.log('\n📊 修复结果报告:');
  console.log('='.repeat(50));
  
  if (allPassed) {
    console.log('\n🎉 所有检查都通过了！');
    console.log('\n🚀 预期效果:');
    console.log('  ✅ 消息页面从顶部开始显示');
    console.log('  ✅ 标题栏显示"消息"而不是小程序名称');
    console.log('  ✅ 布局与其他页面保持一致');
    console.log('  ✅ 消息列表正常显示和滚动');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查消息页面布局是否正常');
    console.log('  3. 测试页面滚动和交互功能');
    console.log('  4. 验证与其他页面的一致性');
  } else {
    console.log('\n⚠️  部分检查未通过，请查看上述修复建议');
    console.log('\n🔧 手动修复步骤:');
    console.log('  1. 检查WXML中的nav组件配置');
    console.log('  2. 确认JSON中的navigationStyle设置');
    console.log('  3. 验证LESS样式的flex布局');
    console.log('  4. 测试页面显示效果');
  }
}

// 主函数
function main() {
  const allPassed = runChecks();
  checkConsistency();
  showLayoutAdvice();
  generateReport(allPassed);
}

// 运行检查脚本
if (require.main === module) {
  main();
}

module.exports = { runChecks, checkConsistency };
