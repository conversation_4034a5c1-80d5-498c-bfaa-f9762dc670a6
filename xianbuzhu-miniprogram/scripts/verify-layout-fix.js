#!/usr/bin/env node

/**
 * 布局修复验证脚本
 * 验证页面布局修复是否正确
 */

const fs = require('fs');

console.log('🔧 验证页面布局修复...\n');

// 检查首页布局修复
function checkHomePageLayoutFix() {
  console.log('🏠 检查首页布局修复:');
  console.log('='.repeat(50));
  
  try {
    // 检查首页LESS
    const homeLessPath = 'pages/home/<USER>';
    if (!fs.existsSync(homeLessPath)) {
      console.log('❌ 首页样式文件不存在');
      return false;
    }
    
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查自定义导航栏样式
    const hasCustomNavbar = homeLess.includes('.custom-navbar');
    console.log(`自定义导航栏样式: ${hasCustomNavbar ? '✅ 存在' : '❌ 缺失'}`);
    
    // 检查导航栏定位
    const hasFixedPosition = homeLess.includes('position: fixed');
    console.log(`导航栏固定定位: ${hasFixedPosition ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查z-index
    const hasZIndex = homeLess.includes('z-index: 1000');
    console.log(`导航栏层级: ${hasZIndex ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查容器padding-top
    const hasPaddingTop = homeLess.includes('padding-top: calc(env(safe-area-inset-top)');
    console.log(`容器顶部间距: ${hasPaddingTop ? '✅ 设置' : '❌ 未设置'}`);
    
    // 检查导航栏最小高度
    const hasMinHeight = homeLess.includes('min-height:');
    console.log(`导航栏最小高度: ${hasMinHeight ? '✅ 设置' : '❌ 未设置'}`);
    
    // 检查首页WXML
    const homeWxmlPath = 'pages/home/<USER>';
    if (fs.existsSync(homeWxmlPath)) {
      const homeWxml = fs.readFileSync(homeWxmlPath, 'utf8');
      
      // 检查自定义导航栏
      const hasCustomNavbarElement = homeWxml.includes('custom-navbar');
      console.log(`自定义导航栏元素: ${hasCustomNavbarElement ? '✅ 存在' : '❌ 缺失'}`);
      
      // 检查区域选择器
      const hasLocationSelector = homeWxml.includes('<location-selector');
      console.log(`区域选择器: ${hasLocationSelector ? '✅ 集成' : '❌ 未集成'}`);
      
      // 检查搜索框
      const hasSearchBox = homeWxml.includes('search-box');
      console.log(`搜索框: ${hasSearchBox ? '✅ 存在' : '❌ 缺失'}`);
      
      // 检查消息图标
      const hasMessageIcon = homeWxml.includes('message-icon');
      console.log(`消息图标: ${hasMessageIcon ? '✅ 存在' : '❌ 缺失'}`);
    }
    
    return hasCustomNavbar && hasFixedPosition && hasPaddingTop;
    
  } catch (error) {
    console.log(`❌ 首页布局检查失败: ${error.message}`);
    return false;
  }
}

// 检查导航栏组件样式
function checkNavbarStyles() {
  console.log('\n🧭 检查导航栏组件样式:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    if (!fs.existsSync(homeLessPath)) {
      return false;
    }
    
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查关键样式属性
    const styles = [
      { name: '渐变背景', pattern: 'linear-gradient' },
      { name: 'Flex布局', pattern: 'display: flex' },
      { name: '安全区域适配', pattern: 'env(safe-area-inset-top)' },
      { name: '毛玻璃效果', pattern: 'backdrop-filter' },
      { name: '圆角设计', pattern: 'border-radius' }
    ];
    
    styles.forEach(style => {
      const hasStyle = homeLess.includes(style.pattern);
      console.log(`  ${style.name}: ${hasStyle ? '✅ 应用' : '❌ 缺失'}`);
    });
    
    return true;
    
  } catch (error) {
    console.log(`❌ 导航栏样式检查失败: ${error.message}`);
    return false;
  }
}

// 检查响应式设计
function checkResponsiveDesign() {
  console.log('\n📱 检查响应式设计:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    if (!fs.existsSync(homeLessPath)) {
      return false;
    }
    
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查响应式单位
    const hasRpx = homeLess.includes('rpx');
    console.log(`响应式单位(rpx): ${hasRpx ? '✅ 使用' : '❌ 未使用'}`);
    
    // 检查Flex布局
    const hasFlexLayout = homeLess.includes('flex:') || homeLess.includes('flex-');
    console.log(`Flex弹性布局: ${hasFlexLayout ? '✅ 使用' : '❌ 未使用'}`);
    
    // 检查calc计算
    const hasCalc = homeLess.includes('calc(');
    console.log(`动态计算(calc): ${hasCalc ? '✅ 使用' : '❌ 未使用'}`);
    
    // 检查安全区域
    const hasSafeArea = homeLess.includes('safe-area-inset');
    console.log(`安全区域适配: ${hasSafeArea ? '✅ 适配' : '❌ 未适配'}`);
    
    return hasRpx && hasFlexLayout && hasCalc && hasSafeArea;
    
  } catch (error) {
    console.log(`❌ 响应式设计检查失败: ${error.message}`);
    return false;
  }
}

// 检查功能完整性
function checkFunctionality() {
  console.log('\n⚙️  检查功能完整性:');
  console.log('='.repeat(50));
  
  try {
    const homeJsPath = 'pages/home/<USER>';
    if (!fs.existsSync(homeJsPath)) {
      return false;
    }
    
    const homeJs = fs.readFileSync(homeJsPath, 'utf8');
    
    // 检查关键功能
    const functions = [
      { name: '区域选择处理', pattern: 'onLocationChange' },
      { name: '搜索跳转', pattern: 'goToSearch' },
      { name: '消息跳转', pattern: 'goToMessage' },
      { name: '分类筛选', pattern: 'onCategoryChange' }
    ];
    
    functions.forEach(func => {
      const hasFunction = homeJs.includes(func.pattern);
      console.log(`  ${func.name}: ${hasFunction ? '✅ 实现' : '❌ 缺失'}`);
    });
    
    return true;
    
  } catch (error) {
    console.log(`❌ 功能完整性检查失败: ${error.message}`);
    return false;
  }
}

// 生成布局修复报告
function generateLayoutFixReport(results) {
  console.log('\n📊 布局修复验证报告:');
  console.log('='.repeat(50));
  
  const { homePage, navbar, responsive, functionality } = results;
  
  console.log(`首页布局修复: ${homePage ? '✅ 完成' : '❌ 未完成'}`);
  console.log(`导航栏样式: ${navbar ? '✅ 正确' : '❌ 错误'}`);
  console.log(`响应式设计: ${responsive ? '✅ 完整' : '❌ 不完整'}`);
  console.log(`功能完整性: ${functionality ? '✅ 正常' : '❌ 异常'}`);
  
  const allPassed = homePage && navbar && responsive && functionality;
  
  if (allPassed) {
    console.log('\n🎉 页面布局修复验证通过！');
    console.log('\n🚀 修复效果:');
    console.log('  ✅ 自定义导航栏正确显示在顶部');
    console.log('  ✅ 内容区域不被导航栏遮挡');
    console.log('  ✅ 适配不同设备的状态栏高度');
    console.log('  ✅ 区域选择器正常工作');
    console.log('  ✅ 搜索和消息功能正常');
    console.log('  ✅ 响应式布局适配各种屏幕');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 在真机上测试布局效果');
    console.log('  3. 检查不同设备的适配情况');
    console.log('  4. 验证横竖屏切换效果');
    console.log('  5. 测试所有交互功能');
  } else {
    console.log('\n⚠️  部分验证未通过，需要进一步修复');
    
    console.log('\n🔧 修复建议:');
    if (!homePage) {
      console.log('  - 检查首页布局配置');
      console.log('  - 确保导航栏和容器样式正确');
    }
    if (!navbar) {
      console.log('  - 完善导航栏样式设计');
      console.log('  - 检查定位和层级设置');
    }
    if (!responsive) {
      console.log('  - 完善响应式设计');
      console.log('  - 确保适配不同设备');
    }
    if (!functionality) {
      console.log('  - 检查功能实现');
      console.log('  - 确保所有交互正常');
    }
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    homePage: checkHomePageLayoutFix(),
    navbar: checkNavbarStyles(),
    responsive: checkResponsiveDesign(),
    functionality: checkFunctionality()
  };
  
  generateLayoutFixReport(results);
}

// 运行验证脚本
if (require.main === module) {
  main();
}

module.exports = { 
  checkHomePageLayoutFix, 
  checkNavbarStyles, 
  checkResponsiveDesign,
  checkFunctionality 
};
