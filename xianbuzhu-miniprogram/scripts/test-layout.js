#!/usr/bin/env node

/**
 * 布局测试脚本
 * 验证页面布局是否正确
 */

const fs = require('fs');

console.log('📐 测试页面布局配置...\n');

// 检查布局工具函数
function checkLayoutUtils() {
  console.log('🔧 检查布局工具函数:');
  console.log('='.repeat(50));
  
  const layoutUtilPath = 'utils/layout.js';
  if (!fs.existsSync(layoutUtilPath)) {
    console.log('❌ 布局工具函数文件不存在');
    return false;
  }
  
  const layoutUtil = fs.readFileSync(layoutUtilPath, 'utf8');
  
  // 检查关键函数
  const functions = [
    'getSystemInfo',
    'getStatusBarHeight',
    'getCustomNavBarHeight',
    'getPageContentStyle',
    'setPageStyle'
  ];
  
  let allExists = true;
  functions.forEach(func => {
    const hasFunction = layoutUtil.includes(`function ${func}`) || 
                       layoutUtil.includes(`${func}(`);
    console.log(`  ${func}: ${hasFunction ? '✅ 存在' : '❌ 缺失'}`);
    if (!hasFunction) allExists = false;
  });
  
  return allExists;
}

// 检查首页布局配置
function checkHomePageLayout() {
  console.log('\n🏠 检查首页布局配置:');
  console.log('='.repeat(50));
  
  try {
    // 检查首页JS
    const homeJsPath = 'pages/home/<USER>';
    if (!fs.existsSync(homeJsPath)) {
      console.log('❌ 首页JS文件不存在');
      return false;
    }
    
    const homeJs = fs.readFileSync(homeJsPath, 'utf8');
    
    // 检查是否引入布局工具
    const hasLayoutImport = homeJs.includes('require(\'../../utils/layout\')') ||
                           homeJs.includes('require("../../utils/layout")');
    console.log(`布局工具引入: ${hasLayoutImport ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查是否调用setPageStyle
    const hasSetPageStyle = homeJs.includes('setPageStyle');
    console.log(`页面样式设置: ${hasSetPageStyle ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查是否有pageStyle数据
    const hasPageStyleData = homeJs.includes('pageStyle:');
    console.log(`页面样式数据: ${hasPageStyleData ? '✅ 正确' : '❌ 缺失'}`);
    
    // 检查首页WXML
    const homeWxmlPath = 'pages/home/<USER>';
    if (fs.existsSync(homeWxmlPath)) {
      const homeWxml = fs.readFileSync(homeWxmlPath, 'utf8');
      
      // 检查是否使用动态样式
      const hasDynamicStyle = homeWxml.includes('pageStyle.height') ||
                             homeWxml.includes('pageStyle.paddingTop');
      console.log(`动态样式应用: ${hasDynamicStyle ? '✅ 正确' : '❌ 缺失'}`);
    }
    
    // 检查首页LESS
    const homeLessPath = 'pages/home/<USER>';
    if (fs.existsSync(homeLessPath)) {
      const homeLess = fs.readFileSync(homeLessPath, 'utf8');
      
      // 检查自定义导航栏样式
      const hasCustomNavbar = homeLess.includes('.custom-navbar');
      console.log(`自定义导航栏样式: ${hasCustomNavbar ? '✅ 存在' : '❌ 缺失'}`);
      
      // 检查是否有固定的padding-top（应该被移除）
      const hasFixedPadding = homeLess.includes('padding-top: calc(env(safe-area-inset-top)');
      console.log(`固定padding移除: ${!hasFixedPadding ? '✅ 正确' : '❌ 仍存在'}`);
      
      // 检查导航栏高度设置
      const hasMinHeight = homeLess.includes('min-height:');
      console.log(`导航栏最小高度: ${hasMinHeight ? '✅ 设置' : '❌ 未设置'}`);
    }
    
    return hasLayoutImport && hasSetPageStyle && hasPageStyleData;
    
  } catch (error) {
    console.log(`❌ 首页布局检查失败: ${error.message}`);
    return false;
  }
}

// 检查其他页面是否需要布局修复
function checkOtherPages() {
  console.log('\n📄 检查其他页面布局:');
  console.log('='.repeat(50));
  
  const pagesWithCustomNavbar = [
    'pages/message/index',
    'pages/my/index'
  ];
  
  let needsFix = [];
  
  pagesWithCustomNavbar.forEach(pagePath => {
    const lessPath = `${pagePath}.less`;
    const jsPath = `${pagePath}.js`;
    
    if (fs.existsSync(lessPath)) {
      const lessContent = fs.readFileSync(lessPath, 'utf8');
      
      // 检查是否有固定的padding-top
      const hasFixedPadding = lessContent.includes('padding-top:') && 
                             lessContent.includes('env(safe-area-inset-top)');
      
      if (hasFixedPadding) {
        needsFix.push(pagePath);
        console.log(`❌ ${pagePath}: 需要修复布局`);
      } else {
        console.log(`✅ ${pagePath}: 布局正常`);
      }
    }
  });
  
  if (needsFix.length > 0) {
    console.log(`\n⚠️  需要修复的页面: ${needsFix.join(', ')}`);
    return false;
  }
  
  return true;
}

// 生成布局测试报告
function generateLayoutReport(results) {
  console.log('\n📊 布局测试报告:');
  console.log('='.repeat(50));
  
  const { utils, homePage, otherPages } = results;
  
  console.log(`布局工具函数: ${utils ? '✅ 完整' : '❌ 不完整'}`);
  console.log(`首页布局配置: ${homePage ? '✅ 正确' : '❌ 错误'}`);
  console.log(`其他页面布局: ${otherPages ? '✅ 正常' : '❌ 需修复'}`);
  
  const allPassed = utils && homePage && otherPages;
  
  if (allPassed) {
    console.log('\n🎉 页面布局测试通过！');
    console.log('\n🚀 布局特性:');
    console.log('  ✅ 动态计算导航栏高度');
    console.log('  ✅ 适配不同设备的状态栏');
    console.log('  ✅ 自动处理安全区域');
    console.log('  ✅ 响应式布局设计');
    console.log('  ✅ 内容区域不被遮挡');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 在不同设备上测试布局');
    console.log('  3. 检查导航栏是否正确显示');
    console.log('  4. 验证内容区域是否完整可见');
    console.log('  5. 测试横竖屏切换');
  } else {
    console.log('\n⚠️  部分布局测试未通过');
    
    console.log('\n🔧 修复建议:');
    if (!utils) {
      console.log('  - 检查并完善布局工具函数');
    }
    if (!homePage) {
      console.log('  - 修复首页布局配置');
      console.log('  - 确保正确引入和使用布局工具');
      console.log('  - 检查动态样式是否正确应用');
    }
    if (!otherPages) {
      console.log('  - 修复其他页面的固定布局');
      console.log('  - 统一使用动态布局计算');
    }
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    utils: checkLayoutUtils(),
    homePage: checkHomePageLayout(),
    otherPages: checkOtherPages()
  };
  
  generateLayoutReport(results);
}

// 运行测试脚本
if (require.main === module) {
  main();
}

module.exports = { 
  checkLayoutUtils, 
  checkHomePageLayout, 
  checkOtherPages 
};
