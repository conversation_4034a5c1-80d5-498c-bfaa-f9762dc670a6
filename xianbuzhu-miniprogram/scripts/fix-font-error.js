#!/usr/bin/env node

/**
 * TDesign字体加载错误修复脚本
 * 彻底解决 Failed to load font https://tdesign.gtimg.com/icon/0.3.1/fonts/t.woff 错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复TDesign字体加载错误...\n');

// 修复步骤1：修改TDesign图标字体配置
function fixTDesignIconFont() {
  const iconWxssPath = 'miniprogram_npm/tdesign-miniprogram/icon/icon.wxss';
  
  if (!fs.existsSync(iconWxssPath)) {
    console.log('❌ TDesign图标文件不存在:', iconWxssPath);
    return false;
  }
  
  try {
    let content = fs.readFileSync(iconWxssPath, 'utf8');
    
    // 检查是否已经修复
    if (content.includes('/* 修复：使用本地字体替代在线字体 */')) {
      console.log('✅ TDesign图标字体配置已修复');
      return true;
    }
    
    // 替换在线字体为本地字体
    const originalFontFace = /@font-face\s*{[^}]*src:\s*url\('https:\/\/tdesign\.gtimg\.com[^}]*}/g;
    
    const newFontFace = `/* 原始在线字体配置已注释 */
/* @font-face {
  font-family: t;
  src: url('https://tdesign.gtimg.com/icon/0.3.1/fonts/t.eot'), url('https://tdesign.gtimg.com/icon/0.3.1/fonts/t.eot?#iefix') format('ded-opentype'), url('https://tdesign.gtimg.com/icon/0.3.1/fonts/t.woff') format('woff'), url('https://tdesign.gtimg.com/icon/0.3.1/fonts/t.ttf') format('truetype'), url('https://tdesign.gtimg.com/icon/0.3.1/fonts/t.svg') format('svg');
  font-weight: normal;
  font-style: normal;
} */

/* 修复：使用本地字体替代在线字体 */
@font-face {
  font-family: t;
  src: local('Arial'), local('Helvetica'), local('PingFang SC'), local('Microsoft YaHei'), local('sans-serif');
  font-weight: normal;
  font-style: normal;
}`;
    
    content = content.replace(originalFontFace, newFontFace);
    
    fs.writeFileSync(iconWxssPath, content, 'utf8');
    console.log('✅ 已修复TDesign图标字体配置');
    return true;
    
  } catch (error) {
    console.log('❌ 修复TDesign图标字体配置失败:', error.message);
    return false;
  }
}

// 修复步骤2：更新app.wxss
function fixAppWxss() {
  const appWxssPath = 'app.wxss';
  
  try {
    let content = fs.readFileSync(appWxssPath, 'utf8');
    
    // 检查是否已包含修复代码
    if (content.includes('/* 彻底禁用TDesign在线字体加载 */')) {
      console.log('✅ app.wxss字体配置已修复');
      return true;
    }
    
    // 添加字体修复代码
    const fontFixCode = `
/* 彻底禁用TDesign在线字体加载 */
@font-face {
  font-family: t;
  src: local('Arial'), local('Helvetica'), local('PingFang SC'), local('Microsoft YaHei'), local('sans-serif') !important;
  font-weight: normal;
  font-style: normal;
}

/* 强制覆盖TDesign图标字体 */
.t-icon {
  font-family: 'Arial', 'Helvetica', 'PingFang SC', 'Microsoft YaHei', sans-serif !important;
}

/* 禁用所有外部字体加载 */
.t-icon::before {
  font-family: inherit !important;
}

/* 确保图标容器使用系统字体 */
.t-icon[class*="t-icon-"]::before {
  font-family: inherit !important;
}
`;
    
    // 在文件开头添加修复代码
    content = fontFixCode + '\n' + content;
    
    fs.writeFileSync(appWxssPath, content, 'utf8');
    console.log('✅ 已更新app.wxss字体配置');
    return true;
    
  } catch (error) {
    console.log('❌ 更新app.wxss失败:', error.message);
    return false;
  }
}

// 修复步骤3：更新项目配置
function fixProjectConfig() {
  const configPath = 'project.config.json';
  
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    
    // 禁用URL检查
    if (config.setting) {
      config.setting.urlCheck = false;
    }
    
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
    console.log('✅ 已更新项目配置，禁用URL检查');
    return true;
    
  } catch (error) {
    console.log('❌ 更新项目配置失败:', error.message);
    return false;
  }
}

// 修复步骤4：检查并清理缓存
function checkAndCleanCache() {
  console.log('📋 缓存清理建议:');
  console.log('  1. 在微信开发者工具中点击"工具" → "清除缓存" → "清除所有缓存"');
  console.log('  2. 重新编译项目');
  console.log('  3. 检查控制台是否还有字体加载错误');
}

// 生成修复报告
function generateReport(results) {
  console.log('\n📊 修复结果报告:');
  console.log('='.repeat(50));
  
  const totalSteps = results.length;
  const successSteps = results.filter(r => r.success).length;
  
  console.log(`总修复步骤: ${totalSteps}`);
  console.log(`成功步骤: ${successSteps}`);
  console.log(`失败步骤: ${totalSteps - successSteps}`);
  
  console.log('\n详细结果:');
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(`  ${status} 步骤${index + 1}: ${result.description}`);
  });
  
  if (successSteps === totalSteps) {
    console.log('\n🎉 所有修复步骤已完成！');
    console.log('\n🚀 下一步操作:');
    console.log('  1. 重启微信开发者工具');
    console.log('  2. 清除所有缓存');
    console.log('  3. 重新编译项目');
    console.log('  4. 检查控制台确认错误已解决');
  } else {
    console.log('\n⚠️  部分修复步骤失败，请手动检查相关文件');
  }
}

// 主函数
function main() {
  const results = [];
  
  // 执行修复步骤
  results.push({
    description: '修复TDesign图标字体配置',
    success: fixTDesignIconFont()
  });
  
  results.push({
    description: '更新app.wxss字体配置',
    success: fixAppWxss()
  });
  
  results.push({
    description: '更新项目配置',
    success: fixProjectConfig()
  });
  
  // 显示缓存清理建议
  checkAndCleanCache();
  
  // 生成报告
  generateReport(results);
}

// 运行修复脚本
if (require.main === module) {
  main();
}

module.exports = {
  fixTDesignIconFont,
  fixAppWxss,
  fixProjectConfig
};
