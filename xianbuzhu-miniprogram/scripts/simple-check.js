#!/usr/bin/env node

/**
 * 简单的文件检查脚本
 */

const fs = require('fs');

console.log('🔍 简单文件检查...\n');

// 检查关键文件是否存在
const keyFiles = [
  'pages/home/<USER>',
  'pages/home/<USER>',
  'pages/home/<USER>',
  'pages/home/<USER>',
  'components/location-selector/index.wxml',
  'components/location-selector/index.js',
  'components/location-selector/index.json',
  'app.json',
  'app.js'
];

console.log('📄 检查关键文件:');
console.log('='.repeat(40));

let allExist = true;

keyFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allExist = false;
});

// 检查JSON文件语法
console.log('\n📋 检查JSON语法:');
console.log('='.repeat(40));

const jsonFiles = [
  'app.json',
  'pages/home/<USER>',
  'components/location-selector/index.json'
];

let jsonValid = true;

jsonFiles.forEach(file => {
  if (fs.existsSync(file)) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      JSON.parse(content);
      console.log(`✅ ${file}: JSON语法正确`);
    } catch (error) {
      console.log(`❌ ${file}: JSON语法错误 - ${error.message}`);
      jsonValid = false;
    }
  }
});

// 检查WXML基本语法
console.log('\n📝 检查WXML基本语法:');
console.log('='.repeat(40));

const wxmlFiles = [
  'pages/home/<USER>',
  'components/location-selector/index.wxml'
];

let wxmlValid = true;

wxmlFiles.forEach(file => {
  if (fs.existsSync(file)) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      // 简单检查：确保每个 < 都有对应的 >
      let openCount = 0;
      let inComment = false;
      
      for (let i = 0; i < content.length; i++) {
        const char = content[i];
        const next3 = content.substr(i, 4);
        const next2 = content.substr(i, 3);
        
        if (next3 === '<!--') {
          inComment = true;
          i += 3;
          continue;
        }
        
        if (next2 === '-->' && inComment) {
          inComment = false;
          i += 2;
          continue;
        }
        
        if (!inComment) {
          if (char === '<') openCount++;
          if (char === '>') openCount--;
        }
      }
      
      if (openCount === 0) {
        console.log(`✅ ${file}: WXML语法基本正确`);
      } else {
        console.log(`❌ ${file}: WXML可能有未闭合的标签`);
        wxmlValid = false;
      }
      
    } catch (error) {
      console.log(`❌ ${file}: 读取失败 - ${error.message}`);
      wxmlValid = false;
    }
  }
});

// 总结
console.log('\n📊 检查结果:');
console.log('='.repeat(40));

console.log(`文件存在性: ${allExist ? '✅ 通过' : '❌ 失败'}`);
console.log(`JSON语法: ${jsonValid ? '✅ 正确' : '❌ 错误'}`);
console.log(`WXML语法: ${wxmlValid ? '✅ 基本正确' : '❌ 有问题'}`);

const allPassed = allExist && jsonValid && wxmlValid;

if (allPassed) {
  console.log('\n🎉 基本检查通过！');
  console.log('可以尝试在微信开发者工具中编译');
} else {
  console.log('\n⚠️  发现问题，请修复后再编译');
}

console.log('\n💡 提示:');
console.log('- 如果微信开发者工具报错，请查看具体错误信息');
console.log('- 确保所有组件都正确注册');
console.log('- 检查自定义组件的路径是否正确');
