#!/usr/bin/env node

/**
 * 首页容器布局验证脚本
 * 验证首页容器布局修复是否正确
 */

const fs = require('fs');

console.log('📦 验证首页容器布局修复...\n');

// 检查page样式设置
function checkPageStyles() {
  console.log('📄 检查page样式设置:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查page容器设置
    const hasPageFlex = homeLess.includes('page {') && homeLess.includes('display: flex');
    console.log(`page flex布局: ${hasPageFlex ? '✅ 已设置' : '❌ 未设置'}`);
    
    const hasPageHeight = homeLess.includes('height: 100vh');
    console.log(`page高度设置: ${hasPageHeight ? '✅ 已设置' : '❌ 未设置'}`);
    
    const hasPagePaddingBottom = homeLess.includes('padding-bottom: calc(env(safe-area-inset-bottom)');
    console.log(`page底部安全区域: ${hasPagePaddingBottom ? '✅ 已设置' : '❌ 未设置'}`);
    
    const hasBoxSizing = homeLess.includes('box-sizing: border-box');
    console.log(`page盒模型: ${hasBoxSizing ? '✅ 已设置' : '❌ 未设置'}`);
    
    return hasPageFlex && hasPageHeight && hasPagePaddingBottom && hasBoxSizing;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查导航栏定位方式
function checkNavbarPositioning() {
  console.log('\n🧭 检查导航栏定位方式:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查是否使用sticky定位
    const hasStickyPosition = homeLess.includes('position: sticky');
    console.log(`sticky定位: ${hasStickyPosition ? '✅ 使用' : '❌ 未使用'}`);
    
    // 检查导航栏是否移除了fixed定位
    const navbarFixedMatch = homeLess.match(/\.custom-navbar[\s\S]*?position:\s*fixed/);
    console.log(`导航栏fixed定位: ${!navbarFixedMatch ? '✅ 已移除' : '❌ 仍在使用'}`);
    
    // 检查top设置
    const hasTopZero = homeLess.match(/\.custom-navbar[\s\S]*?top:\s*0/);
    console.log(`top: 0设置: ${hasTopZero ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查是否包含状态栏高度
    const hasSafeAreaTop = homeLess.includes('calc(env(safe-area-inset-top)');
    console.log(`包含状态栏高度: ${hasSafeAreaTop ? '✅ 已包含' : '❌ 未包含'}`);
    
    return hasStickyPosition && !navbarFixedMatch && hasTopZero && hasSafeAreaTop;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查容器布局
function checkContainerLayout() {
  console.log('\n📦 检查容器布局:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查home-container是否使用flex: 1
    const hasContainerFlex = homeLess.match(/\.home-container[\s\S]*?flex:\s*1/);
    console.log(`容器flex: 1: ${hasContainerFlex ? '✅ 已设置' : '❌ 未设置'}`);
    
    // 检查是否移除了固定高度
    const hasFixedHeight = homeLess.match(/\.home-container[\s\S]*?height:\s*calc\(100vh/);
    console.log(`移除固定高度: ${!hasFixedHeight ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查是否移除了padding-top
    const hasPaddingTop = homeLess.match(/\.home-container[\s\S]*?padding-top:/);
    console.log(`移除padding-top: ${!hasPaddingTop ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查home-content是否使用flex: 1
    const hasContentFlex = homeLess.match(/\.home-content[\s\S]*?flex:\s*1/);
    console.log(`内容区flex: 1: ${hasContentFlex ? '✅ 已设置' : '❌ 未设置'}`);
    
    // 检查overflow设置
    const hasOverflow = homeLess.match(/\.home-content[\s\S]*?overflow-y:\s*auto/);
    console.log(`内容区滚动: ${hasOverflow ? '✅ 已设置' : '❌ 未设置'}`);
    
    return hasContainerFlex && !hasFixedHeight && !hasPaddingTop && hasContentFlex && hasOverflow;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 对比消息页面的布局方式
function compareWithMessagePage() {
  console.log('\n📋 对比消息页面布局:');
  console.log('='.repeat(50));
  
  try {
    const messageLessPath = 'pages/message/index.less';
    if (!fs.existsSync(messageLessPath)) {
      console.log('❌ 消息页面样式文件不存在');
      return false;
    }
    
    const messageLess = fs.readFileSync(messageLessPath, 'utf8');
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查page样式是否一致
    const messagePageFlex = messageLess.includes('page {') && messageLess.includes('display: flex');
    const homePageFlex = homeLess.includes('page {') && homeLess.includes('display: flex');
    console.log(`page flex布局一致性: ${messagePageFlex && homePageFlex ? '✅ 一致' : '❌ 不一致'}`);
    
    const messagePageHeight = messageLess.includes('height: 100vh');
    const homePageHeight = homeLess.includes('height: 100vh');
    console.log(`page高度设置一致性: ${messagePageHeight && homePageHeight ? '✅ 一致' : '❌ 不一致'}`);
    
    const messagePaddingBottom = messageLess.includes('padding-bottom: calc(env(safe-area-inset-bottom)');
    const homePaddingBottom = homeLess.includes('padding-bottom: calc(env(safe-area-inset-bottom)');
    console.log(`底部安全区域一致性: ${messagePaddingBottom && homePaddingBottom ? '✅ 一致' : '❌ 不一致'}`);
    
    console.log('\n参考的消息页面布局特点:');
    console.log('  - page使用flex布局');
    console.log('  - page高度100vh');
    console.log('  - page底部padding包含安全区域');
    console.log('  - 内容区域使用flex: 1');
    
    return messagePageFlex && homePageFlex && messagePageHeight && homePageHeight && messagePaddingBottom && homePaddingBottom;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查布局层次结构
function checkLayoutHierarchy() {
  console.log('\n🏗️  检查布局层次结构:');
  console.log('='.repeat(50));
  
  console.log('修复后的布局层次:');
  console.log('1. page (flex容器, 100vh高度)');
  console.log('   ├─ .custom-navbar (sticky定位, 包含状态栏高度)');
  console.log('   └─ .home-container (flex: 1)');
  console.log('       └─ .home-content (flex: 1, 可滚动)');
  
  console.log('\n与消息页面的对比:');
  console.log('消息页面: page > nav组件 + .message-list');
  console.log('首页: page > .custom-navbar + .home-container');
  console.log('两者都使用相同的page容器设置');
  
  return true;
}

// 生成容器布局验证报告
function generateContainerReport(results) {
  console.log('\n📊 首页容器布局验证报告:');
  console.log('='.repeat(50));
  
  const { pageStyles, navbarPositioning, containerLayout, messageComparison, layoutHierarchy } = results;
  
  console.log(`page样式设置: ${pageStyles ? '✅ 正确' : '❌ 错误'}`);
  console.log(`导航栏定位: ${navbarPositioning ? '✅ 正确' : '❌ 错误'}`);
  console.log(`容器布局: ${containerLayout ? '✅ 正确' : '❌ 错误'}`);
  console.log(`与消息页面一致性: ${messageComparison ? '✅ 一致' : '❌ 不一致'}`);
  console.log(`布局层次: ${layoutHierarchy ? '✅ 正确' : '❌ 错误'}`);
  
  const allPassed = pageStyles && navbarPositioning && containerLayout && messageComparison && layoutHierarchy;
  
  if (allPassed) {
    console.log('\n🎉 首页容器布局修复验证通过！');
    console.log('\n✨ 修复效果:');
    console.log('  📄 page容器使用flex布局，与消息页面一致');
    console.log('  🧭 导航栏使用sticky定位，不会超出显示区域');
    console.log('  📦 容器使用flex: 1，自适应剩余空间');
    console.log('  📱 适配安全区域，在所有设备上正确显示');
    console.log('  🔄 内容区域可正常滚动');
    
    console.log('\n📱 布局结构:');
    console.log('  page (flex, 100vh)');
    console.log('  ├─ custom-navbar (sticky, 包含状态栏)');
    console.log('  │   ├─ location-selector');
    console.log('  │   └─ search-container');
    console.log('  └─ home-container (flex: 1)');
    console.log('      └─ home-content (flex: 1, 滚动)');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查导航栏是否在正确位置');
    console.log('  3. 验证定位和搜索框是否正常显示');
    console.log('  4. 测试页面滚动功能');
    console.log('  5. 在不同设备上验证显示效果');
  } else {
    console.log('\n⚠️  部分验证未通过');
    
    console.log('\n🔧 修复建议:');
    if (!pageStyles) console.log('  - 检查page样式设置');
    if (!navbarPositioning) console.log('  - 调整导航栏定位方式');
    if (!containerLayout) console.log('  - 优化容器布局');
    if (!messageComparison) console.log('  - 参考消息页面布局');
    if (!layoutHierarchy) console.log('  - 检查布局层次结构');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    pageStyles: checkPageStyles(),
    navbarPositioning: checkNavbarPositioning(),
    containerLayout: checkContainerLayout(),
    messageComparison: compareWithMessagePage(),
    layoutHierarchy: checkLayoutHierarchy()
  };
  
  generateContainerReport(results);
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = { 
  checkPageStyles, 
  checkNavbarPositioning, 
  checkContainerLayout,
  compareWithMessagePage,
  checkLayoutHierarchy 
};
