#!/usr/bin/env node

/**
 * 功能模块清理验证脚本
 * 验证清理后的小程序功能完整性
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 验证小程序功能模块清理结果...\n');

// 核心功能页面列表
const CORE_PAGES = [
  // 主包页面
  { path: 'pages/home/<USER>', name: '首页', required: true },
  { path: 'pages/message/index', name: '消息页面', required: true },
  { path: 'pages/my/index', name: '我的页面', required: true },
  { path: 'pages/item-detail/index', name: '物品详情', required: true },
  
  // 分包页面
  { path: 'pages/search/index', name: '搜索页面', required: true },
  { path: 'pages/release/index', name: '发布物品', required: true },
  { path: 'pages/chat/index', name: '聊天页面', required: true },
  { path: 'pages/user-profile/index', name: '用户详情', required: true },
  { path: 'pages/my-items/index', name: '我的物品', required: true },
  { path: 'pages/my/info-edit/index', name: '个人信息编辑', required: true },
  { path: 'pages/login/index', name: '登录页面', required: true },
  { path: 'pages/agreement/index', name: '协议页面', required: true },
];

// 应该被移除的页面
const REMOVED_PAGES = [
  { path: 'pages/dataCenter/index', name: '数据中心页面' },
  { path: 'pages/setting/index', name: '设置页面' },
];

// 检查页面文件是否存在
function checkPageExists(pagePath) {
  const files = ['.wxml', '.js', '.json', '.less', '.wxss'];
  const results = {};
  
  files.forEach(ext => {
    const filePath = `${pagePath}${ext}`;
    results[ext] = fs.existsSync(filePath);
  });
  
  return results;
}

// 检查核心页面
function checkCorePages() {
  console.log('📋 检查核心功能页面:');
  console.log('='.repeat(50));
  
  let allPassed = true;
  
  CORE_PAGES.forEach((page, index) => {
    const files = checkPageExists(page.path);
    const hasWxml = files['.wxml'];
    const hasJs = files['.js'];
    const hasJson = files['.json'];
    const hasStyle = files['.less'] || files['.wxss'];
    
    const isComplete = hasWxml && hasJs && hasJson;
    
    if (isComplete) {
      console.log(`✅ ${index + 1}. ${page.name}: 完整`);
    } else {
      console.log(`❌ ${index + 1}. ${page.name}: 缺失文件`);
      if (!hasWxml) console.log(`   - 缺少 .wxml 文件`);
      if (!hasJs) console.log(`   - 缺少 .js 文件`);
      if (!hasJson) console.log(`   - 缺少 .json 文件`);
      if (!hasStyle) console.log(`   - 缺少样式文件`);
      allPassed = false;
    }
  });
  
  return allPassed;
}

// 检查已移除的页面
function checkRemovedPages() {
  console.log('\n🗑️  检查已移除的页面:');
  console.log('='.repeat(50));
  
  let allRemoved = true;
  
  REMOVED_PAGES.forEach((page, index) => {
    const files = checkPageExists(page.path);
    const hasAnyFile = Object.values(files).some(exists => exists);
    
    if (!hasAnyFile) {
      console.log(`✅ ${index + 1}. ${page.name}: 已移除`);
    } else {
      console.log(`❌ ${index + 1}. ${page.name}: 仍然存在`);
      Object.entries(files).forEach(([ext, exists]) => {
        if (exists) {
          console.log(`   - 存在文件: ${page.path}${ext}`);
        }
      });
      allRemoved = false;
    }
  });
  
  return allRemoved;
}

// 检查app.json配置
function checkAppJsonConfig() {
  console.log('\n📄 检查app.json配置:');
  console.log('='.repeat(50));
  
  try {
    const appJsonPath = 'app.json';
    if (!fs.existsSync(appJsonPath)) {
      console.log('❌ app.json文件不存在');
      return false;
    }
    
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    // 检查主包页面
    const mainPages = appJson.pages || [];
    console.log(`主包页面数量: ${mainPages.length}`);
    mainPages.forEach(page => {
      console.log(`  - ${page}`);
    });
    
    // 检查分包页面
    const subPackages = appJson.subpackages || [];
    console.log(`\n分包数量: ${subPackages.length}`);
    subPackages.forEach(pkg => {
      console.log(`  分包: ${pkg.name} (${pkg.root})`);
      pkg.pages.forEach(page => {
        console.log(`    - ${pkg.root}/${page}`);
      });
    });
    
    // 检查tabBar配置
    const tabBar = appJson.tabBar;
    if (tabBar && tabBar.list) {
      console.log(`\nTabBar页面数量: ${tabBar.list.length}`);
      tabBar.list.forEach(tab => {
        console.log(`  - ${tab.pagePath} (${tab.text})`);
      });
    }
    
    console.log('\n✅ app.json配置检查完成');
    return true;
    
  } catch (error) {
    console.log(`❌ app.json配置检查失败: ${error.message}`);
    return false;
  }
}

// 检查侧边栏配置
function checkSidebarConfig() {
  console.log('\n🔧 检查侧边栏配置:');
  console.log('='.repeat(50));
  
  try {
    const navPath = 'components/nav/index.js';
    if (!fs.existsSync(navPath)) {
      console.log('❌ nav组件文件不存在');
      return false;
    }
    
    const navContent = fs.readFileSync(navPath, 'utf8');
    
    // 提取sidebar配置
    const sidebarMatch = navContent.match(/sidebar:\s*\[([\s\S]*?)\]/);
    if (!sidebarMatch) {
      console.log('❌ 未找到sidebar配置');
      return false;
    }
    
    console.log('侧边栏配置项:');
    
    // 简单解析sidebar项目（这里用正则匹配title）
    const titleMatches = sidebarMatch[1].match(/title:\s*['"`]([^'"`]+)['"`]/g);
    if (titleMatches) {
      titleMatches.forEach((match, index) => {
        const title = match.match(/['"`]([^'"`]+)['"`]/)[1];
        console.log(`  ${index + 1}. ${title}`);
      });
    }
    
    console.log('\n✅ 侧边栏配置检查完成');
    return true;
    
  } catch (error) {
    console.log(`❌ 侧边栏配置检查失败: ${error.message}`);
    return false;
  }
}

// 检查登录功能
function checkLoginFeature() {
  console.log('\n🔐 检查登录功能:');
  console.log('='.repeat(50));
  
  const loginFiles = [
    { path: 'pages/login/index.wxml', name: '登录页面模板' },
    { path: 'pages/login/index.js', name: '登录页面逻辑' },
    { path: 'pages/login/index.json', name: '登录页面配置' },
    { path: 'pages/agreement/index.wxml', name: '协议页面模板' },
    { path: 'utils/auth.js', name: '认证工具函数' },
  ];
  
  let allExists = true;
  
  loginFiles.forEach((file, index) => {
    if (fs.existsSync(file.path)) {
      console.log(`✅ ${index + 1}. ${file.name}: 存在`);
    } else {
      console.log(`❌ ${index + 1}. ${file.name}: 缺失`);
      allExists = false;
    }
  });
  
  return allExists;
}

// 生成清理报告
function generateCleanupReport(results) {
  console.log('\n📊 功能模块清理报告:');
  console.log('='.repeat(50));
  
  const { corePages, removedPages, appJson, sidebar, login } = results;
  
  console.log(`核心页面检查: ${corePages ? '✅ 通过' : '❌ 失败'}`);
  console.log(`页面移除检查: ${removedPages ? '✅ 通过' : '❌ 失败'}`);
  console.log(`app.json配置: ${appJson ? '✅ 正常' : '❌ 异常'}`);
  console.log(`侧边栏配置: ${sidebar ? '✅ 正常' : '❌ 异常'}`);
  console.log(`登录功能: ${login ? '✅ 完整' : '❌ 缺失'}`);
  
  const allPassed = corePages && removedPages && appJson && sidebar && login;
  
  if (allPassed) {
    console.log('\n🎉 功能模块清理完成！');
    console.log('\n🚀 闲置物品置换小程序功能模块:');
    console.log('  ✅ 用户系统 (登录/注册/个人中心)');
    console.log('  ✅ 物品管理 (发布/浏览/搜索/详情)');
    console.log('  ✅ 交换系统 (消息/聊天/用户详情)');
    console.log('  ✅ 个人功能 (我的物品/个人信息)');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 测试各个功能模块');
    console.log('  3. 验证页面跳转和交互');
    console.log('  4. 检查登录状态管理');
  } else {
    console.log('\n⚠️  部分检查未通过，请查看上述详细信息');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    corePages: checkCorePages(),
    removedPages: checkRemovedPages(),
    appJson: checkAppJsonConfig(),
    sidebar: checkSidebarConfig(),
    login: checkLoginFeature()
  };
  
  generateCleanupReport(results);
}

// 运行验证脚本
if (require.main === module) {
  main();
}

module.exports = { 
  checkCorePages, 
  checkRemovedPages, 
  checkAppJsonConfig,
  checkSidebarConfig,
  checkLoginFeature 
};
