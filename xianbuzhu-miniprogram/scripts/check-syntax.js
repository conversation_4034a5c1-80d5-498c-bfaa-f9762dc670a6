#!/usr/bin/env node

/**
 * 语法检查脚本
 * 检查所有文件的语法错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查文件语法错误...\n');

// 检查JSON文件语法
function checkJsonSyntax(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    JSON.parse(content);
    return { valid: true };
  } catch (error) {
    return { 
      valid: false, 
      error: error.message,
      line: error.message.match(/line (\d+)/)?.[1] || 'unknown'
    };
  }
}

// 检查WXML文件语法
function checkWxmlSyntax(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const errors = [];
    
    // 检查基本的XML语法错误
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      
      // 检查未闭合的标签
      if (line.includes('<') && !line.includes('>') && !line.trim().startsWith('<!--')) {
        errors.push(`第${lineNum}行: 可能存在未闭合的标签`);
      }
      
      // 检查多余的 <
      const openBrackets = (line.match(/</g) || []).length;
      const closeBrackets = (line.match(/>/g) || []).length;
      if (openBrackets > closeBrackets && !line.includes('<!--')) {
        errors.push(`第${lineNum}行: 标签可能未正确闭合`);
      }
    });
    
    return { 
      valid: errors.length === 0, 
      errors: errors 
    };
  } catch (error) {
    return { 
      valid: false, 
      error: error.message 
    };
  }
}

// 检查JavaScript文件语法
function checkJsSyntax(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 基本的语法检查
    const errors = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      const trimmed = line.trim();
      
      // 检查常见的语法错误
      if (trimmed.includes('function') && !trimmed.includes('(')) {
        errors.push(`第${lineNum}行: 函数定义可能缺少括号`);
      }
      
      // 检查未闭合的括号
      const openParens = (line.match(/\(/g) || []).length;
      const closeParens = (line.match(/\)/g) || []).length;
      if (openParens !== closeParens && !trimmed.startsWith('//')) {
        errors.push(`第${lineNum}行: 括号可能未正确匹配`);
      }
    });
    
    return { 
      valid: errors.length === 0, 
      errors: errors 
    };
  } catch (error) {
    return { 
      valid: false, 
      error: error.message 
    };
  }
}

// 检查单个文件
function checkFile(filePath) {
  const ext = path.extname(filePath);
  const fileName = path.basename(filePath);
  
  if (!fs.existsSync(filePath)) {
    return { valid: false, error: '文件不存在' };
  }
  
  switch (ext) {
    case '.json':
      return checkJsonSyntax(filePath);
    case '.wxml':
      return checkWxmlSyntax(filePath);
    case '.js':
      return checkJsSyntax(filePath);
    default:
      return { valid: true, skipped: true };
  }
}

// 检查核心页面文件
function checkCorePages() {
  console.log('📄 检查核心页面文件:');
  console.log('='.repeat(50));
  
  const corePages = [
    'pages/home/<USER>',
    'pages/message/index',
    'pages/my/index',
    'pages/item-detail/index'
  ];
  
  let allValid = true;
  
  corePages.forEach(pagePath => {
    console.log(`\n检查页面: ${pagePath}`);
    
    const files = [
      { path: `${pagePath}.wxml`, name: 'WXML模板' },
      { path: `${pagePath}.js`, name: 'JavaScript逻辑' },
      { path: `${pagePath}.json`, name: 'JSON配置' },
      { path: `${pagePath}.less`, name: 'LESS样式' }
    ];
    
    files.forEach(file => {
      const result = checkFile(file.path);
      
      if (result.skipped) {
        console.log(`  ${file.name}: ⏭️  跳过检查`);
      } else if (result.valid) {
        console.log(`  ${file.name}: ✅ 语法正确`);
      } else {
        console.log(`  ${file.name}: ❌ 语法错误`);
        if (result.error) {
          console.log(`    错误: ${result.error}`);
        }
        if (result.errors) {
          result.errors.forEach(err => {
            console.log(`    ${err}`);
          });
        }
        allValid = false;
      }
    });
  });
  
  return allValid;
}

// 检查组件文件
function checkComponents() {
  console.log('\n🧩 检查组件文件:');
  console.log('='.repeat(50));
  
  const components = [
    'components/location-selector/index',
    'components/nav/index',
    'components/card/index',
    'components/custom-icon/index'
  ];
  
  let allValid = true;
  
  components.forEach(componentPath => {
    console.log(`\n检查组件: ${componentPath}`);
    
    const files = [
      { path: `${componentPath}.wxml`, name: 'WXML模板' },
      { path: `${componentPath}.js`, name: 'JavaScript逻辑' },
      { path: `${componentPath}.json`, name: 'JSON配置' }
    ];
    
    files.forEach(file => {
      const result = checkFile(file.path);
      
      if (result.skipped) {
        console.log(`  ${file.name}: ⏭️  跳过检查`);
      } else if (result.valid) {
        console.log(`  ${file.name}: ✅ 语法正确`);
      } else {
        console.log(`  ${file.name}: ❌ 语法错误`);
        if (result.error) {
          console.log(`    错误: ${result.error}`);
        }
        if (result.errors) {
          result.errors.forEach(err => {
            console.log(`    ${err}`);
          });
        }
        allValid = false;
      }
    });
  });
  
  return allValid;
}

// 检查配置文件
function checkConfigFiles() {
  console.log('\n⚙️  检查配置文件:');
  console.log('='.repeat(50));
  
  const configFiles = [
    { path: 'app.json', name: '应用配置' },
    { path: 'app.js', name: '应用逻辑' },
    { path: 'project.config.json', name: '项目配置' },
    { path: 'sitemap.json', name: '站点地图' }
  ];
  
  let allValid = true;
  
  configFiles.forEach(file => {
    const result = checkFile(file.path);
    
    if (result.skipped) {
      console.log(`${file.name}: ⏭️  跳过检查`);
    } else if (result.valid) {
      console.log(`${file.name}: ✅ 语法正确`);
    } else {
      console.log(`${file.name}: ❌ 语法错误`);
      if (result.error) {
        console.log(`  错误: ${result.error}`);
      }
      if (result.errors) {
        result.errors.forEach(err => {
          console.log(`  ${err}`);
        });
      }
      allValid = false;
    }
  });
  
  return allValid;
}

// 生成语法检查报告
function generateSyntaxReport(results) {
  console.log('\n📊 语法检查报告:');
  console.log('='.repeat(50));
  
  const { pages, components, configs } = results;
  
  console.log(`核心页面文件: ${pages ? '✅ 语法正确' : '❌ 存在错误'}`);
  console.log(`组件文件: ${components ? '✅ 语法正确' : '❌ 存在错误'}`);
  console.log(`配置文件: ${configs ? '✅ 语法正确' : '❌ 存在错误'}`);
  
  const allPassed = pages && components && configs;
  
  if (allPassed) {
    console.log('\n🎉 所有文件语法检查通过！');
    console.log('\n🚀 现在可以安全地:');
    console.log('  ✅ 重新编译小程序');
    console.log('  ✅ 在开发者工具中预览');
    console.log('  ✅ 在真机上测试');
    console.log('  ✅ 发布到体验版');
  } else {
    console.log('\n⚠️  发现语法错误，请修复后再编译');
    console.log('\n🔧 修复建议:');
    console.log('  1. 检查上述错误信息');
    console.log('  2. 修复语法错误');
    console.log('  3. 重新运行语法检查');
    console.log('  4. 确保所有文件都通过检查');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    pages: checkCorePages(),
    components: checkComponents(),
    configs: checkConfigFiles()
  };
  
  generateSyntaxReport(results);
}

// 运行语法检查
if (require.main === module) {
  main();
}

module.exports = { 
  checkFile,
  checkCorePages, 
  checkComponents, 
  checkConfigFiles 
};
