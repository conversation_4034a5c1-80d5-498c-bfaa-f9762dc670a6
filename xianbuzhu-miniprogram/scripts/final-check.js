#!/usr/bin/env node

/**
 * 最终检查脚本
 * 确保所有文件都准备好编译
 */

const fs = require('fs');

console.log('🎯 最终编译前检查...\n');

// 检查关键文件
function checkFiles() {
  console.log('📁 检查关键文件:');
  console.log('='.repeat(40));
  
  const files = [
    { path: 'app.json', desc: '应用配置' },
    { path: 'app.js', desc: '应用逻辑' },
    { path: 'pages/home/<USER>', desc: '首页模板' },
    { path: 'pages/home/<USER>', desc: '首页逻辑' },
    { path: 'pages/home/<USER>', desc: '首页配置' },
    { path: 'components/location-selector/index.wxml', desc: '区域选择器模板' },
    { path: 'components/location-selector/index.js', desc: '区域选择器逻辑' },
    { path: 'components/custom-icon/index.wxml', desc: '自定义图标模板' },
    { path: 'utils/location.js', desc: '位置工具函数' }
  ];
  
  let allExist = true;
  
  files.forEach(file => {
    const exists = fs.existsSync(file.path);
    console.log(`${exists ? '✅' : '❌'} ${file.desc}: ${file.path}`);
    if (!exists) allExist = false;
  });
  
  return allExist;
}

// 检查组件注册
function checkComponentRegistration() {
  console.log('\n🧩 检查组件注册:');
  console.log('='.repeat(40));
  
  try {
    const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    const homeJson = JSON.parse(fs.readFileSync('pages/home/<USER>', 'utf8'));
    
    // 检查全局组件
    const globalComponents = appJson.usingComponents || {};
    console.log('全局组件:');
    Object.keys(globalComponents).forEach(name => {
      const path = globalComponents[name];
      const exists = fs.existsSync(path + '.js');
      console.log(`  ${exists ? '✅' : '❌'} ${name}: ${path}`);
    });
    
    // 检查首页组件
    const homeComponents = homeJson.usingComponents || {};
    console.log('首页组件:');
    Object.keys(homeComponents).forEach(name => {
      const path = homeComponents[name];
      const exists = fs.existsSync(path + '.js');
      console.log(`  ${exists ? '✅' : '❌'} ${name}: ${path}`);
    });
    
    return true;
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查页面配置
function checkPageConfig() {
  console.log('\n📄 检查页面配置:');
  console.log('='.repeat(40));
  
  try {
    const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    
    // 检查主包页面
    const pages = appJson.pages || [];
    console.log(`主包页面数量: ${pages.length}`);
    
    let allPagesExist = true;
    pages.forEach(page => {
      const exists = fs.existsSync(page + '.wxml') && fs.existsSync(page + '.js');
      console.log(`  ${exists ? '✅' : '❌'} ${page}`);
      if (!exists) allPagesExist = false;
    });
    
    // 检查分包页面
    const subpackages = appJson.subpackages || [];
    console.log(`分包数量: ${subpackages.length}`);
    
    subpackages.forEach(pkg => {
      console.log(`  分包: ${pkg.name} (${pkg.root})`);
      pkg.pages.forEach(page => {
        const fullPath = pkg.root + '/' + page;
        const exists = fs.existsSync(fullPath + '.wxml') && fs.existsSync(fullPath + '.js');
        console.log(`    ${exists ? '✅' : '❌'} ${fullPath}`);
        if (!exists) allPagesExist = false;
      });
    });
    
    return allPagesExist;
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查权限配置
function checkPermissions() {
  console.log('\n🔐 检查权限配置:');
  console.log('='.repeat(40));
  
  try {
    const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    
    // 检查位置权限
    const permission = appJson.permission;
    const hasLocationPermission = permission && permission['scope.userLocation'];
    console.log(`位置权限: ${hasLocationPermission ? '✅ 已配置' : '❌ 未配置'}`);
    
    // 检查隐私信息
    const requiredPrivateInfos = appJson.requiredPrivateInfos || [];
    const hasLocationPrivacy = requiredPrivateInfos.includes('getLocation');
    console.log(`位置隐私声明: ${hasLocationPrivacy ? '✅ 已配置' : '❌ 未配置'}`);
    
    return hasLocationPermission && hasLocationPrivacy;
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 生成最终报告
function generateFinalReport(results) {
  console.log('\n📊 最终检查报告:');
  console.log('='.repeat(40));
  
  const { files, components, pages, permissions } = results;
  
  console.log(`关键文件: ${files ? '✅ 完整' : '❌ 缺失'}`);
  console.log(`组件注册: ${components ? '✅ 正确' : '❌ 错误'}`);
  console.log(`页面配置: ${pages ? '✅ 完整' : '❌ 缺失'}`);
  console.log(`权限配置: ${permissions ? '✅ 完整' : '❌ 缺失'}`);
  
  const allPassed = files && components && pages && permissions;
  
  if (allPassed) {
    console.log('\n🎉 所有检查通过！可以安全编译');
    console.log('\n🚀 编译步骤:');
    console.log('1. 打开微信开发者工具');
    console.log('2. 导入项目目录');
    console.log('3. 点击"编译"按钮');
    console.log('4. 检查控制台是否有错误');
    console.log('5. 在模拟器中测试功能');
    
    console.log('\n✨ 主要功能:');
    console.log('- 自定义导航栏（区域选择 + 搜索 + 消息）');
    console.log('- 地理位置和区域选择');
    console.log('- 物品分类和展示');
    console.log('- 用户登录和认证');
    console.log('- 消息和聊天功能');
  } else {
    console.log('\n⚠️  发现问题，请修复后再编译');
    
    if (!files) console.log('- 检查缺失的关键文件');
    if (!components) console.log('- 检查组件注册和路径');
    if (!pages) console.log('- 检查页面配置和文件');
    if (!permissions) console.log('- 检查权限和隐私配置');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    files: checkFiles(),
    components: checkComponentRegistration(),
    pages: checkPageConfig(),
    permissions: checkPermissions()
  };
  
  generateFinalReport(results);
}

// 运行检查
if (require.main === module) {
  main();
}

module.exports = { 
  checkFiles,
  checkComponentRegistration, 
  checkPageConfig, 
  checkPermissions 
};
