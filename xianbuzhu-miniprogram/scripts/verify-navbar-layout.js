#!/usr/bin/env node

/**
 * 导航栏布局验证脚本
 * 验证导航栏布局修复是否正确
 */

const fs = require('fs');

console.log('🎨 验证导航栏布局修复...\n');

// 检查导航栏尺寸设置
function checkNavbarDimensions() {
  console.log('📏 检查导航栏尺寸:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查导航栏高度
    const minHeightMatch = homeLess.match(/min-height:\s*(\d+)rpx/);
    const minHeight = minHeightMatch ? parseInt(minHeightMatch[1]) : 0;
    console.log(`导航栏最小高度: ${minHeight}rpx ${minHeight >= 96 ? '✅ 足够' : '❌ 过小'}`);
    
    // 检查内边距
    const paddingMatch = homeLess.match(/padding:\s*(\d+)rpx\s+(\d+)rpx/);
    if (paddingMatch) {
      const verticalPadding = parseInt(paddingMatch[1]);
      const horizontalPadding = parseInt(paddingMatch[2]);
      console.log(`导航栏内边距: ${verticalPadding}rpx ${horizontalPadding}rpx ${verticalPadding >= 24 ? '✅ 足够' : '❌ 过小'}`);
    }
    
    // 检查组件间距
    const gapMatch = homeLess.match(/gap:\s*(\d+)rpx/);
    const gap = gapMatch ? parseInt(gapMatch[1]) : 0;
    console.log(`组件间距: ${gap}rpx ${gap >= 20 ? '✅ 合适' : '❌ 过小'}`);
    
    return minHeight >= 96 && (paddingMatch ? parseInt(paddingMatch[1]) >= 24 : false) && gap >= 20;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查搜索框样式
function checkSearchBoxStyle() {
  console.log('\n🔍 检查搜索框样式:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查搜索框高度
    const searchMinHeightMatch = homeLess.match(/\.search-box[\s\S]*?min-height:\s*(\d+)rpx/);
    const searchMinHeight = searchMinHeightMatch ? parseInt(searchMinHeightMatch[1]) : 0;
    console.log(`搜索框最小高度: ${searchMinHeight}rpx ${searchMinHeight >= 56 ? '✅ 足够' : '❌ 过小'}`);
    
    // 检查搜索框内边距
    const searchPaddingMatch = homeLess.match(/\.search-box[\s\S]*?padding:\s*(\d+)rpx\s+(\d+)rpx/);
    if (searchPaddingMatch) {
      const searchVerticalPadding = parseInt(searchPaddingMatch[1]);
      console.log(`搜索框内边距: ${searchVerticalPadding}rpx ${searchVerticalPadding >= 20 ? '✅ 足够' : '❌ 过小'}`);
    }
    
    // 检查搜索框圆角
    const searchBorderRadiusMatch = homeLess.match(/\.search-box[\s\S]*?border-radius:\s*(\d+)rpx/);
    const searchBorderRadius = searchBorderRadiusMatch ? parseInt(searchBorderRadiusMatch[1]) : 0;
    console.log(`搜索框圆角: ${searchBorderRadius}rpx ${searchBorderRadius >= 36 ? '✅ 美观' : '⚠️  可优化'}`);
    
    return searchMinHeight >= 56 && (searchPaddingMatch ? parseInt(searchPaddingMatch[1]) >= 20 : false);
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查区域选择器样式
function checkLocationSelectorStyle() {
  console.log('\n📍 检查区域选择器样式:');
  console.log('='.repeat(50));
  
  try {
    const locationLessPath = 'components/location-selector/index.less';
    if (!fs.existsSync(locationLessPath)) {
      console.log('❌ 区域选择器样式文件不存在');
      return false;
    }
    
    const locationLess = fs.readFileSync(locationLessPath, 'utf8');
    
    // 检查区域选择器高度
    const locationMinHeightMatch = locationLess.match(/min-height:\s*(\d+)rpx/);
    const locationMinHeight = locationMinHeightMatch ? parseInt(locationMinHeightMatch[1]) : 0;
    console.log(`区域选择器最小高度: ${locationMinHeight}rpx ${locationMinHeight >= 56 ? '✅ 足够' : '❌ 过小'}`);
    
    // 检查区域选择器内边距
    const locationPaddingMatch = locationLess.match(/padding:\s*(\d+)rpx\s+(\d+)rpx/);
    if (locationPaddingMatch) {
      const locationVerticalPadding = parseInt(locationPaddingMatch[1]);
      console.log(`区域选择器内边距: ${locationVerticalPadding}rpx ${locationVerticalPadding >= 16 ? '✅ 足够' : '❌ 过小'}`);
    }
    
    // 检查区域选择器圆角
    const locationBorderRadiusMatch = locationLess.match(/border-radius:\s*(\d+)rpx/);
    const locationBorderRadius = locationBorderRadiusMatch ? parseInt(locationBorderRadiusMatch[1]) : 0;
    console.log(`区域选择器圆角: ${locationBorderRadius}rpx ${locationBorderRadius >= 28 ? '✅ 美观' : '⚠️  可优化'}`);
    
    return locationMinHeight >= 56 && (locationPaddingMatch ? parseInt(locationPaddingMatch[1]) >= 16 : false);
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查消息图标样式
function checkMessageIconStyle() {
  console.log('\n💬 检查消息图标样式:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查消息图标尺寸
    const messageMinWidthMatch = homeLess.match(/\.message-icon[\s\S]*?min-width:\s*(\d+)rpx/);
    const messageMinHeightMatch = homeLess.match(/\.message-icon[\s\S]*?min-height:\s*(\d+)rpx/);
    
    const messageMinWidth = messageMinWidthMatch ? parseInt(messageMinWidthMatch[1]) : 0;
    const messageMinHeight = messageMinHeightMatch ? parseInt(messageMinHeightMatch[1]) : 0;
    
    console.log(`消息图标最小宽度: ${messageMinWidth}rpx ${messageMinWidth >= 56 ? '✅ 足够' : '❌ 过小'}`);
    console.log(`消息图标最小高度: ${messageMinHeight}rpx ${messageMinHeight >= 56 ? '✅ 足够' : '❌ 过小'}`);
    
    // 检查消息图标内边距
    const messagePaddingMatch = homeLess.match(/\.message-icon[\s\S]*?padding:\s*(\d+)rpx/);
    const messagePadding = messagePaddingMatch ? parseInt(messagePaddingMatch[1]) : 0;
    console.log(`消息图标内边距: ${messagePadding}rpx ${messagePadding >= 16 ? '✅ 足够' : '❌ 过小'}`);
    
    return messageMinWidth >= 56 && messageMinHeight >= 56 && messagePadding >= 16;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查内容区域适配
function checkContentAreaAdaptation() {
  console.log('\n📄 检查内容区域适配:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查内容区域padding-top
    const contentPaddingMatch = homeLess.match(/padding-top:\s*calc\(env\(safe-area-inset-top\)\s*\+\s*(\d+)rpx\)/);
    const contentPadding = contentPaddingMatch ? parseInt(contentPaddingMatch[1]) : 0;
    
    console.log(`内容区域顶部间距: ${contentPadding}rpx`);
    
    // 理想的padding应该是: 导航栏高度(96rpx) + 导航栏padding(48rpx) = 144rpx
    if (contentPadding === 144) {
      console.log('✅ 间距正确 (96rpx导航栏 + 48rpx内边距 = 144rpx)');
      return true;
    } else if (contentPadding > 160) {
      console.log(`⚠️  间距过大 (${contentPadding}rpx)，可能导致内容区域过小`);
      return false;
    } else if (contentPadding < 130) {
      console.log(`❌ 间距过小 (${contentPadding}rpx)，内容可能被导航栏遮挡`);
      return false;
    } else {
      console.log(`⚠️  间距可能需要微调 (${contentPadding}rpx)`);
      return true;
    }
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 生成布局验证报告
function generateLayoutReport(results) {
  console.log('\n📊 导航栏布局验证报告:');
  console.log('='.repeat(50));
  
  const { navbar, searchBox, locationSelector, messageIcon, contentArea } = results;
  
  console.log(`导航栏尺寸: ${navbar ? '✅ 正确' : '❌ 错误'}`);
  console.log(`搜索框样式: ${searchBox ? '✅ 正确' : '❌ 错误'}`);
  console.log(`区域选择器: ${locationSelector ? '✅ 正确' : '❌ 错误'}`);
  console.log(`消息图标: ${messageIcon ? '✅ 正确' : '❌ 错误'}`);
  console.log(`内容区域: ${contentArea ? '✅ 正确' : '❌ 错误'}`);
  
  const allPassed = navbar && searchBox && locationSelector && messageIcon && contentArea;
  
  if (allPassed) {
    console.log('\n🎉 导航栏布局修复验证通过！');
    console.log('\n✨ 修复效果:');
    console.log('  📏 导航栏高度足够，组件不会被压缩');
    console.log('  📍 区域选择器显示完整，可正常点击');
    console.log('  🔍 搜索框高度合适，文字居中显示');
    console.log('  💬 消息图标尺寸正确，徽章位置准确');
    console.log('  📄 内容区域不被遮挡，滚动正常');
    console.log('  🎨 整体视觉效果美观统一');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查导航栏组件是否正常显示');
    console.log('  3. 测试各个组件的点击功能');
    console.log('  4. 验证在不同设备上的显示效果');
    console.log('  5. 确认内容区域滚动正常');
  } else {
    console.log('\n⚠️  部分布局验证未通过');
    
    console.log('\n🔧 修复建议:');
    if (!navbar) console.log('  - 调整导航栏高度和内边距');
    if (!searchBox) console.log('  - 优化搜索框尺寸和样式');
    if (!locationSelector) console.log('  - 调整区域选择器组件样式');
    if (!messageIcon) console.log('  - 优化消息图标尺寸');
    if (!contentArea) console.log('  - 调整内容区域的顶部间距');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    navbar: checkNavbarDimensions(),
    searchBox: checkSearchBoxStyle(),
    locationSelector: checkLocationSelectorStyle(),
    messageIcon: checkMessageIconStyle(),
    contentArea: checkContentAreaAdaptation()
  };
  
  generateLayoutReport(results);
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = { 
  checkNavbarDimensions, 
  checkSearchBoxStyle, 
  checkLocationSelectorStyle,
  checkMessageIconStyle,
  checkContentAreaAdaptation 
};
