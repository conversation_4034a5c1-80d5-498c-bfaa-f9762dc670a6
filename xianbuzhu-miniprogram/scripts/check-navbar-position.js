#!/usr/bin/env node

/**
 * 导航栏位置检查脚本
 * 验证导航栏位置是否正确
 */

const fs = require('fs');

console.log('📐 检查导航栏位置配置...\n');

// 检查导航栏位置设置
function checkNavbarPosition() {
  console.log('🧭 检查导航栏位置:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    if (!fs.existsSync(homeLessPath)) {
      console.log('❌ 首页样式文件不存在');
      return false;
    }
    
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查导航栏定位
    const hasFixedPosition = homeLess.includes('position: fixed');
    console.log(`固定定位: ${hasFixedPosition ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查top位置 - 应该是从状态栏下方开始
    const hasCorrectTop = homeLess.includes('top: env(safe-area-inset-top)');
    const hasWrongTop = homeLess.includes('top: 0');
    console.log(`top位置: ${hasCorrectTop ? '✅ 从状态栏下方开始' : hasWrongTop ? '❌ 从屏幕顶部开始(错误)' : '⚠️  未设置'}`);
    
    // 检查z-index
    const hasZIndex = homeLess.includes('z-index: 1000');
    console.log(`层级设置: ${hasZIndex ? '✅ 正确' : '❌ 错误'}`);
    
    // 检查padding设置
    const hasPadding = homeLess.includes('padding: 20rpx 32rpx');
    const hasWrongPadding = homeLess.includes('padding: calc(env(safe-area-inset-top)');
    console.log(`内边距: ${hasPadding ? '✅ 正确(不包含状态栏高度)' : hasWrongPadding ? '❌ 错误(包含状态栏高度)' : '⚠️  未设置'}`);
    
    // 检查最小高度
    const hasMinHeight = homeLess.includes('min-height: 88rpx');
    const hasWrongMinHeight = homeLess.includes('min-height: calc(88rpx + env(safe-area-inset-top))');
    console.log(`最小高度: ${hasMinHeight ? '✅ 正确(仅导航栏内容高度)' : hasWrongMinHeight ? '❌ 错误(包含状态栏高度)' : '⚠️  未设置'}`);
    
    return hasCorrectTop && !hasWrongTop && hasPadding && !hasWrongPadding && hasMinHeight && !hasWrongMinHeight;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查内容区域位置
function checkContentPosition() {
  console.log('\n📄 检查内容区域位置:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查容器padding-top
    const paddingTopMatch = homeLess.match(/padding-top:\s*calc\(env\(safe-area-inset-top\)\s*\+\s*(\d+)rpx\)/);
    
    if (paddingTopMatch) {
      const paddingValue = parseInt(paddingTopMatch[1]);
      console.log(`容器顶部间距: ${paddingValue}rpx`);
      
      // 理想的padding-top应该是: 状态栏高度 + 导航栏高度(88rpx) + 导航栏padding(40rpx) = 128rpx
      if (paddingValue === 128) {
        console.log(`✅ 间距正确 (状态栏 + 88rpx导航栏 + 40rpx内边距 = 128rpx)`);
        return true;
      } else if (paddingValue > 140) {
        console.log(`⚠️  间距过大 (${paddingValue}rpx)，可能导致内容区域过小`);
        return false;
      } else if (paddingValue < 120) {
        console.log(`❌ 间距过小 (${paddingValue}rpx)，内容可能被导航栏遮挡`);
        return false;
      } else {
        console.log(`⚠️  间距可能需要微调 (${paddingValue}rpx)`);
        return true;
      }
    } else {
      console.log('❌ 未找到容器顶部间距设置');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查布局层次
function checkLayoutHierarchy() {
  console.log('\n📱 检查布局层次:');
  console.log('='.repeat(50));
  
  console.log('正确的布局层次应该是:');
  console.log('1. 📶 系统状态栏 (最顶部)');
  console.log('2. 🧭 自定义导航栏 (状态栏下方)');
  console.log('3. 📄 页面内容 (导航栏下方)');
  console.log('4. 📋 TabBar (最底部)');
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查是否正确实现了这个层次
    const correctTop = homeLess.includes('top: env(safe-area-inset-top)');
    const correctPadding = homeLess.includes('padding: 20rpx 32rpx');
    const correctContentPadding = homeLess.includes('padding-top: calc(env(safe-area-inset-top) + 128rpx)');
    
    console.log(`\n实际配置:`);
    console.log(`导航栏位置: ${correctTop ? '✅ 状态栏下方' : '❌ 位置错误'}`);
    console.log(`导航栏内边距: ${correctPadding ? '✅ 不包含状态栏' : '❌ 包含状态栏'}`);
    console.log(`内容区间距: ${correctContentPadding ? '✅ 正确计算' : '❌ 计算错误'}`);
    
    return correctTop && correctPadding && correctContentPadding;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 生成位置检查报告
function generatePositionReport(results) {
  console.log('\n📊 导航栏位置检查报告:');
  console.log('='.repeat(50));
  
  const { navbar, content, hierarchy } = results;
  
  console.log(`导航栏位置: ${navbar ? '✅ 正确' : '❌ 错误'}`);
  console.log(`内容区位置: ${content ? '✅ 正确' : '❌ 错误'}`);
  console.log(`布局层次: ${hierarchy ? '✅ 正确' : '❌ 错误'}`);
  
  const allPassed = navbar && content && hierarchy;
  
  if (allPassed) {
    console.log('\n🎉 导航栏位置配置正确！');
    console.log('\n✨ 预期效果:');
    console.log('  📶 系统状态栏显示在最顶部');
    console.log('  🧭 自定义导航栏在状态栏下方');
    console.log('  📍 区域选择器和搜索框在导航栏内');
    console.log('  📄 页面内容从导航栏下方开始');
    console.log('  🚫 没有内容被遮挡');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查导航栏是否在正确位置');
    console.log('  3. 验证状态栏是否可见');
    console.log('  4. 测试不同设备的适配效果');
  } else {
    console.log('\n⚠️  导航栏位置配置有问题');
    
    console.log('\n🔧 修复建议:');
    if (!navbar) {
      console.log('  - 设置导航栏 top: env(safe-area-inset-top)');
      console.log('  - 调整导航栏 padding，移除状态栏高度');
      console.log('  - 设置正确的最小高度');
    }
    if (!content) {
      console.log('  - 调整内容区域的 padding-top');
      console.log('  - 确保计算包含状态栏和导航栏高度');
    }
    if (!hierarchy) {
      console.log('  - 检查整体布局层次');
      console.log('  - 确保各层级位置正确');
    }
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    navbar: checkNavbarPosition(),
    content: checkContentPosition(),
    hierarchy: checkLayoutHierarchy()
  };
  
  generatePositionReport(results);
}

// 运行检查
if (require.main === module) {
  main();
}

module.exports = { 
  checkNavbarPosition, 
  checkContentPosition, 
  checkLayoutHierarchy 
};
