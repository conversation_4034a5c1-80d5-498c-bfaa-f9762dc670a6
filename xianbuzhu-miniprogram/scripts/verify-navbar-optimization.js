#!/usr/bin/env node

/**
 * 导航栏布局优化验证脚本
 * 验证定位组件和搜索框的优化效果
 */

const fs = require('fs');

console.log('🎨 验证导航栏布局优化...\n');

// 检查定位组件样式优化
function checkLocationSelectorOptimization() {
  console.log('📍 检查定位组件样式优化:');
  console.log('='.repeat(50));
  
  try {
    const locationLessPath = 'components/location-selector/index.less';
    const locationLess = fs.readFileSync(locationLessPath, 'utf8');
    
    // 检查是否移除了卡片背景
    const hasBackground = locationLess.includes('background: rgba(255, 255, 255, 0.9)');
    console.log(`移除卡片背景: ${!hasBackground ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查是否移除了圆角
    const hasBorderRadius = locationLess.includes('border-radius: 28rpx');
    console.log(`移除圆角: ${!hasBorderRadius ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查是否移除了内边距
    const hasPadding = locationLess.includes('padding: 16rpx 20rpx');
    console.log(`移除内边距: ${!hasPadding ? '✅ 已移除' : '❌ 仍存在'}`);
    
    // 检查文字颜色是否改为白色
    const hasWhiteColor = locationLess.includes('color: #fff');
    console.log(`文字颜色白色: ${hasWhiteColor ? '✅ 已设置' : '❌ 未设置'}`);
    
    // 检查最大宽度是否减小
    const hasReducedWidth = locationLess.includes('max-width: 160rpx');
    console.log(`减小最大宽度: ${hasReducedWidth ? '✅ 已减小' : '❌ 未减小'}`);
    
    return !hasBackground && !hasBorderRadius && !hasPadding && hasWhiteColor && hasReducedWidth;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查定位组件图标颜色
function checkLocationIconColors() {
  console.log('\n🎨 检查定位组件图标颜色:');
  console.log('='.repeat(50));
  
  try {
    const locationWxmlPath = 'components/location-selector/index.wxml';
    const locationWxml = fs.readFileSync(locationWxmlPath, 'utf8');
    
    // 检查定位图标颜色
    const hasWhiteLocationIcon = locationWxml.includes('name="location" size="28rpx" color="#fff"');
    console.log(`定位图标白色: ${hasWhiteLocationIcon ? '✅ 已设置' : '❌ 未设置'}`);
    
    // 检查下拉箭头颜色
    const hasWhiteChevron = locationWxml.includes('name="chevron-down" size="24rpx" color="#fff"');
    console.log(`下拉箭头白色: ${hasWhiteChevron ? '✅ 已设置' : '❌ 未设置'}`);
    
    return hasWhiteLocationIcon && hasWhiteChevron;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查搜索框优化
function checkSearchBoxOptimization() {
  console.log('\n🔍 检查搜索框优化:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查最大宽度限制
    const hasMaxWidth = homeLess.includes('max-width: 480rpx');
    console.log(`搜索框最大宽度: ${hasMaxWidth ? '✅ 已限制' : '❌ 未限制'}`);
    
    // 检查背景透明度调整
    const hasNewBackground = homeLess.includes('background: rgba(255, 255, 255, 0.15)');
    console.log(`背景透明度调整: ${hasNewBackground ? '✅ 已调整' : '❌ 未调整'}`);
    
    // 检查边框添加
    const hasBorder = homeLess.includes('border: 1rpx solid rgba(255, 255, 255, 0.2)');
    console.log(`添加边框: ${hasBorder ? '✅ 已添加' : '❌ 未添加'}`);
    
    // 检查高度调整
    const hasNewHeight = homeLess.includes('height: 52rpx');
    console.log(`高度调整: ${hasNewHeight ? '✅ 已调整' : '❌ 未调整'}`);
    
    // 检查圆角调整
    const hasNewBorderRadius = homeLess.includes('border-radius: 28rpx');
    console.log(`圆角调整: ${hasNewBorderRadius ? '✅ 已调整' : '❌ 未调整'}`);
    
    return hasMaxWidth && hasNewBackground && hasBorder && hasNewHeight && hasNewBorderRadius;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查搜索框文字和图标颜色
function checkSearchBoxColors() {
  console.log('\n🎨 检查搜索框颜色:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    const homeWxmlPath = 'pages/home/<USER>';
    const homeWxml = fs.readFileSync(homeWxmlPath, 'utf8');
    
    // 检查文字颜色
    const hasWhiteText = homeLess.includes('color: rgba(255, 255, 255, 0.8)');
    console.log(`搜索文字颜色: ${hasWhiteText ? '✅ 半透明白色' : '❌ 未设置'}`);
    
    // 检查搜索图标颜色
    const hasWhiteIcon = homeWxml.includes('color="rgba(255, 255, 255, 0.8)"');
    console.log(`搜索图标颜色: ${hasWhiteIcon ? '✅ 半透明白色' : '❌ 未设置'}`);
    
    return hasWhiteText && hasWhiteIcon;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 检查导航栏间距优化
function checkNavbarSpacing() {
  console.log('\n📐 检查导航栏间距优化:');
  console.log('='.repeat(50));
  
  try {
    const homeLessPath = 'pages/home/<USER>';
    const homeLess = fs.readFileSync(homeLessPath, 'utf8');
    
    // 检查组件间距
    const hasOptimizedGap = homeLess.includes('gap: 24rpx');
    console.log(`组件间距优化: ${hasOptimizedGap ? '✅ 已优化' : '❌ 未优化'}`);
    
    return hasOptimizedGap;
    
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 生成优化验证报告
function generateOptimizationReport(results) {
  console.log('\n📊 导航栏布局优化验证报告:');
  console.log('='.repeat(50));
  
  const { locationSelector, locationIcons, searchBox, searchColors, navbarSpacing } = results;
  
  console.log(`定位组件优化: ${locationSelector ? '✅ 完成' : '❌ 未完成'}`);
  console.log(`定位图标颜色: ${locationIcons ? '✅ 正确' : '❌ 错误'}`);
  console.log(`搜索框优化: ${searchBox ? '✅ 完成' : '❌ 未完成'}`);
  console.log(`搜索框颜色: ${searchColors ? '✅ 正确' : '❌ 错误'}`);
  console.log(`导航栏间距: ${navbarSpacing ? '✅ 优化' : '❌ 未优化'}`);
  
  const allPassed = locationSelector && locationIcons && searchBox && searchColors && navbarSpacing;
  
  if (allPassed) {
    console.log('\n🎉 导航栏布局优化验证通过！');
    console.log('\n✨ 优化效果:');
    console.log('  📍 定位组件去除卡片样式，直接贴靠左侧');
    console.log('  🎨 定位组件使用白色文字和图标，融入导航栏');
    console.log('  🔍 搜索框长度适中，不会过长');
    console.log('  💫 搜索框使用半透明样式，更好融入背景');
    console.log('  📐 组件间距合理，整体和谐');
    
    console.log('\n📱 预期显示效果:');
    console.log('  ┌─────────────────────────────────┐');
    console.log('  │ 📶 状态栏                       │');
    console.log('  ├─────────────────────────────────┤');
    console.log('  │ 📍阳光花园 ▼   🔍搜索物品      │ ← 优化后的导航栏');
    console.log('  │ (白色文字)     (半透明搜索框)   │');
    console.log('  ├─────────────────────────────────┤');
    console.log('  │        页面内容                 │');
    console.log('  └─────────────────────────────────┘');
    
    console.log('\n🎨 样式特点:');
    console.log('  - 定位组件：无背景，白色文字，贴靠左侧');
    console.log('  - 搜索框：半透明背景，白色边框，适中长度');
    console.log('  - 整体：简洁和谐，视觉统一');
    
    console.log('\n🔄 下一步操作:');
    console.log('  1. 重新编译小程序');
    console.log('  2. 检查定位组件是否贴靠左侧');
    console.log('  3. 验证搜索框长度是否合适');
    console.log('  4. 确认整体样式和谐统一');
    console.log('  5. 测试所有交互功能');
  } else {
    console.log('\n⚠️  部分优化验证未通过');
    
    console.log('\n🔧 修复建议:');
    if (!locationSelector) console.log('  - 完成定位组件样式优化');
    if (!locationIcons) console.log('  - 调整定位图标颜色');
    if (!searchBox) console.log('  - 优化搜索框样式');
    if (!searchColors) console.log('  - 调整搜索框颜色');
    if (!navbarSpacing) console.log('  - 优化导航栏间距');
  }
  
  return allPassed;
}

// 主函数
function main() {
  const results = {
    locationSelector: checkLocationSelectorOptimization(),
    locationIcons: checkLocationIconColors(),
    searchBox: checkSearchBoxOptimization(),
    searchColors: checkSearchBoxColors(),
    navbarSpacing: checkNavbarSpacing()
  };
  
  generateOptimizationReport(results);
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = { 
  checkLocationSelectorOptimization, 
  checkLocationIconColors, 
  checkSearchBoxOptimization,
  checkSearchBoxColors,
  checkNavbarSpacing 
};
