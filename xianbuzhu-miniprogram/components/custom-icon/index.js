const { processIcon } = require('../../utils/icon-config');

Component({
  properties: {
    // 图标名称
    name: {
      type: String,
      value: ''
    },
    // 图标大小
    size: {
      type: String,
      value: '24rpx'
    },
    // 图标颜色
    color: {
      type: String,
      value: '#333'
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  data: {
    iconChar: ''
  },

  observers: {
    'name': function(newName) {
      this.updateIcon(newName);
    }
  },

  lifetimes: {
    attached() {
      this.updateIcon(this.data.name);
    }
  },

  methods: {
    updateIcon(iconName) {
      const iconChar = processIcon(iconName);
      this.setData({
        iconChar: iconChar
      });
    },

    onIconTap(e) {
      this.triggerEvent('tap', {
        name: this.data.name
      });
    }
  }
});
