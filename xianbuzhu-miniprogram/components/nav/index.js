Component({
  options: {
    styleIsolation: 'shared',
  },
  properties: {
    navType: {
      type: String,
      value: 'title',
    },
    titleText: String,
  },
  data: {
    visible: false,
    sidebar: [
      {
        title: '首页',
        url: 'pages/home/<USER>',
        isSidebar: true,
      },
      {
        title: '消息',
        url: 'pages/message/index',
        isSidebar: true,
      },
      {
        title: '我的',
        url: 'pages/my/index',
        isSidebar: true,
      },
      {
        title: '搜索物品',
        url: 'pages/search/index',
        isSidebar: false,
      },
      {
        title: '发布物品',
        url: 'pages/release/index',
        isSidebar: false,
      },
      {
        title: '我的物品',
        url: 'pages/my-items/index',
        isSidebar: false,
      },
      {
        title: '个人信息',
        url: 'pages/my/info-edit/index',
        isSidebar: false,
      },
      {
        title: '登录/注册',
        url: 'pages/login/index',
        isSidebar: false,
      },
    ],
    statusHeight: 0,
  },
  lifetimes: {
    ready() {
      const statusHeight = wx.getWindowInfo().statusBarHeight;
      this.setData({ statusHeight });
    },
  },
  methods: {
    openDrawer() {
      this.setData({
        visible: true,
      });
    },
    itemClick(e) {
      const that = this;
      const { isSidebar, url } = e.detail.item;
      if (isSidebar) {
        wx.switchTab({
          url: `/${url}`,
        }).then(() => {
          // 防止点回tab时，sidebar依旧是展开模式
          that.setData({
            visible: false,
          });
        });
      } else {
        wx.navigateTo({
          url: `/${url}`,
        }).then(() => {
          that.setData({
            visible: false,
          });
        });
      }
    },

    searchTurn() {
      wx.navigateTo({
        url: `/pages/search/index`,
      });
    },
  },
});
