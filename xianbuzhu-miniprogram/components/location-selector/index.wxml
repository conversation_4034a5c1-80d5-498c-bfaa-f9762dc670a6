<view class="location-selector {{customClass}}" bindtap="onSelectorTap">
  <!-- 定位图标 -->
  <view class="location-icon">
    <custom-icon name="location" size="28rpx" color="#fff" />
  </view>

  <!-- 当前区域显示 -->
  <view class="location-text">
    <text class="community-name">{{currentCommunity.name || '定位中...'}}</text>
    <custom-icon name="chevron-down" size="24rpx" color="#fff" />
  </view>
</view>

<!-- 区域选择弹窗 -->
<t-popup 
  visible="{{showPopup}}" 
  placement="top"
  bind:visible-change="onPopupChange"
  t-class="location-popup"
>
  <view class="popup-content">
    <!-- 弹窗头部 -->
    <view class="popup-header">
      <view class="header-title">选择区域</view>
      <view class="header-close" bindtap="closePopup">
        <custom-icon name="close" size="32rpx" color="#999" />
      </view>
    </view>
    
    <!-- 当前定位 -->
    <view class="current-location">
      <view class="section-title">
        <custom-icon name="location" size="28rpx" color="#0052d9" />
        <text>当前定位</text>
        <view class="refresh-btn" bindtap="refreshLocation">
          <custom-icon name="refresh" size="24rpx" color="#0052d9" />
          <text>重新定位</text>
        </view>
      </view>
      
      <view class="location-item current" wx:if="{{currentLocation.address}}">
        <view class="location-info">
          <view class="location-name">{{currentLocation.address.formatted_address}}</view>
          <view class="location-detail">{{currentLocation.address.address}}</view>
        </view>
        <view class="location-action" bindtap="selectCurrentLocation">
          <text>选择</text>
        </view>
      </view>
      
      <view class="location-loading" wx:else>
        <text>正在定位...</text>
      </view>
    </view>
    
    <!-- 附近小区 -->
    <view class="nearby-communities">
      <view class="section-title">
        <custom-icon name="home" size="28rpx" color="#666" />
        <text>附近小区</text>
      </view>
      
      <scroll-view class="communities-list" scroll-y>
        <view 
          class="location-item {{item.id === selectedId ? 'selected' : ''}}"
          wx:for="{{nearbyCommunities}}"
          wx:key="id"
          bindtap="selectCommunity"
          data-community="{{item}}"
        >
          <view class="location-info">
            <view class="location-name">{{item.name}}</view>
            <view class="location-detail">
              {{item.address}}
              <text class="distance">{{item.distanceText}}</text>
            </view>
          </view>
          <view class="location-action" wx:if="{{item.id === selectedId}}">
            <custom-icon name="check" size="24rpx" color="#0052d9" />
          </view>
        </view>
        
        <view class="communities-loading" wx:if="{{loadingCommunities}}">
          <text>加载中...</text>
        </view>
        
        <view class="communities-empty" wx:elif="{{nearbyCommunities.length === 0}}">
          <text>暂无附近小区</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 地图选择 -->
    <view class="map-selector">
      <view class="map-btn" bindtap="openMapSelector">
        <custom-icon name="location" size="28rpx" color="#0052d9" />
        <text>在地图上选择</text>
        <custom-icon name="chevron-right" size="24rpx" color="#999" />
      </view>
    </view>
  </view>
</t-popup>
