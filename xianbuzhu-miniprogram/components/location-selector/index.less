@import '/variable.less';

.location-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 0;
  box-sizing: border-box;
  
  .location-icon {
    flex-shrink: 0;
  }

  .location-text {
    display: flex;
    align-items: center;
    gap: 6rpx;

    .community-name {
      font-size: 30rpx;
      color: #fff;
      font-weight: 500;
      width: 144rpx; // 固定宽度，约6个字 (30rpx * 6 * 0.8)
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.location-popup {
  .popup-content {
    background: @bg-color-white;
    border-radius: 0 0 24rpx 24rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .header-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .header-close {
      padding: 8rpx;
    }
  }
  
  .current-location {
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 8rpx;
      margin-bottom: 24rpx;
      
      text {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        flex: 1;
      }
      
      .refresh-btn {
        display: flex;
        align-items: center;
        gap: 6rpx;
        padding: 8rpx 16rpx;
        border-radius: 16rpx;
        background: #f0f8ff;
        
        text {
          font-size: 24rpx;
          color: @brand-color;
        }
      }
    }
    
    .location-loading {
      text-align: center;
      padding: 40rpx 0;
      color: #999;
      font-size: 28rpx;
    }
  }
  
  .nearby-communities {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 32rpx 32rpx 16rpx;
      
      text {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
      }
    }
    
    .communities-list {
      flex: 1;
      max-height: 400rpx;
    }
    
    .communities-loading,
    .communities-empty {
      text-align: center;
      padding: 60rpx 0;
      color: #999;
      font-size: 28rpx;
    }
  }
  
  .location-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 1rpx solid #f8f8f8;
    transition: background-color 0.2s;
    
    &:active {
      background-color: #f8f8f8;
    }
    
    &.current {
      background-color: #f0f8ff;
    }
    
    &.selected {
      background-color: #f0f8ff;
    }
    
    .location-info {
      flex: 1;
      
      .location-name {
        font-size: 30rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 8rpx;
      }
      
      .location-detail {
        font-size: 26rpx;
        color: #666;
        
        .distance {
          margin-left: 16rpx;
          color: @brand-color;
          font-size: 24rpx;
        }
      }
    }
    
    .location-action {
      flex-shrink: 0;
      
      text {
        font-size: 28rpx;
        color: @brand-color;
      }
    }
  }
  
  .map-selector {
    padding: 24rpx 32rpx;
    border-top: 1rpx solid #f0f0f0;
    
    .map-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      padding: 24rpx;
      border: 1rpx solid #e0e0e0;
      border-radius: 12rpx;
      background: #fafafa;
      
      text {
        font-size: 28rpx;
        color: @brand-color;
      }
    }
  }
}
