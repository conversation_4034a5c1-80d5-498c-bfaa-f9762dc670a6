const { 
  getUserLocationWithAddress, 
  searchNearbyCommunities, 
  saveSelectedCommunity, 
  getSelectedCommunity,
  formatDistance,
  requestLocationAuth
} = require('../../utils/location');

Component({
  properties: {
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    showPopup: false,
    currentCommunity: {},
    currentLocation: {},
    nearbyCommunities: [],
    selectedId: '',
    loadingCommunities: false
  },

  lifetimes: {
    attached() {
      this.initLocation();
    }
  },

  methods: {
    // 初始化位置信息
    async initLocation() {
      try {
        // 先获取已保存的社区
        const savedCommunity = getSelectedCommunity();
        if (savedCommunity) {
          this.setData({
            currentCommunity: savedCommunity,
            selectedId: savedCommunity.id
          });
        }

        // 获取当前位置
        await this.getCurrentLocationInfo();
        
      } catch (error) {
        console.error('初始化位置失败:', error);
        this.setData({
          currentCommunity: { name: '定位失败' }
        });
      }
    },

    // 获取当前位置信息
    async getCurrentLocationInfo() {
      try {
        const locationInfo = await getUserLocationWithAddress();
        
        this.setData({
          currentLocation: locationInfo
        });

        // 如果没有保存的社区，使用当前定位
        if (!this.data.currentCommunity.name || this.data.currentCommunity.name === '定位中...') {
          this.setData({
            currentCommunity: {
              name: locationInfo.address.formatted_address,
              address: locationInfo.address.address,
              latitude: locationInfo.latitude,
              longitude: locationInfo.longitude
            }
          });
        }

        // 搜索附近小区
        await this.searchNearbyCommunities(locationInfo.latitude, locationInfo.longitude);
        
      } catch (error) {
        console.error('获取位置信息失败:', error);
        
        // 如果是权限问题，提示用户授权
        if (error.message.includes('权限')) {
          const hasAuth = await requestLocationAuth();
          if (hasAuth) {
            this.getCurrentLocationInfo();
          }
        }
      }
    },

    // 搜索附近小区
    async searchNearbyCommunities(latitude, longitude) {
      try {
        this.setData({ loadingCommunities: true });
        
        const communities = await searchNearbyCommunities(latitude, longitude);
        
        // 添加距离文本
        const communitiesWithDistance = communities.map(community => ({
          ...community,
          distanceText: formatDistance(community.distance)
        }));
        
        this.setData({
          nearbyCommunities: communitiesWithDistance,
          loadingCommunities: false
        });
        
      } catch (error) {
        console.error('搜索附近小区失败:', error);
        this.setData({ loadingCommunities: false });
      }
    },

    // 点击选择器
    onSelectorTap() {
      this.setData({ showPopup: true });
    },

    // 弹窗显示状态变化
    onPopupChange(e) {
      this.setData({ showPopup: e.detail.visible });
    },

    // 关闭弹窗
    closePopup() {
      this.setData({ showPopup: false });
    },

    // 重新定位
    async refreshLocation() {
      wx.showToast({
        title: '定位中...',
        icon: 'loading'
      });
      
      await this.getCurrentLocationInfo();
      
      wx.hideToast();
    },

    // 选择当前定位
    selectCurrentLocation() {
      const { currentLocation } = this.data;
      if (!currentLocation.address) return;

      const community = {
        id: 'current',
        name: currentLocation.address.formatted_address,
        address: currentLocation.address.address,
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        distance: 0
      };

      this.selectCommunityData(community);
    },

    // 选择小区
    selectCommunity(e) {
      const community = e.currentTarget.dataset.community;
      this.selectCommunityData(community);
    },

    // 选择社区数据
    selectCommunityData(community) {
      this.setData({
        currentCommunity: community,
        selectedId: community.id,
        showPopup: false
      });

      // 保存选择的社区
      saveSelectedCommunity(community);

      // 触发选择事件
      this.triggerEvent('change', {
        community: community
      });

      wx.showToast({
        title: '已切换区域',
        icon: 'success',
        duration: 1500
      });
    },

    // 打开地图选择
    openMapSelector() {
      wx.navigateTo({
        url: '/pages/map-selector/index'
      });
    }
  }
});
