<view class="home-card" bindtap="onCardTap" data-item="{{item}}">
  <t-image t-class="home-card__image" src="{{item.url}}" mode="aspectFill" />
  <view class="home-card__info">
    <view class="home-card__title">{{item.title}}</view>
    <view class="home-card__desc">{{item.desc}}</view>
    <view class="home-card__meta">
      <view class="home-card__location">
        <t-icon name="location" size="24rpx" />
        <text>{{item.location}} · {{item.distance}}</text>
      </view>
      <view class="home-card__time">{{item.publishTime}}</view>
    </view>
    <view class="home-card__tag-group">
      <t-tag wx:for="{{item.tags}}" wx:key="index" size="small" variant="light" theme="{{item.theme}}">{{item.text}}</t-tag>
    </view>
    <view class="home-card__footer">
      <view class="home-card__publisher">
        <t-avatar size="small" image="{{item.publisher.avatar}}" />
        <text class="publisher-name">{{item.publisher.name}}</text>
        <view class="trust-level">
          <t-icon name="star-filled" size="24rpx" color="#ffa500" />
          <text>{{item.publisher.trustLevel}}</text>
        </view>
      </view>
      <view class="home-card__stats">
        <view class="stat-item">
          <t-icon name="browse" size="24rpx" />
          <text>{{item.viewCount}}</text>
        </view>
        <view class="stat-item">
          <t-icon name="heart" size="24rpx" />
          <text>{{item.likeCount}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
