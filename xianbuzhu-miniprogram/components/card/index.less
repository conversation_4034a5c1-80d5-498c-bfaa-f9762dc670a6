@import '/variable.less';

.home-card {
  display: inline-flex;
  flex-direction: column;
  width: 340rpx;
  min-height: 520rpx;
  border-radius: 12rpx;
  background: @bg-color-white;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;

  &__image {
    width: 340rpx;
    height: 240rpx;
    border-radius: 12rpx 12rpx 0 0;
  }

  &__info {
    padding: 24rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.4;
    margin-bottom: 16rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  &__meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    font-size: 24rpx;
    color: #999;
  }

  &__location {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }

  &__tag-group {
    display: flex;
    gap: 12rpx;
    margin-bottom: 16rpx;
    flex-wrap: wrap;
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
  }

  &__publisher {
    display: flex;
    align-items: center;
    gap: 12rpx;

    .publisher-name {
      font-size: 26rpx;
      color: #333;
    }

    .trust-level {
      display: flex;
      align-items: center;
      gap: 4rpx;
      font-size: 24rpx;
      color: #ffa500;
    }
  }

  &__stats {
    display: flex;
    gap: 24rpx;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 6rpx;
      font-size: 24rpx;
      color: #999;
    }
  }
}
