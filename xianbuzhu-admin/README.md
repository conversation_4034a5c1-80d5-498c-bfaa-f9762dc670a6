# 社区闲置物品置换平台 - 管理后台

## 项目概述

这是社区闲置物品置换平台的管理后台项目，为平台管理员提供用户管理、内容审核、数据统计、系统配置等功能。

## 🚧 当前状态

- **编译状态**: 存在214个TypeScript类型错误，需要进一步优化
- **核心功能**: 基础页面和组件结构完整
- **待优化**: 类型定义、API接口、组件属性等

## 技术栈

- **框架**: React 18 + TypeScript
- **UI组件库**: Ant Design 5.x
- **状态管理**: Redux Toolkit + RTK Query
- **路由**: React Router 6
- **图表**: ECharts + @ant-design/charts
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier
- **测试**: Jest + React Testing Library

## 项目结构

```
admin-dashboard/
├── public/                    # 静态资源
├── src/
│   ├── components/           # 通用组件
│   │   ├── Layout/          # 布局组件
│   │   ├── Charts/          # 图表组件
│   │   └── Common/          # 通用组件
│   ├── pages/               # 页面组件
│   │   ├── Dashboard/       # 仪表板
│   │   ├── Users/           # 用户管理
│   │   ├── Items/           # 物品管理
│   │   ├── Exchanges/       # 交换管理
│   │   ├── Reports/         # 举报管理
│   │   ├── Statistics/      # 数据统计
│   │   └── Settings/        # 系统设置
│   ├── services/            # API服务
│   ├── store/               # Redux状态管理
│   ├── hooks/               # 自定义Hooks
│   ├── utils/               # 工具函数
│   ├── types/               # TypeScript类型
│   └── styles/              # 样式文件
├── tests/                   # 测试文件
└── docs/                    # 文档
```

## 主要功能

### 核心管理功能
- 📊 数据仪表板（用户、物品、交换统计）
- 👥 用户管理（查看、编辑、封禁、解封）
- 📦 物品管理（审核、删除、分类管理）
- 🔄 交换管理（监控、纠纷处理）
- 🚨 举报管理（处理用户举报）
- 📈 数据统计（多维度数据分析）
- ⚙️ 系统设置（参数配置、权限管理）

### 特色功能
- 🎯 实时数据监控
- 📋 批量操作支持
- 🔍 高级搜索和筛选
- 📊 可视化图表展示
- 📤 数据导出功能
- 🔔 消息通知中心

## 环境设置

### 前置要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd admin-dashboard

# 安装依赖
npm install

# 复制环境变量配置
cp .env.example .env.local
```

### 环境变量配置
```bash
# .env.local
# API服务地址
VITE_API_BASE_URL=http://localhost:3000/api/admin

# 应用配置
VITE_APP_TITLE=社区置换管理后台
VITE_APP_VERSION=1.0.0

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
```

## 开发

### 启动开发服务器
```bash
# 开发模式
npm run dev

# 预览构建结果
npm run preview
```

### 构建
```bash
# 构建生产版本
npm run build

# 构建并分析包大小
npm run build:analyze
```

## 页面功能详解

### 1. 仪表板 (Dashboard)
```typescript
// src/pages/Dashboard/index.tsx
import { Card, Row, Col, Statistic } from 'antd'
import { UserOutlined, ShoppingOutlined, SwapOutlined } from '@ant-design/icons'
import { useGetDashboardStatsQuery } from '@/services/api'

const Dashboard: React.FC = () => {
  const { data: stats, isLoading } = useGetDashboardStatsQuery()

  return (
    <div className="dashboard">
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats?.totalUsers}
              prefix={<UserOutlined />}
              loading={isLoading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总物品数"
              value={stats?.totalItems}
              prefix={<ShoppingOutlined />}
              loading={isLoading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总交换数"
              value={stats?.totalExchanges}
              prefix={<SwapOutlined />}
              loading={isLoading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={stats?.activeUsers}
              suffix="/ 日"
              loading={isLoading}
            />
          </Card>
        </Col>
      </Row>
      
      {/* 图表组件 */}
      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={12}>
          <Card title="用户增长趋势">
            <UserGrowthChart />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="交换成功率">
            <ExchangeSuccessChart />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
```

### 2. 用户管理 (Users)
```typescript
// src/pages/Users/<USER>
import { Table, Button, Space, Tag, Modal, message } from 'antd'
import { useGetUsersQuery, useUpdateUserStatusMutation } from '@/services/api'

const Users: React.FC = () => {
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const { data, isLoading } = useGetUsersQuery({ page, pageSize })
  const [updateUserStatus] = useUpdateUserStatusMutation()

  const handleStatusChange = async (userId: string, status: string) => {
    try {
      await updateUserStatus({ userId, status }).unwrap()
      message.success('用户状态更新成功')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const columns = [
    {
      title: '用户ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '用户名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '信任等级',
      dataIndex: 'trustLevel',
      key: 'trustLevel',
      render: (trustLevel: any) => (
        <Tag color={trustLevel.level > 3 ? 'green' : 'orange'}>
          等级 {trustLevel.level}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'status',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '正常' : '封禁'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="middle">
          <Button size="small" onClick={() => handleViewUser(record.id)}>
            查看
          </Button>
          <Button 
            size="small" 
            danger={record.isActive}
            onClick={() => handleStatusChange(
              record.id, 
              record.isActive ? 'suspended' : 'active'
            )}
          >
            {record.isActive ? '封禁' : '解封'}
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div className="users-page">
      <div className="page-header">
        <h2>用户管理</h2>
        <Space>
          <Button type="primary">导出数据</Button>
        </Space>
      </div>
      
      <Table
        columns={columns}
        dataSource={data?.users}
        loading={isLoading}
        pagination={{
          current: page,
          pageSize,
          total: data?.total,
          onChange: (page, size) => {
            setPage(page)
            setPageSize(size)
          },
        }}
      />
    </div>
  )
}
```

### 3. 数据统计 (Statistics)
```typescript
// src/pages/Statistics/index.tsx
import { Card, DatePicker, Select, Row, Col } from 'antd'
import { Line, Column, Pie } from '@ant-design/charts'
import { useGetStatisticsQuery } from '@/services/api'

const Statistics: React.FC = () => {
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>()
  const [metric, setMetric] = useState('users')
  
  const { data: chartData } = useGetStatisticsQuery({
    type: metric,
    startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
    endDate: dateRange?.[1]?.format('YYYY-MM-DD'),
  })

  const lineConfig = {
    data: chartData?.trend || [],
    xField: 'date',
    yField: 'value',
    smooth: true,
    color: '#1890ff',
  }

  const columnConfig = {
    data: chartData?.distribution || [],
    xField: 'category',
    yField: 'count',
    color: '#52c41a',
  }

  return (
    <div className="statistics-page">
      <div className="page-header">
        <h2>数据统计</h2>
        <Space>
          <DatePicker.RangePicker 
            value={dateRange}
            onChange={setDateRange}
          />
          <Select 
            value={metric} 
            onChange={setMetric}
            style={{ width: 120 }}
          >
            <Select.Option value="users">用户</Select.Option>
            <Select.Option value="items">物品</Select.Option>
            <Select.Option value="exchanges">交换</Select.Option>
          </Select>
        </Space>
      </div>

      <Row gutter={16}>
        <Col span={12}>
          <Card title="趋势分析">
            <Line {...lineConfig} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="分布统计">
            <Column {...columnConfig} />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
```

## 状态管理

### Redux Store配置
```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit'
import { api } from '@/services/api'
import authSlice from './slices/authSlice'
import uiSlice from './slices/uiSlice'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    [api.reducerPath]: api.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(api.middleware),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
```

### API服务配置
```typescript
// src/services/api.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

export const api = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token
      if (token) {
        headers.set('authorization', `Bearer ${token}`)
      }
      return headers
    },
  }),
  tagTypes: ['User', 'Item', 'Exchange', 'Report', 'Statistics'],
  endpoints: (builder) => ({
    // 用户相关
    getUsers: builder.query<UsersResponse, UsersQuery>({
      query: (params) => ({
        url: '/users',
        params,
      }),
      providesTags: ['User'],
    }),
    
    updateUserStatus: builder.mutation<void, UpdateUserStatusRequest>({
      query: ({ userId, status }) => ({
        url: `/users/${userId}/status`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: ['User'],
    }),

    // 统计相关
    getDashboardStats: builder.query<DashboardStats, void>({
      query: () => '/dashboard/stats',
      providesTags: ['Statistics'],
    }),

    getStatistics: builder.query<StatisticsData, StatisticsQuery>({
      query: (params) => ({
        url: '/statistics/charts',
        params,
      }),
      providesTags: ['Statistics'],
    }),
  }),
})

export const {
  useGetUsersQuery,
  useUpdateUserStatusMutation,
  useGetDashboardStatsQuery,
  useGetStatisticsQuery,
} = api
```

## 组件库

### 通用组件
```typescript
// src/components/Common/PageHeader.tsx
interface PageHeaderProps {
  title: string
  extra?: React.ReactNode
  breadcrumb?: string[]
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  extra,
  breadcrumb,
}) => {
  return (
    <div className="page-header">
      {breadcrumb && (
        <Breadcrumb>
          {breadcrumb.map((item, index) => (
            <Breadcrumb.Item key={index}>{item}</Breadcrumb.Item>
          ))}
        </Breadcrumb>
      )}
      <div className="page-header-content">
        <h1>{title}</h1>
        {extra && <div className="page-header-extra">{extra}</div>}
      </div>
    </div>
  )
}
```

### 图表组件
```typescript
// src/components/Charts/UserGrowthChart.tsx
import { Line } from '@ant-design/charts'
import { useGetStatisticsQuery } from '@/services/api'

export const UserGrowthChart: React.FC = () => {
  const { data, isLoading } = useGetStatisticsQuery({
    type: 'users',
    period: 'month',
  })

  const config = {
    data: data?.trend || [],
    xField: 'date',
    yField: 'count',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 3,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: '新增用户',
        value: datum.count,
      }),
    },
  }

  if (isLoading) {
    return <div>加载中...</div>
  }

  return <Line {...config} />
}
```

## 路由配置

```typescript
// src/router/index.tsx
import { createBrowserRouter } from 'react-router-dom'
import { Layout } from '@/components/Layout'
import Dashboard from '@/pages/Dashboard'
import Users from '@/pages/Users'
import Items from '@/pages/Items'
import Statistics from '@/pages/Statistics'

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Dashboard />,
      },
      {
        path: 'users',
        element: <Users />,
      },
      {
        path: 'items',
        element: <Items />,
      },
      {
        path: 'statistics',
        element: <Statistics />,
      },
    ],
  },
  {
    path: '/login',
    element: <Login />,
  },
])
```

## 测试

### 组件测试
```typescript
// src/pages/Dashboard/__tests__/Dashboard.test.tsx
import { render, screen } from '@testing-library/react'
import { Provider } from 'react-redux'
import { store } from '@/store'
import Dashboard from '../index'

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  )
}

describe('Dashboard', () => {
  it('renders dashboard statistics', () => {
    renderWithProvider(<Dashboard />)
    
    expect(screen.getByText('总用户数')).toBeInTheDocument()
    expect(screen.getByText('总物品数')).toBeInTheDocument()
    expect(screen.getByText('总交换数')).toBeInTheDocument()
    expect(screen.getByText('活跃用户')).toBeInTheDocument()
  })
})
```

### API测试
```typescript
// src/services/__tests__/api.test.ts
import { api } from '../api'
import { store } from '@/store'

describe('API', () => {
  it('should fetch users successfully', async () => {
    const result = await store.dispatch(
      api.endpoints.getUsers.initiate({ page: 1, pageSize: 10 })
    )
    
    expect(result.data).toBeDefined()
    expect(result.data.users).toBeInstanceOf(Array)
  })
})
```

## 构建和部署

### Vite配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          charts: ['@ant-design/charts'],
        },
      },
    },
  },
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
})
```

### Docker部署
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 性能优化

### 代码分割
```typescript
// 路由懒加载
const Dashboard = lazy(() => import('@/pages/Dashboard'))
const Users = lazy(() => import('@/pages/Users'))

// 组件懒加载
const LazyChart = lazy(() => import('@/components/Charts/UserGrowthChart'))
```

### 缓存策略
```typescript
// RTK Query缓存配置
export const api = createApi({
  // ...
  keepUnusedDataFor: 60, // 60秒
  refetchOnMountOrArgChange: 30, // 30秒内不重新请求
})
```

## 相关项目

- [微信小程序](../miniprogram/README.md)
- [后端API服务](../backend/README.md)

## 开发团队

- 前端开发：负责页面开发和用户体验优化
- UI/UX设计：负责界面设计和交互设计
- 产品经理：负责需求分析和功能规划

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。