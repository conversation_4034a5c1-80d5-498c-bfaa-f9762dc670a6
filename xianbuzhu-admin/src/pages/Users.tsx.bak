import React, { useState } from 'react'
import { Button, Card, Table, Input, Space, Popconfirm, message, Form, Select } from 'antd'
import { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { useGetUsersQuery, useDeleteItemMutation as useDeleteUserMutation } from '@services/api'
import { useNavigate } from 'react-router-dom'

const { Search } = Input
const { Option } = Select

const Users: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [form] = Form.useForm()
  const [deleteUser] = useDeleteUserMutation()
  const navigate = useNavigate()

  const { data, isLoading, refetch } = useGetUsersQuery({
    page: currentPage,
    pageSize,
    search: searchText,
    status: filterStatus
  })

  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  const handleDelete = async (id: string) => {
    try {
      await deleteUser(id).unwrap()
      message.success(`删除用户 ${id} 成功`)
      refetch()
    } catch (err: any) {
      message.error(err?.data?.error?.message || '删除用户失败')
    }
  }

  const handleFilter = (status: string) => {
    setFilterStatus(status)
    setCurrentPage(1)
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span style={{ color: status === 'active' ? '#52c41a' : '#ff4d4f' }}>
          {status === 'active' ? '活跃' : '禁用'}
        </span>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => navigate(`/users/${record.id}`)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
            danger
            icon={<DeleteOutlined />}
            size="small"
          >
            删除
          </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="用户管理"
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/users/create')}
            >
              添加用户
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: 16, display: 'flex', flexWrap: 'wrap', gap: 10 }}>
          <Search
            placeholder="搜索用户名或邮箱"
            allowClear
            enterButton={<SearchOutlined />}
            size="middle"
            onSearch={handleSearch}
            style={{ width: 300 }}
          />
          <Form form={form} layout="inline">
            <Form.Item name="status">
              <Select
                placeholder="筛选状态"
                allowClear
                style={{ width: 150 }}
                onChange={handleFilter}
              >
                <Option value="all">全部</Option>
                <Option value="active">活跃</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </Form.Item>
          </Form>
        </div>

        <Table
          columns={columns}
          dataSource={data?.data?.items || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize,
            total: data?.data?.total || 0,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
          }}
        />
      </Card>
    </div>
  )
}

export default Users