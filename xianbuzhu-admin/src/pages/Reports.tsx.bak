import React from 'react'
import { But<PERSON>, Card, Table, Input, Space, Popconfirm, message, Form, Select } from 'antd'
import { SearchOutlined, EditOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { useGetReportsQuery, useHandleReportMutation } from '@services/api'
import { useNavigate } from 'react-router-dom'
import { useState } from 'react'
import type { TablePaginationConfig } from 'antd'
// 定义Report类型
interface Report {
  id: string
  content: string
  item: { name?: string }
  reporter: { username?: string }
  reportedUser: { username?: string }
  createdAt: string
  status: 'pending' | 'resolved' | 'rejected'
}

const { Search } = Input
const { Option } = Select

const Reports: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [handleReport] = useHandleReportMutation()
  const navigate = useNavigate()

  const { data, isLoading, refetch } = useGetReportsQuery({
    page: currentPage,
    pageSize,
    search: searchText,
    status: filterStatus
  })

  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  const handleUpdateStatus = async (id: string, status: string) => {
    try {
      await handleReport({
        reportId: id,
        action: status
      }).unwrap()
      message.success('更新举报状态成功')
      refetch()
    } catch (err: any) {
      message.error((err as any)?.message || '更新举报状态失败')
    }
  }

  const handleFilter = (status: string) => {
    setFilterStatus(status)
    setCurrentPage(1)
  }

  const handleViewDetails = (id: string) => {
    navigate(`/reports/${id}`)
  }

  const columns = [
    {
      title: '举报ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '举报内容',
      dataIndex: 'content',
      key: 'content',
      width: 300,
    },
    {
      title: '举报物品',
      dataIndex: 'item',
      key: 'item',
      render: (item: any) => item?.name || '未知'
    },
    {
      title: '举报用户',
      dataIndex: 'reporter',
      key: 'reporter',
      render: (user: any) => user?.username || '未知'
    },
    {
      title: '被举报用户',
      dataIndex: 'reportedUser',
      key: 'reportedUser',
      render: (user: any) => user?.username || '未知'
    },
    {
      title: '举报时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = '#faad14'
        let text = '处理中'
        let icon = <AlertCircleOutlined size={16} />

        if (status === 'resolved') {
          color = '#52c41a'
          text = '已解决'
          icon = <CheckCircleOutlined size={16} />
        } else if (status === 'rejected') {
          color = '#ff4d4f'
          text = '已驳回'
          icon = <AlertCircleOutlined size={16} />
        }

        return (
          <span style={{ color, display: 'flex', alignItems: 'center' }}>
            {icon}
            <span style={{ marginLeft: 4 }}>{text}</span>
          </span>
        )
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Report) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleViewDetails(record.id)}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <Space size="middle">
              <Popconfirm
                title="确定要标记为已解决吗？"
                onConfirm={() => handleUpdateStatus(record.id, 'resolved')}
                okText="确定"
                cancelText="取消"
              >
                <Button type="primary" size="small">
              解决
            </Button>
              </Popconfirm>
              <Popconfirm
                title="确定要驳回吗？"
                onConfirm={() => handleUpdateStatus(record.id, 'rejected')}
                okText="确定"
                cancelText="取消"
              >
                <Button type="default" size="small">
              驳回
            </Button>
              </Popconfirm>
            </Space>
          )}
        </Space>
      )
    },
  ]

  const pagination: TablePaginationConfig = {
    current: currentPage,
    pageSize: pageSize,
    total: data?.data?.total || 0,
    onChange: (page, size) => {
      setCurrentPage(page)
      setPageSize(size)
    },
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100']
  }

  const reportsData = data?.data?.items || []

  return (
    <div style={{ padding: 24 }}>
      <Card title="举报管理">
        <div style={{ marginBottom: 16, display: 'flex', flexWrap: 'wrap', gap: 10 }}>
          <Search
            placeholder="搜索举报内容或ID"
            allowClear
            enterButton={<SearchOutlined />}
            size="middle"
            onSearch={handleSearch}
            style={{ width: 300 }}
          />
          <Form layout="inline">
            <Form.Item name="status">
              <Select
                placeholder="筛选状态"
                allowClear
                style={{ width: 150 }}
                onChange={handleFilter}
              >
                <Option value="all">全部</Option>
                <Option value="pending">处理中</Option>
                <Option value="resolved">已解决</Option>
                <Option value="rejected">已驳回</Option>
              </Select>
            </Form.Item>
          </Form>
        </div>

        <Table
          columns={columns}
          dataSource={reportsData}
          loading={isLoading}
          rowKey="id"
          pagination={pagination}
        />
      </Card>
    </div>
  )
}

export default Reports