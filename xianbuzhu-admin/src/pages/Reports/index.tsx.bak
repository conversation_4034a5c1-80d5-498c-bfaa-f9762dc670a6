import React, { useEffect, useState } from 'react'
import { Card, Table, Button, Input, Select, message, Popconfirm, Spin, Modal, Divider } from 'antd'
import { SearchOutlined, FilterOutlined, EyeOutlined, CheckOutlined, CloseOutlined, AlertCircleOutlined, UserOutlined, ItemOutlined, MessageOutlined, CalendarOutlined } from '@ant-design/icons'
import { useAppDispatch, useAppSelector } from '@store/index'
import { setBreadcrumb, setPageTitle, setTableSettings, selectTableSettings } from '@store/slices/uiSlice'
import { useGetReportsQuery, useUpdateReportStatusMutation } from '@services/api'
import { Report, ReportStatus, ReportType, User, Item, Exchange } from '@types/api'
import { selectTheme } from '@store/slices/uiSlice'

const { Search } = Input
const { Option } = Select
const { confirm } = Modal

const Reports: React.FC = () => {
  const dispatch = useAppDispatch()
  const theme = useAppSelector(selectTheme)
  const tableKey = 'reports'
  const tableSettings = useAppSelector((state) => selectTableSettings(state, tableKey))
  const [searchKeyword, setSearchKeyword] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(tableSettings.pageSize || 1)
  const [pageSize, setPageSize] = useState(tableSettings.pageSize || 10)
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)

  // API hooks
  const { data, isLoading, error, refetch } = useGetReportsQuery({
    page: currentPage,
    pageSize,
    status: statusFilter,
    type: typeFilter,
    search: searchKeyword
  })
  const [updateReportStatus] = useUpdateReportStatusMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '举报管理' }
    ]))
    dispatch(setPageTitle('举报管理'))
  }, [dispatch])

  // 保存表格设置
  useEffect(() => {
    dispatch(setTableSettings({
      tableKey,
      settings: {
        pageSize
      }
    }))
  }, [pageSize, dispatch, tableKey])

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value)
    setCurrentPage(1)
  }

  // 处理状态筛选
  const handleStatusChange = (value: string) => {
    setStatusFilter(value)
    setCurrentPage(1)
  }

  // 处理类型筛选
  const handleTypeChange = (value: string) => {
    setTypeFilter(value)
    setCurrentPage(1)
  }

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrentPage(pagination.current)
    setPageSize(pagination.pageSize)
  }

  // 获取举报状态文本
  const getStatusText = (status: ReportStatus) => {
    switch (status) {
      case ReportStatus.PENDING: return '待处理'
      case ReportStatus.ACCEPTED: return '已受理'
      case ReportStatus.REJECTED: return '已驳回'
      case ReportStatus.RESOLVED: return '已解决'
      default: return status
    }
  }

  // 获取举报状态样式
  const getStatusStyle = (status: ReportStatus) => {
    switch (status) {
      case ReportStatus.PENDING: return { bg: '#faad1420', color: '#faad14' }
      case ReportStatus.ACCEPTED: return { bg: '#1890ff20', color: '#1890ff' }
      case ReportStatus.REJECTED: return { bg: '#52c41a20', color: '#52c41a' }
      case ReportStatus.RESOLVED: return { bg: '#52c41a20', color: '#52c41a' }
      default: return { bg: '#f0f0f0', color: '#666' }
    }
  }

  // 获取举报类型文本
  const getTypeText = (type: ReportType) => {
    switch (type) {
      case ReportType.ITEM: return '物品举报'
      case ReportType.USER: return '用户举报'
      case ReportType.EXCHANGE: return '交换举报'
      case ReportType.COMMENT: return '评论举报'
      default: return type
    }
  }

  // 获取举报类型图标
  const getTypeIcon = (type: ReportType) => {
    switch (type) {
      case ReportType.ITEM: return <ItemOutlined size={16} />
      case ReportType.USER: return <UserOutlined size={16} />
      case ReportType.EXCHANGE: return <MessageOutlined size={16} />
      case ReportType.COMMENT: return <MessageOutlined size={16} />
      default: return <AlertCircleOutlined size={16} />
    }
  }

  // 处理举报状态更新
  const handleStatusUpdate = async (reportId: string, status: ReportStatus, reason?: string) => {
    try {
      // 这里应该调用更新举报状态的API
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      message.success('举报状态更新成功')
      refetch()
      setSelectedReport(null)
    } catch (err) {
      message.error(`更新失败: ${err instanceof Error ? err.message : '未知错误'}`)
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '举报ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '举报类型',
      key: 'type',
      render: (record: Report) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div
            style={{
              width: '24px',
              height: '24px',
              borderRadius: '4px',
              backgroundColor: '#1890ff20',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: '8px',
              color: '#1890ff'
            }}
          >
            {getTypeIcon(record.type)}
          </div>
          <span>{getTypeText(record.type)}</span>
        </div>
      ),
      width: 120
    },
    {
      title: '举报对象',
      key: 'target',
      render: (record: Report) => {
        if (record.type === ReportType.ITEM && record.targetItem) {
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div
                style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '4px',
                  backgroundColor: '#f0f0f0',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: '8px',
                  overflow: 'hidden'
                }}
              >
                {record.targetItem.images && record.targetItem.images.length > 0 ? (
                  <img src={record.targetItem.images[0]} alt={record.targetItem.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                ) : (
                  <ItemOutlined size={16} />
                )}
              </div>
              <div style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                {record.targetItem.title}
              </div>
            </div>
          )
        } else if (record.type === ReportType.USER && record.targetUser) {
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div
                style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  backgroundColor: '#f0f0f0',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: '8px',
                  overflow: 'hidden'
                }}
              >
                {record.targetUser.avatar ? (
                  <img src={record.targetUser.avatar} alt={record.targetUser.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                ) : (
                  <UserOutlined size={16} />
                )}
              </div>
              <div style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                {record.targetUser.name}
              </div>
            </div>
          )
        } else if (record.type === ReportType.EXCHANGE && record.targetExchange) {
          return (
            <div style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
              交换 #{record.targetExchange.id.substring(0, 8)}...
            </div>
          )
        } else if (record.type === ReportType.COMMENT && record.targetCommentId) {
          return (
            <div style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
              评论 #{record.targetCommentId.substring(0, 8)}...
            </div>
          )
        }
        return '未知对象'
      }
    },
    {
      title: '举报原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 200
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Report) => {
        const style = getStatusStyle(record.status)
        return (
          <span
            style={{
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              backgroundColor: style.bg,
              color: style.color
            }}
          >
            {getStatusText(record.status)}
          </span>
        )
      },
      width: 100
    },
    {
      title: '举报时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => (
        <span>{new Date(text).toLocaleString()}</span>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: Report) => (
        <div style={{ display: 'flex', justifyContent: 'center', gap: '8px' }}>
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined size={16} />}
            onClick={() => setSelectedReport(record)}
          >
            查看
          </Button>
          {record.status === ReportStatus.PENDING && (
            <Popconfirm
              title="确定要受理该举报吗？"
              onConfirm={() => handleStatusUpdate(record.id, ReportStatus.ACCEPTED)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="success"
                size="small"
                icon={<CheckOutlined size={16} />}
              >
                受理
              </Button>
            </Popconfirm>
          )}
          {record.status === ReportStatus.PENDING && (
            <Popconfirm
              title="确定要驳回该举报吗？"
              onConfirm={() => handleStatusUpdate(record.id, ReportStatus.REJECTED)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="danger"
                size="small"
                icon={<CloseOutlined size={16} />}
              >
                驳回
              </Button>
            </Popconfirm>
          )}
        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
          <AlertCircleOutlined style={{ marginRight: '8px' }} />
          <span>获取数据失败: {error.error || '未知错误'}</span>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px' }}>
      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: '24px'
        }}
      >
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', alignItems: 'center' }}>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <Search
              placeholder="搜索举报..."
              prefix={<SearchOutlined />}
              allowClear
              enterButton="搜索"
              size="middle"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flexWrap: 'wrap' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <FilterOutlined style={{ marginRight: '8px', color: '#666' }} />
              <Select
                placeholder="筛选状态"
                size="middle"
                value={statusFilter}
                onChange={handleStatusChange}
                allowClear
              >
                <Option value={ReportStatus.PENDING}>待处理</Option>
                <Option value={ReportStatus.ACCEPTED}>已受理</Option>
                <Option value={ReportStatus.REJECTED}>已驳回</Option>
                <Option value={ReportStatus.RESOLVED}>已解决</Option>
              </Select>
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <FilterOutlined style={{ marginRight: '8px', color: '#666' }} />
              <Select
                placeholder="筛选类型"
                size="middle"
                value={typeFilter}
                onChange={handleTypeChange}
                allowClear
              >
                <Option value={ReportType.ITEM}>物品举报</Option>
                <Option value={ReportType.USER}>用户举报</Option>
                <Option value={ReportType.EXCHANGE}>交换举报</Option>
                <Option value={ReportType.COMMENT}>评论举报</Option>
              </Select>
            </div>
          </div>
        </div>
      </Card>

      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
        }}
      >
        <Table
          columns={columns}
          dataSource={data?.data?.items || []}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize,
            total: data?.data?.total || 0,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true
          }}
          onChange={handleTableChange}
          loading={isLoading}
          style={{ color: theme === 'dark' ? '#fff' : '#333' }}
        />
      </Card>

      {/* 举报详情弹窗 */}
      {selectedReport && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000
          }}
          onClick={() => setSelectedReport(null)}
        >
          <div
            style={{
              backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
              borderRadius: '8px',
              width: '80%',
              maxWidth: '800px',
              maxHeight: '80vh',
              overflow: 'auto',
              padding: '24px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
              <h2 style={{ margin: 0, color: theme === 'dark' ? '#fff' : '#333' }}>举报详情</h2>
              <Button
                type="text"
                onClick={() => setSelectedReport(null)}
                style={{ color: theme === 'dark' ? '#999' : '#666' }}
              >
                关闭
              </Button>
            </div>

            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ marginBottom: '16px', color: theme === 'dark' ? '#fff' : '#333' }}>举报信息</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>举报ID</p>
                  <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.id}</p>
                </div>
                <div>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>举报类型</p>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div
                      style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '4px',
                        backgroundColor: '#1890ff20',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: '8px',
                        color: '#1890ff'
                      }}
                    >
                      {getTypeIcon(selectedReport.type)}
                    </div>
                    <span>{getTypeText(selectedReport.type)}</span>
                  </div>
                </div>
                <div>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>举报状态</p>
                  <span
                    style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      ...getStatusStyle(selectedReport.status)
                    }}
                  >
                    {getStatusText(selectedReport.status)}
                  </span>
                </div>
                <div>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>举报时间</p>
                  <p style={{ margin: 0, color: theme === 'dark' ? '#fff' : '#333' }}>{new Date(selectedReport.createdAt).toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ marginBottom: '16px', color: theme === 'dark' ? '#fff' : '#333' }}>举报方信息</h3>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div
                  style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '50%',
                    backgroundColor: '#f0f0f0',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: '16px',
                    overflow: 'hidden'
                  }}
                >
                  {selectedReport.reporter?.avatar ? (
                    <img src={selectedReport.reporter.avatar} alt={selectedReport.reporter.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                  ) : (
                    <UserOutlined size={24} />
                  )}
                </div>
                <div>
                  <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.reporter?.name}</p>
                  <p style={{ margin: '4px 0', fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>ID: {selectedReport.reporter?.id}</p>
                  <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>注册时间: {selectedReport.reporter?.createdAt ? new Date(selectedReport.reporter.createdAt).toLocaleDateString() : '未知'}</p>
                </div>
              </div>
            </div>

            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ marginBottom: '16px', color: theme === 'dark' ? '#fff' : '#333' }}>举报对象</h3>
              {selectedReport.type === ReportType.ITEM && selectedReport.targetItem ? (
                <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
                  <div
                    style={{
                      width: '120px',
                      height: '120px',
                      borderRadius: '8px',
                      backgroundColor: '#f0f0f0',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      overflow: 'hidden',
                      flexShrink: 0
                    }}
                  >
                    {selectedReport.targetItem.images && selectedReport.targetItem.images.length > 0 ? (
                      <img src={selectedReport.targetItem.images[0]} alt={selectedReport.targetItem.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                    ) : (
                      <ItemOutlined size={32} />
                    )}
                  </div>
                  <div style={{ flex: 1, minWidth: '200px' }}>
                    <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333', fontSize: '16px' }}>{selectedReport.targetItem.title}</p>
                    <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>{selectedReport.targetItem.description}</p>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px', marginBottom: '8px' }}>
                      <div>
                        <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>估值</p>
                        <p style={{ margin: 0, fontWeight: 'bold', color: '#1890ff' }}>¥{selectedReport.targetItem.estimatedValue}</p>
                      </div>
                      <div>
                        <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>状态</p>
                        <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetItem.status === 'available' ? '可交换' : '已交换'}</p>
                      </div>
                      <div>
                        <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>发布时间</p>
                        <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{new Date(selectedReport.targetItem.createdAt).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div
                        style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '50%',
                          backgroundColor: '#f0f0f0',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          marginRight: '8px',
                          overflow: 'hidden'
                        }}
                      >
                        {selectedReport.targetItem.publisher?.avatar ? (
                          <img src={selectedReport.targetItem.publisher.avatar} alt={selectedReport.targetItem.publisher.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                        ) : (
                          <UserOutlined size={16} />
                        )}
                      </div>
                      <div>
                        <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetItem.publisher?.name}</p>
                        <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>发布者</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : selectedReport.type === ReportType.USER && selectedReport.targetUser ? (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div
                    style={{
                      width: '120px',
                      height: '120px',
                      borderRadius: '50%',
                      backgroundColor: '#f0f0f0',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginRight: '16px',
                      overflow: 'hidden'
                    }}
                  >
                    {selectedReport.targetUser.avatar ? (
                      <img src={selectedReport.targetUser.avatar} alt={selectedReport.targetUser.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                    ) : (
                      <UserOutlined size={32} />
                    )}
                  </div>
                  <div>
                    <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333', fontSize: '16px' }}>{selectedReport.targetUser.name}</p>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px', margin: '8px 0' }}>
                      <div>
                        <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>ID</p>
                        <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetUser.id}</p>
                      </div>
                      <div>
                        <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>信任分数</p>
                        <p style={{ margin: 0, fontWeight: 'bold', color: '#1890ff' }}>{selectedReport.targetUser.trustScore}</p>
                      </div>
                      <div>
                        <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>积分</p>
                        <p style={{ margin: 0, fontWeight: 'bold', color: '#1890ff' }}>{selectedReport.targetUser.points}</p>
                      </div>
                    </div>
                    <p style={{ margin: '4px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>注册时间: {new Date(selectedReport.targetUser.createdAt).toLocaleString()}</p>
                    <p style={{ margin: '4px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>最后登录: {new Date(selectedReport.targetUser.lastLoginAt).toLocaleString()}</p>
                    <p style={{ margin: '4px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>发布物品: {selectedReport.targetUser.publishedItemsCount || 0}</p>
                    <p style={{ margin: '4px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>交换次数: {selectedReport.targetUser.exchangesCount || 0}</p>
                  </div>
                </div>
              ) : selectedReport.type === ReportType.EXCHANGE && selectedReport.targetExchange ? (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>交换ID</p>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetExchange.id}</p>
                    </div>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>状态</p>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetExchange.status}</p>
                    </div>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>创建时间</p>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{new Date(selectedReport.targetExchange.createdAt).toLocaleString()}</p>
                    </div>
                  </div>
                  <Divider />
                  <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>请求方</p>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div
                          style={{
                            width: '40px',
                            height: '40px',
                            borderRadius: '50%',
                            backgroundColor: '#f0f0f0',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginRight: '12px',
                            overflow: 'hidden'
                          }}
                        >
                          {selectedReport.targetExchange.requester?.avatar ? (
                            <img src={selectedReport.targetExchange.requester.avatar} alt={selectedReport.targetExchange.requester.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                          ) : (
                            <UserOutlined size={20} />
                          )}
                        </div>
                        <div>
                          <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetExchange.requester?.name}</p>
                          <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>ID: {selectedReport.targetExchange.requester?.id}</p>
                        </div>
                      </div>
                    </div>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>物品拥有方</p>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div
                          style={{
                            width: '40px',
                            height: '40px',
                            borderRadius: '50%',
                            backgroundColor: '#f0f0f0',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginRight: '12px',
                            overflow: 'hidden'
                          }}
                        >
                          {selectedReport.targetExchange.targetItem?.publisher?.avatar ? (
                            <img src={selectedReport.targetExchange.targetItem.publisher.avatar} alt={selectedReport.targetExchange.targetItem.publisher.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                          ) : (
                            <UserOutlined size={20} />
                          )}
                        </div>
                        <div>
                          <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetExchange.targetItem?.publisher?.name}</p>
                          <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>ID: {selectedReport.targetExchange.targetItem?.publisher?.id}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Divider />
                  <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>请求方提供</p>
                      <div style={{ display: 'flex', gap: '12px' }}>
                        <div
                          style={{
                            width: '60px',
                            height: '60px',
                            borderRadius: '4px',
                            backgroundColor: '#f0f0f0',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            overflow: 'hidden',
                            flexShrink: 0
                          }}
                        >
                          {selectedReport.targetExchange.offeredItem?.images && selectedReport.targetExchange.offeredItem.images.length > 0 ? (
                            <img src={selectedReport.targetExchange.offeredItem.images[0]} alt={selectedReport.targetExchange.offeredItem.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                          ) : (
                            <ItemOutlined size={24} />
                          )}
                        </div>
                        <div style={{ flex: 1 }}>
                          <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetExchange.offeredItem?.title}</p>
                          <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>估值: ¥{selectedReport.targetExchange.offeredItem?.estimatedValue}</p>
                        </div>
                      </div>
                    </div>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>请求方获取</p>
                      <div style={{ display: 'flex', gap: '12px' }}>
                        <div
                          style={{
                            width: '60px',
                            height: '60px',
                            borderRadius: '4px',
                            backgroundColor: '#f0f0f0',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            overflow: 'hidden',
                            flexShrink: 0
                          }}
                        >
                          {selectedReport.targetExchange.targetItem?.images && selectedReport.targetExchange.targetItem.images.length > 0 ? (
                            <img src={selectedReport.targetExchange.targetItem.images[0]} alt={selectedReport.targetExchange.targetItem.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                          ) : (
                            <ItemOutlined size={24} />
                          )}
                        </div>
                        <div style={{ flex: 1 }}>
                          <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetExchange.targetItem?.title}</p>
                          <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>估值: ¥{selectedReport.targetExchange.targetItem?.estimatedValue}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : selectedReport.type === ReportType.COMMENT && selectedReport.targetCommentId ? (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>评论ID</p>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetCommentId}</p>
                    </div>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>所属对象</p>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetType === 'item' ? '物品' : '交换'}</p>
                    </div>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                      <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>所属对象ID</p>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.targetId}</p>
                    </div>
                  </div>
                  <div style={{ backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', padding: '16px', borderRadius: '8px' }}>
                    <p style={{ margin: 0, fontSize: '14px', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.commentContent || '评论内容不可用'}</p>
                  </div>
                </div>
              ) : (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100px' }}>
                  <p style={{ color: theme === 'dark' ? '#999' : '#666' }}>暂无举报对象详情</p>
                </div>
              )}
            </div>

            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ marginBottom: '16px', color: theme === 'dark' ? '#fff' : '#333' }}>举报详情</h3>
              <div style={{ backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', padding: '16px', borderRadius: '8px' }}>
                <p style={{ margin: '0 0 12px 0', fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>举报原因: {selectedReport.reason}</p>
                <p style={{ margin: 0, color: theme === 'dark' ? '#fff' : '#333' }}>{selectedReport.description || '无详细描述'}</p>
              </div>
            </div>

            <div style={{ marginTop: '24px', display: 'flex', justifyContent: 'flex-end', gap: '16px' }}>
              {selectedReport.status === ReportStatus.PENDING && (
                <Button
                  type="success"
                  icon={<CheckOutlined size={16} />}
                  onClick={() => handleStatusUpdate(selectedReport.id, ReportStatus.ACCEPTED)}
                >
                  受理
                </Button>
              )}
              {selectedReport.status === ReportStatus.PENDING && (
                <Button
                  type="danger"
                  icon={<CloseOutlined size={16} />}
                  onClick={() => handleStatusUpdate(selectedReport.id, ReportStatus.REJECTED)}
                >
                  驳回
                </Button>
              )}
              {selectedReport.status === ReportStatus.ACCEPTED && (
                <Button
                  type="primary"
                  icon={<CheckOutlined size={16} />}
                  onClick={() => handleStatusUpdate(selectedReport.id, ReportStatus.RESOLVED)}
                >
                  标记为已解决
                </Button>
              )}
              <Button
                type="default"
                onClick={() => setSelectedReport(null)}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Reports