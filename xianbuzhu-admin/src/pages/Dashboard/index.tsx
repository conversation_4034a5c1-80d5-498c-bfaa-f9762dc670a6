import React, { useEffect } from 'react'
import { Card, Row, Col, Statistic, Spin } from 'antd'
import { ArrowUpOutlined, ArrowDownOutlined, UserOutlined, ShoppingOutlined, SwapOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { useAppDispatch, useAppSelector } from '@store/index'
import { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'
import { useGetDashboardStatsQuery } from '@services/api'
// 暂时注释掉Chart组件导入
// import Chart from '../../components/Charts/Chart'
import { selectTheme } from '@store/slices/uiSlice'

const Dashboard: React.FC = () => {
  const dispatch = useAppDispatch()
  const theme = useAppSelector(selectTheme)
  const { data, isLoading, error } = useGetDashboardStatsQuery()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([{ title: '首页', path: '/dashboard' }]))
    dispatch(setPageTitle('仪表板'))
  }, [dispatch])

// 图表数据
const chartData: any[] = [
    { date: '1月', users: 120, items: 80, exchanges: 40 },
    { date: '2月', users: 140, items: 100, exchanges: 55 },
    { date: '3月', users: 180, items: 130, exchanges: 75 },
    { date: '4月', users: 220, items: 150, exchanges: 90 },
    { date: '5月', users: 250, items: 180, exchanges: 110 },
    { date: '6月', users: 300, items: 220, exchanges: 140 },
  ]

  // 统计卡片配置
  const statsCards = [
    {
      title: '总用户数',
      icon: <UserOutlined />,
      value: (data as any)?.users?.total || 0,
      growth: (data as any)?.users?.growth || 0,
      color: '#1890ff'
    },
    {
      title: '总物品数',
      icon: <ShoppingOutlined />,
      value: (data as any)?.items?.total || 0,
      growth: (data as any)?.items?.growth || 0,
      color: '#52c41a'
    },
    {
      title: '总交换数',
      icon: <SwapOutlined />,
      value: (data as any)?.exchanges?.total || 0,
      growth: (data as any)?.exchanges?.successRate || 0,
      color: '#faad14'
    },
    {
      title: '待处理举报',
      icon: <ExclamationCircleOutlined />,
      value: (data as any)?.reports?.pending || 0,
      growth: (data as any)?.reports?.todayNew || 0,
      color: '#ff4d4f'
    }
  ]

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
          <ExclamationCircleOutlined style={{ marginRight: '8px' }} />
          <span>获取数据失败: {(error as any)?.message || '未知错误'}</span>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px' }}>
      {/* 统计卡片区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {statsCards.map((card, index) => (
          <Col xs={24} sm={12} md={6} key={index}>
            <Card
              bordered={false}
              style={{
                backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div
                    style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: `${card.color}20`,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginRight: '16px'
                    }}
                  >
                    <span style={{ color: card.color }}>{card.icon}</span>
                  </div>
                  <div>
                    <p style={{ color: theme === 'dark' ? '#999' : '#666', margin: 0 }}>{card.title}</p>
                    <Statistic
                      value={card.value}
                      style={{ color: theme === 'dark' ? '#fff' : '#333' }}
                      valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}
                    />
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    color: card.growth >= 0 ? '#52c41a' : '#ff4d4f'
                  }}
                >
                  {card.growth >= 0 ? (
                    <ArrowUpOutlined size={16} style={{ marginRight: '4px' }} />
                  ) : (
                    <ArrowDownOutlined size={16} style={{ marginRight: '4px' }} />
                  )}
                  <span>{Math.abs(card.growth)}%</span>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} md={24}>
          <Card
            title="平台数据趋势"
            bordered={false}
            style={{
              backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
            }}
          >
            <div style={{ height: '300px' }}>
              {/* 暂时注释掉Chart组件 */}
              <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#f5f5f5', borderRadius: '8px' }}>
                <span style={{ color: '#999' }}>图表组件开发中...</span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard