import React, { useEffect } from 'react'
import { Card, Form, Input, Button, Divider, Typography, message } from 'antd'
import { LockOutlined, UserOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useLoginMutation } from '@services/api'
import { GlobalStyle } from '@styles/GlobalStyle'
import { useAppDispatch, useAppSelector } from '@store/index'
import { loginStart, loginSuccess, loginFailure, initializeAuth, selectAuthLoading, selectLoginError, clearError } from '@store/slices/authSlice'

const { Title } = Typography

const LoginPage: React.FC = () => {
  const [form] = Form.useForm()
  const [login] = useLoginMutation()
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const loading = useAppSelector(selectAuthLoading)
  const loginError = useAppSelector(selectLoginError)

  // 初始化认证状态
  useEffect(() => {
    dispatch(initializeAuth())
  }, [dispatch])

  // 处理错误消息
  useEffect(() => {
    if (loginError) {
      message.error(loginError)
      dispatch(clearError())
    }
  }, [loginError, dispatch])

  const onFinish = async (values: { username: string; password: string }) => {
    dispatch(loginStart())
    try {
      const response = await login(values).unwrap()
      if (response.success) {
        dispatch(loginSuccess({
          token: response.data?.token || '',
          admin: response.data?.admin || null
        }))
        message.success('登录成功')
        navigate('/dashboard')
      } else {
        dispatch(loginFailure(response.message || '登录失败'))
      }
    } catch (error: any) {
      dispatch(loginFailure(error?.data?.error?.message || '登录失败，请重试'))
    }
  }

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      background: '#f0f2f5'
    }}>
      <GlobalStyle />
      <Card style={{ width: 360 }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 30 }}>管理后台登录</Title>
        <Form
          form={form}
          onFinish={onFinish}
          layout="vertical"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              prefix={<UserOutlined className="site-form-item-icon" />}
              placeholder="用户名"
            />
          </Form.Item>
          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input
              prefix={<LockOutlined className="site-form-item-icon" />}
              type="password"
              placeholder="密码"
            />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              style={{ width: '100%' }}
              loading={loading}
            >
              登录
            </Button>
          </Form.Item>
          <Divider />
          <div style={{ textAlign: 'center' }}>
            <a href="#" style={{ color: '#1890ff' }}>忘记密码?</a>
          </div>
        </Form>
      </Card>
    </div>
  )
}

export default LoginPage