import React, { useEffect, useState } from 'react'
import { Card, Form, Input, Select, Switch, Button, message, Spin, Tabs, Upload, Divider } from 'antd'
import { SaveOutlined, AlertOutlined, InfoOutlined, BellOutlined, LockOutlined, CogOutlined, UploadOutlined } from '@ant-design/icons'
import { useAppDispatch, useAppSelector } from '@store/index'
import { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'
import { useGetSettingsQuery, useUpdateSettingsMutation } from '@services/api'
import { Settings, Theme } from '@types/api'
import { selectTheme } from '@store/slices/uiSlice'

const { Option } = Select
const { TabPane } = Tabs
const { Dragger } = Upload

const Settings: React.FC = () => {
  const dispatch = useAppDispatch()
  const theme = useAppSelector(selectTheme)
  const [form] = Form.useForm()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTab, setCurrentTab] = useState('basic')
  const [fileList, setFileList] = useState<any[]>([])

  // API hooks
  const { data, isLoading, error } = useGetSettingsQuery()
  const [updateSettings] = useUpdateSettingsMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '系统设置' }
    ]))
    dispatch(setPageTitle('系统设置'))
  }, [dispatch])

  // 初始化表单数据
  useEffect(() => {
    if (data?.data) {
      form.setFieldsValue({
        siteName: data.data.siteName,
        siteDescription: data.data.siteDescription,
        contactEmail: data.data.contactEmail,
        theme: data.data.theme,
        enableNotification: data.data.enableNotification,
        notificationInterval: data.data.notificationInterval,
        maxFileSize: data.data.maxFileSize,
        defaultItemLimit: data.data.defaultItemLimit,
        enableRegistration: data.data.enableRegistration,
        requireVerification: data.data.requireVerification
      })
    }
  }, [data, form])

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      setIsSubmitting(true)

      // 模拟API调用
      await updateSettings(values).unwrap()

      message.success('设置保存成功')
    } catch (err) {
      message.error(`保存失败: ${err instanceof Error ? err.message : '未知错误'}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  // 处理文件上传
  const handleFileChange = (info: any) => {
    const { fileList } = info
    setFileList(fileList)
  }

  // 处理标签切换
  const handleTabChange = (key: string) => {
    setCurrentTab(key)
  }

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
          <AlertOutlined style={{ marginRight: '8px' }} />
          <span>获取数据失败: {error instanceof Error ? error.message : error.error || '未知错误'}</span>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px' }}>
      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: '24px'
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h2 style={{ margin: 0, color: theme === 'dark' ? '#fff' : '#333', display: 'flex', alignItems: 'center' }}>
            <CogOutlined style={{ marginRight: '8px' }} />
            系统设置
          </h2>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSubmit}
            loading={isSubmitting}
          >
            保存设置
          </Button>
        </div>

        <Tabs activeKey={currentTab} onChange={handleTabChange} style={{ marginBottom: '24px' }}>
          <TabPane tab={<span style={{ display: 'flex', alignItems: 'center' }}><InfoOutlined style={{ marginRight: '8px' }} /> 基本设置</span>} key="basic" />
          <TabPane tab={<span style={{ display: 'flex', alignItems: 'center' }}><BellOutlined style={{ marginRight: '8px' }} /> 通知设置</span>} key="notification" />
          <TabPane tab={<span style={{ display: 'flex', alignItems: 'center' }}><LockOutlined style={{ marginRight: '8px' }} /> 安全设置</span>} key="security" />
          <TabPane tab={<span style={{ display: 'flex', alignItems: 'center' }}><UploadOutlined style={{ marginRight: '8px' }} /> 文件设置</span>} key="file" />
        </Tabs>

        <Form
          form={form}
          layout="vertical"
          style={{ color: theme === 'dark' ? '#fff' : '#333' }}
        >
          {currentTab === 'basic' && (
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
              <Form.Item
                name="siteName"
                label="网站名称"
                rules={[{ required: true, message: '请输入网站名称' }]}
              >
                <Input placeholder="请输入网站名称" />
              </Form.Item>

              <Form.Item
                name="siteDescription"
                label="网站描述"
                rules={[{ required: true, message: '请输入网站描述' }]}
              >
                <Input.TextArea placeholder="请输入网站描述" rows={4} />
              </Form.Item>

              <Form.Item
                name="contactEmail"
                label="联系邮箱"
                rules={[
                  { required: true, message: '请输入联系邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入联系邮箱" />
              </Form.Item>

              <Form.Item
                name="theme"
                label="主题风格"
                rules={[{ required: true, message: '请选择主题风格' }]}
              >
                <Select placeholder="请选择主题风格">
                  <Option value={Theme.LIGHT}>明亮模式</Option>
                  <Option value={Theme.DARK}>暗黑模式</Option>
                  <Option value={Theme.SYSTEM}>跟随系统</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="logo"
                label="网站Logo"
              >
                <Dragger
                  name="logo"
                  fileList={fileList}
                  onChange={handleFileChange}
                  maxCount={1}
                  accept="image/*"
                >
                  <p className="ant-upload-drag-icon">
                    <UploadOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
                  <p className="ant-upload-hint">支持 JPG, PNG, SVG 格式文件</p>
                </Dragger>
                {fileList.length > 0 && (
                  <div style={{ marginTop: '16px', display: 'flex', alignItems: 'center' }}>
                    <img src={fileList[0].thumbUrl} alt="Logo 预览" style={{ width: '80px', height: '80px', objectFit: 'contain' }} />
                  </div>
                )}
              </Form.Item>

              <Form.Item
                name="favicon"
                label="网站图标"
              >
                <Dragger
                  name="favicon"
                  fileList={fileList}
                  onChange={handleFileChange}
                  maxCount={1}
                  accept="image/*"
                >
                  <p className="ant-upload-drag-icon">
                    <UploadOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
                  <p className="ant-upload-hint">支持 JPG, PNG, ICO 格式文件，建议尺寸 32x32</p>
                </Dragger>
                {fileList.length > 0 && (
                  <div style={{ marginTop: '16px', display: 'flex', alignItems: 'center' }}>
                    <img src={fileList[0].thumbUrl} alt="Favicon 预览" style={{ width: '32px', height: '32px', objectFit: 'contain' }} />
                  </div>
                )}
              </Form.Item>
            </div>
          )}

          {currentTab === 'notification' && (
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
              <Form.Item
                name="enableNotification"
                label="启用通知系统"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="notificationInterval"
                label="通知检查间隔(分钟)"
                rules={[
                  { required: true, message: '请输入通知检查间隔' },
                  { type: 'number', min: 1, message: '间隔不能小于1分钟' }
                ]}
              >
                <Input type="number" placeholder="请输入通知检查间隔" />
              </Form.Item>

              <Form.Item
                name="emailNotification"
                label="启用邮件通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="smsNotification"
                label="启用短信通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="pushNotification"
                label="启用推送通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </div>
          )}

          {currentTab === 'security' && (
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
              <Form.Item
                name="enableRegistration"
                label="启用用户注册"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="requireVerification"
                label="需要邮箱验证"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="maxLoginAttempts"
                label="最大登录尝试次数"
                rules={[
                  { required: true, message: '请输入最大登录尝试次数' },
                  { type: 'number', min: 1, max: 10, message: '次数必须在1-10之间' }
                ]}
              >
                <Input type="number" placeholder="请输入最大登录尝试次数" />
              </Form.Item>

              <Form.Item
                name="lockoutDuration"
                label="账户锁定时长(分钟)"
                rules={[
                  { required: true, message: '请输入账户锁定时长' },
                  { type: 'number', min: 1, message: '时长不能小于1分钟' }
                ]}
              >
                <Input type="number" placeholder="请输入账户锁定时长" />
              </Form.Item>

              <Form.Item
                name="sessionTimeout"
                label="会话超时时间(分钟)"
                rules={[
                  { required: true, message: '请输入会话超时时间' },
                  { type: 'number', min: 5, message: '时长不能小于5分钟' }
                ]}
              >
                <Input type="number" placeholder="请输入会话超时时间" />
              </Form.Item>

              <Form.Item
                name="passwordExpiration"
                label="密码过期时间(天)"
                rules={[
                  { required: true, message: '请输入密码过期时间' },
                  { type: 'number', min: 7, message: '时长不能小于7天' }
                ]}
              >
                <Input type="number" placeholder="请输入密码过期时间" />
              </Form.Item>
            </div>
          )}

          {currentTab === 'file' && (
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
              <Form.Item
                name="maxFileSize"
                label="最大文件大小(MB)"
                rules={[
                  { required: true, message: '请输入最大文件大小' },
                  { type: 'number', min: 1, message: '大小不能小于1MB' }
                ]}
              >
                <Input type="number" placeholder="请输入最大文件大小" />
              </Form.Item>

              <Form.Item
                name="allowedFileTypes"
                label="允许的文件类型"
                rules={[{ required: true, message: '请输入允许的文件类型' }]}
              >
                <Input placeholder="请输入允许的文件类型，用逗号分隔" />
              </Form.Item>

              <Form.Item
                name="maxImageDimensions"
                label="最大图片尺寸(像素)"
                rules={[{ required: true, message: '请输入最大图片尺寸' }]}
              >
                <Input placeholder="例如: 1920x1080" />
              </Form.Item>

              <Form.Item
                name="compressImages"
                label="压缩上传图片"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="imageQuality"
                label="图片压缩质量(0-100)"
                rules={[
                  { required: true, message: '请输入图片压缩质量' },
                  { type: 'number', min: 1, max: 100, message: '质量必须在1-100之间' }
                ]}
              >
                <Input type="number" placeholder="请输入图片压缩质量" />
              </Form.Item>

              <Form.Item
                name="storageLocation"
                label="文件存储位置"
                rules={[{ required: true, message: '请选择文件存储位置' }]}
              >
                <Select placeholder="请选择文件存储位置">
                  <Option value="local">本地存储</Option>
                  <Option value="oss">阿里云OSS</Option>
                  <Option value="s3">AWS S3</Option>
                  <Option value="qiniu">七牛云</Option>
                </Select>
              </Form.Item>
            </div>
          )}
        </Form>
      </Card>

      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h2 style={{ margin: 0, color: theme === 'dark' ? '#fff' : '#333', display: 'flex', alignItems: 'center' }}>
            <InfoOutlined style={{ marginRight: '8px' }} />
            系统信息
          </h2>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '16px' }}>
          <div style={{ padding: '16px', backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', borderRadius: '8px' }}>
            <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>系统版本</p>
            <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>v1.0.0</p>
          </div>

          <div style={{ padding: '16px', backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', borderRadius: '8px' }}>
            <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>上次更新时间</p>
            <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{new Date().toLocaleString()}</p>
          </div>

          <div style={{ padding: '16px', backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', borderRadius: '8px' }}>
            <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>服务器时间</p>
            <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{new Date().toLocaleString()}</p>
          </div>

          <div style={{ padding: '16px', backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', borderRadius: '8px' }}>
            <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>当前用户数</p>
            <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>1,234</p>
          </div>

          <div style={{ padding: '16px', backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', borderRadius: '8px' }}>
            <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>物品总数</p>
            <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>5,678</p>
          </div>

          <div style={{ padding: '16px', backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', borderRadius: '8px' }}>
            <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>交换总数</p>
            <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>3,456</p>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default Settings