import React, { useEffect, useState } from 'react'
import { Card, Table, Button, Input, Select, message, Popconfirm, Spin, Timeline, Modal } from 'antd'
import { SearchOutlined, FilterOutlined, PlusOutlined, EditOutlined, CheckOutlined, CloseOutlined, SwapOutlined, UserOutlined, ShoppingOutlined, AlertCircleOutlined } from '@ant-design/icons'
import { useAppDispatch, useAppSelector } from '@store/index'
import { setBreadcrumb, setPageTitle, setTableSettings, selectTableSettings } from '@store/slices/uiSlice'
import { useGetExchangesQuery, useUpdateExchangeStatusMutation, useGetExchangeByIdQuery } from '@services/api'
import { Exchange, ExchangeStatus, Item, User } from '@types/api'
import { selectTheme } from '@store/slices/uiSlice'

const { Search } = Input
const { Option } = Select

const Exchanges: React.FC = () => {
  const dispatch = useAppDispatch()
  const theme = useAppSelector(selectTheme)
  const tableKey = 'exchanges'
  const tableSettings = useAppSelector((state) => selectTableSettings(state, tableKey))
  const [searchKeyword, setSearchKeyword] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(tableSettings.pageSize || 1)
  const [pageSize, setPageSize] = useState(tableSettings.pageSize || 10)
  const [exchangeId, setExchangeId] = useState<string | null>(null)

  // API hooks
  const { data, isLoading, error, refetch } = useGetExchangesQuery({
    page: currentPage,
    pageSize,
    status: statusFilter,
    search: searchKeyword
  })
  const [updateExchangeStatus] = useUpdateExchangeStatusMutation()
  const { data: exchangeDetail, isLoading: isDetailLoading, error: detailError } = useGetExchangeByIdQuery(exchangeId || '', { skip: !exchangeId })

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '交换管理' }
    ]))
    dispatch(setPageTitle('交换管理'))
  }, [dispatch])

  // 保存表格设置
  useEffect(() => {
    dispatch(setTableSettings({
      tableKey,
      settings: {
        pageSize
      }
    }))
  }, [pageSize, dispatch, tableKey])

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value)
    setCurrentPage(1)
  }

  // 处理状态筛选
  const handleStatusChange = (value: string) => {
    setStatusFilter(value)
    setCurrentPage(1)
  }

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrentPage(pagination.current)
    setPageSize(pagination.pageSize)
  }

  // 获取交换状态文本
  const getStatusText = (status: ExchangeStatus) => {
    switch (status) {
      case ExchangeStatus.PENDING: return '待确认'
      case ExchangeStatus.ACCEPTED: return '已接受'
      case ExchangeStatus.REJECTED: return '已拒绝'
      case ExchangeStatus.COMPLETED: return '已完成'
      case ExchangeStatus.DISPUTED: return '有争议'
      case ExchangeStatus.CANCELLED: return '已取消'
      default: return status
    }
  }

  // 获取交换状态样式
  const getStatusStyle = (status: ExchangeStatus) => {
    switch (status) {
      case ExchangeStatus.PENDING: return { bg: '#722ed120', color: '#722ed1' }
      case ExchangeStatus.ACCEPTED: return { bg: '#1890ff20', color: '#1890ff' }
      case ExchangeStatus.REJECTED: return { bg: '#ff4d4f20', color: '#ff4d4f' }
      case ExchangeStatus.COMPLETED: return { bg: '#52c41a20', color: '#52c41a' }
      case ExchangeStatus.DISPUTED: return { bg: '#faad1420', color: '#faad14' }
      case ExchangeStatus.CANCELLED: return { bg: '#ff4d4f20', color: '#ff4d4f' }
      default: return { bg: '#f0f0f0', color: '#666' }
    }
  }

  // 处理交换状态更新
  const handleStatusUpdate = async (exchangeId: string, status: ExchangeStatus, reason?: string) => {
    // 对于拒绝和争议状态，要求提供原因
    if ((status === ExchangeStatus.REJECTED || status === ExchangeStatus.DISPUTED) && !reason) {
      const inputReason = prompt('请输入' + (status === ExchangeStatus.REJECTED ? '拒绝' : '争议') + '原因:');
      if (!inputReason) {
        message.warning('请提供' + (status === ExchangeStatus.REJECTED ? '拒绝' : '争议') + '原因');
        return;
      }
      reason = inputReason;
    }

    try {
      await updateExchangeStatus({ id: exchangeId, status, reason }).unwrap()
      message.success('交换状态更新成功')
      refetch()
      setSelectedExchange(null)
    } catch (err: any) {
      message.error(`更新失败: ${err?.message || err?.error || '未知错误'}`)
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '交换ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '交换双方',
      key: 'users',
      render: (record: Exchange) => (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: '#f0f0f0',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: '12px',
                overflow: 'hidden'
              }}
            >
              <UserOutlined size={16} />
            </div>
            <div>
              <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{record.requesterName}</p>
              <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>请求方</p>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginLeft: '44px' }}>
            <SwapOutlined size={16} style={{ color: '#1890ff', marginRight: '8px' }} />
            <span style={{ fontSize: '12px', color: '#1890ff' }}>交换</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: '#f0f0f0',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: '12px',
                overflow: 'hidden'
              }}
            >
              <UserOutlined size={16} />
            </div>
            <div>
              <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{record.targetItem?.publisherName}</p>
              <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>物品拥有方</p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '交换物品',
      key: 'items',
      render: (record: Exchange) => (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '4px',
                backgroundColor: '#f0f0f0',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: '12px',
                overflow: 'hidden',
                flexShrink: 0
              }}
            >
              {record.offeredItem?.images && record.offeredItem.images.length > 0 ? (
                <img src={record.offeredItem.images[0]} alt={record.offeredItem.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
              ) : (
                <ShoppingOutlined size={16} />
              )}
            </div>
            <div style={{ flex: 1 }}>
              <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{record.offeredItem?.title}</p>
              <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>估值: ¥{record.offeredItem?.estimatedValue}</p>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginLeft: '52px' }}>
            <SwapOutlined size={16} style={{ color: '#1890ff', marginRight: '8px' }} />
            <span style={{ fontSize: '12px', color: '#1890ff' }}>交换</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '4px',
                backgroundColor: '#f0f0f0',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: '12px',
                overflow: 'hidden',
                flexShrink: 0
              }}
            >
              {record.targetItem?.images && record.targetItem.images.length > 0 ? (
                <img src={record.targetItem.images[0]} alt={record.targetItem.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
              ) : (
                <ShoppingOutlined size={16} />
              )}
            </div>
            <div style={{ flex: 1 }}>
              <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{record.targetItem?.title}</p>
              <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>估值: ¥{record.targetItem?.estimatedValue}</p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Exchange) => {
        const style = getStatusStyle(record.status)
        return (
          <span
            style={{
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              backgroundColor: style.bg,
              color: style.color
            }}
          >
            {getStatusText(record.status)}
          </span>
        )
      },
      width: 100
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => (
        <span>{new Date(text).toLocaleString()}</span>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: Exchange) => (
        <div style={{ display: 'flex', justifyContent: 'center', gap: '8px' }}>
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined size={16} />}
              onClick={() => setSelectedExchange(record)}
            >
              详情
            </Button>
            {record.status === ExchangeStatus.PENDING && (
              <Popconfirm
                title="确定要接受该交换请求吗？"
                onConfirm={() => handleStatusUpdate(record.id, ExchangeStatus.ACCEPTED)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="success"
                  size="small"
                  icon={<CheckOutlined size={16} />}
                >
                  接受
                </Button>
              </Popconfirm>
            )}
            {record.status === ExchangeStatus.PENDING && (
              <Popconfirm
                title="确定要拒绝该交换请求吗？"
                onConfirm={() => handleStatusUpdate(record.id, ExchangeStatus.REJECTED)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="danger"
                  size="small"
                  icon={<CloseOutlined size={16} />}
                >
                  拒绝
                </Button>
              </Popconfirm>
            )}
            {(record.status === ExchangeStatus.ACCEPTED || record.status === ExchangeStatus.DISPUTED) && (
              <Popconfirm
                title="确定要标记为已完成吗？"
                onConfirm={() => handleStatusUpdate(record.id, ExchangeStatus.COMPLETED)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckOutlined size={16} />}
                >
                  完成
                </Button>
              </Popconfirm>
            )}
          </div>
      )
    }
  ]

  // 生成交换时间线数据
  const generateTimelineData = (exchange: Exchange) => {
    const timeline = [
      {
        time: exchange.createdAt,
        title: '交换请求创建',
        description: `${exchange.requesterName} 请求交换 ${exchange.targetItem?.title}`
      }
    ]

    if (exchange.status === ExchangeStatus.ACCEPTED || exchange.status === ExchangeStatus.COMPLETED || exchange.status === ExchangeStatus.DISPUTED) {
      timeline.push({
        time: exchange.confirmedAt || exchange.updatedAt,
        title: '交换请求接受',
        description: `${exchange.targetItem?.publisherName} 接受了交换请求`
      })
    }

    if (exchange.status === ExchangeStatus.COMPLETED) {
      timeline.push({
        time: exchange.completedAt || exchange.updatedAt,
        title: '交换完成',
        description: '双方已完成物品交换'
      })
    }

    if (exchange.status === ExchangeStatus.REJECTED) {
      timeline.push({
        time: exchange.updatedAt,
        title: '交换请求拒绝',
        description: `${exchange.targetItem?.publisherName} 拒绝了交换请求${exchange.reason ? `: ${exchange.reason}` : ''}`
      })
    }

    if (exchange.status === ExchangeStatus.CANCELLED) {
      timeline.push({
        time: exchange.updatedAt,
        title: '交换已取消',
        description: '交换请求已取消'
      })
    }

    if (exchange.status === ExchangeStatus.DISPUTED) {
      timeline.push({
        time: exchange.updatedAt,
        title: '交换有争议',
        description: `交换过程中出现争议${exchange.reason ? `: ${exchange.reason}` : ''}`
      })
    }

    return timeline
  }

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
          <AlertCircleOutlined style={{ marginRight: '8px' }} />
          <span>获取数据失败: {error.error || '未知错误'}</span>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px' }}>
      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: '24px'
        }}
      >
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', alignItems: 'center' }}>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <Search
              placeholder="搜索交换..."
              prefix={<SearchOutlined />}
              allowClear
              enterButton="搜索"
              size="middle"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <FilterOutlined style={{ marginRight: '8px', color: '#666' }} />
              <Select
                placeholder="筛选状态"
                size="middle"
                value={statusFilter}
                onChange={handleStatusChange}
                allowClear
              >
                <Option value={ExchangeStatus.PENDING}>待确认</Option>
                <Option value={ExchangeStatus.ACCEPTED}>已接受</Option>
                <Option value={ExchangeStatus.REJECTED}>已拒绝</Option>
                <Option value={ExchangeStatus.COMPLETED}>已完成</Option>
                <Option value={ExchangeStatus.DISPUTED}>有争议</Option>
                <Option value={ExchangeStatus.CANCELLED}>已取消</Option>
              </Select>
            </div>
          </div>
        </div>
      </Card>

      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)' 
        }}
      >
        <Table
          columns={columns}
          dataSource={data?.data?.items || []}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize,
            total: data?.data?.total || 0,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true
          }}
          onChange={handleTableChange}
          loading={isLoading}
          style={{ color: theme === 'dark' ? '#fff' : '#333' }}
        />
      </Card>

      {/* 交换详情弹窗 */}
      <Modal
        title={`交换详情 #${exchangeId}`}
        visible={!!exchangeId}
        onCancel={() => setExchangeId(null)}
        width={800}
        destroyOnClose
        footer={null}
      >
        {isDetailLoading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
            <Spin size="large" />
          </div>
        ) : detailError ? (
          <div style={{ padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}>
            <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
              <AlertCircleOutlined style={{ marginRight: '8px' }} />
              <span>获取交换详情失败: {detailError.error || '未知错误'}</span>
            </div>
          </div>
        ) : exchangeDetail?.data ? (
        <div
            style={{
              backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
              borderRadius: '8px',
              width: '100%',
              overflow: 'auto',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)',
              }}>
              <div style={{ marginBottom: '16px' }}>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>交换详情 #{exchangeDetail.data.id}</h3>
              </div>

            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ marginBottom: '16px', color: theme === 'dark' ? '#fff' : '#333' }}>交换信息</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>交换ID</p>
                  <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{exchangeDetail.data.id}</p>
                </div>
                <div>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>状态</p>
                  <span
                    style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      ...getStatusStyle(exchangeDetail.data.status || ExchangeStatus.PENDING)
                    }}
                  >
                    {getStatusText(exchangeDetail.data.status || ExchangeStatus.PENDING)}
                  </span>
                </div>
                <div>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>请求方</p>
                  <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{exchangeDetail.data.requesterName}</p>
                </div>
                <div>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>拥有方</p>
                  <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{exchangeDetail.data.targetItem?.publisherName}</p>
                </div>
                {(exchangeDetail.data.status === ExchangeStatus.REJECTED || exchangeDetail.data.status === ExchangeStatus.DISPUTED) && exchangeDetail.data.reason && (
                  <div style={{ gridColumn: '1 / -1' }}>
                    <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>
                      {exchangeDetail.data.status === ExchangeStatus.REJECTED ? '拒绝原因' : '争议原因'}
                    </p>
                    <p style={{ margin: 0, color: theme === 'dark' ? '#fff' : '#333' }}>{exchangeDetail.data.reason}</p>
                  </div>
                )}
              </div>
            </div>

            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ marginBottom: '16px', color: theme === 'dark' ? '#fff' : '#333' }}>交换物品</h3>
              <div style={{ display: 'flex', gap: '24px', flexWrap: 'wrap' }}>
                <div style={{ flex: 1, minWidth: '200px' }}>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>请求方提供</p>
                  <div style={{ display: 'flex', gap: '12px' }}>
                    <div
                      style={{
                        width: '80px',
                        height: '80px',
                        borderRadius: '4px',
                        backgroundColor: '#f0f0f0',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        overflow: 'hidden'
                      }}
                    >
                      {exchangeDetail.data.offeredItem?.images && exchangeDetail.data.offeredItem.images.length > 0 ? (
                        <img src={exchangeDetail.data.offeredItem.images[0]} alt={exchangeDetail.data.offeredItem.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                      ) : (
                        <ShoppingOutlined size={24} />
                      )}
                    </div>
                    <div style={{ flex: 1 }}>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{exchangeDetail.data.offeredItem?.title}</p>
                      <p style={{ margin: '4px 0', fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>{exchangeDetail.data.offeredItem?.description.substring(0, 50)}...</p>
                      <p style={{ margin: 0, fontSize: '14px', color: '#1890ff' }}>估值: ¥{exchangeDetail.data.offeredItem?.estimatedValue}</p>
                    </div>
                  </div>
                </div>
                <div style={{ flex: 1, minWidth: '200px' }}>
                  <p style={{ margin: '8px 0', fontSize: '14px', color: theme === 'dark' ? '#999' : '#666' }}>请求方获取</p>
                  <div style={{ display: 'flex', gap: '12px' }}>
                    <div
                      style={{
                        width: '80px',
                        height: '80px',
                        borderRadius: '4px',
                        backgroundColor: '#f0f0f0',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        overflow: 'hidden'
                      }}
                    >
                      {exchangeDetail.data.targetItem?.images && exchangeDetail.data.targetItem.images.length > 0 ? (
                        <img src={exchangeDetail.data.targetItem.images[0]} alt={exchangeDetail.data.targetItem.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                      ) : (
                        <ShoppingOutlined size={24} />
                      )}
                    </div>
                    <div style={{ flex: 1 }}>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{exchangeDetail.data.targetItem?.title}</p>
                      <p style={{ margin: '4px 0', fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>{exchangeDetail.data.targetItem?.description.substring(0, 50)}...</p>
                      <p style={{ margin: 0, fontSize: '14px', color: '#1890ff' }}>估值: ¥{exchangeDetail.data.targetItem?.estimatedValue}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 style={{ marginBottom: '16px', color: theme === 'dark' ? '#fff' : '#333' }}>交换时间线</h3>
              <Timeline
                mode="left"
                style={{
                  color: theme === 'dark' ? '#fff' : '#333',
                  '& .ant-timeline-item-head': {
                    backgroundColor: theme === 'dark' ? '#333' : '#f0f0f0'
                  },
                  '& .ant-timeline-item-content': {
                    color: theme === 'dark' ? '#fff' : '#333'
                  }
                }}
              >
                {generateTimelineData(exchangeDetail.data).map((item, index) => (
                  <Timeline.Item key={index}>
                    <Timeline.ItemDot />
                    <Timeline.ItemContent>
                      <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{item.title}</p>
                      <p style={{ margin: '4px 0', fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>{item.description}</p>
                      <p style={{ margin: 0, fontSize: '12px', color: '#1890ff' }}>{new Date(item.time).toLocaleString()}</p>
                    </Timeline.ItemContent>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>

            <div style={{ marginTop: '24px', display: 'flex', justifyContent: 'flex-end', gap: '16px' }}>
              {exchangeDetail.data.status === ExchangeStatus.PENDING && (
                <Button
                  type="success"
                  icon={<CheckOutlined size={16} />}
                  onClick={() => handleStatusUpdate(exchangeDetail.data.id, ExchangeStatus.ACCEPTED)}
                >
                  接受请求
                </Button>
              )}
              {exchangeDetail.data.status === ExchangeStatus.PENDING && (
                <Button
                  type="danger"
                  icon={<CloseOutlined size={16} />}
                  onClick={() => handleStatusUpdate(exchangeDetail.data.id, ExchangeStatus.REJECTED)}
                >
                  拒绝请求
                </Button>
              )}
              {(exchangeDetail.data.status === ExchangeStatus.ACCEPTED || exchangeDetail.data.status === ExchangeStatus.DISPUTED) && (
                <Button
                  type="primary"
                  icon={<CheckOutlined size={16} />}
                  onClick={() => handleStatusUpdate(exchangeDetail.data.id, ExchangeStatus.COMPLETED)}
                >
                  标记为完成
                </Button>
              )}
              <Button
                type="default"
                onClick={() => setExchangeId(null)}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}
      </Modal>
    </div>
  )
}

export default Exchanges