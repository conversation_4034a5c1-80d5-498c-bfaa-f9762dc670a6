import React, { useEffect, useState, useRef } from 'react'
import { Card, List, Avatar, Input, Button, message, Spin, Empty, Badge, Modal, Form, Select } from 'antd'
import { MessageOutlined, SearchOutlined, SendOutlined, UserOutlined, InfoOutlined, AlertOutlined, CheckOutlined, ClockOutlined } from '@ant-design/icons'
import { useAppDispatch, useAppSelector } from '@store/index'
import { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'
import { useGetChatsQuery, useGetChatMessagesQuery, useSendMessageMutation, useMarkChatAsReadMutation } from '@services/api'
import { Chat, Message, User, MessageStatus } from '@types/api'
import { selectTheme } from '@store/slices/uiSlice'

const { Option } = Select
const { TextArea } = Input

const Chat: React.FC = () => {
  const dispatch = useAppDispatch()
  const theme = useAppSelector(selectTheme)
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null)
  const [messageContent, setMessageContent] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoadingChats, setIsLoadingChats] = useState(true)
  const [isLoadingMessages, setIsLoadingMessages] = useState(false)
  const [showUserSearchModal, setShowUserSearchModal] = useState(false)
  const [newChatForm] = Form.useForm()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // API hooks
  const { data: chatsData, error: chatsError } = useGetChatsQuery()
  const { data: messagesData, error: messagesError, refetch: refetchMessages } = useGetChatMessagesQuery(selectedChat?.id || '')
  const [sendMessage, { isLoading: isSendingMessage, isSuccess }] = useSendMessageMutation()
  const [markChatAsRead] = useMarkChatAsReadMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '聊天管理' }
    ]))
    dispatch(setPageTitle('聊天管理'))
  }, [dispatch])

  // 监听聊天数据加载状态
  useEffect(() => {
    if (chatsData || chatsError) {
      setIsLoadingChats(false)
    }
  }, [chatsData, chatsError])

  // 监听消息数据加载状态
  useEffect(() => {
    if (selectedChat) {
      setIsLoadingMessages(true)
      // 标记聊天为已读
      markChatAsRead(selectedChat.id)
    }
  }, [selectedChat, markChatAsRead])

  // 监听消息数据加载完成
  useEffect(() => {
    if (messagesData || messagesError) {
      setIsLoadingMessages(false)
      // 滚动到底部
      scrollToBottom()
    }
  }, [messagesData, messagesError])

  // 滚动到消息底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // 选择聊天
  const handleChatSelect = (chat: Chat) => {
    setSelectedChat(chat)
  }

  // 发送消息
  const handleSendMessage = async () => {
    if (!messageContent.trim() || !selectedChat) return

    try {
      await sendMessage({
        chatId: selectedChat.id,
        content: messageContent
      }).unwrap()

      setMessageContent('')
      // 重新获取消息
      await refetchMessages()
    } catch (err) {
      message.error(`发送消息失败: ${err instanceof Error ? err.message : '未知错误'}`)
    }
  }

  // 处理消息输入变化
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessageContent(e.target.value)
  }

  // 处理搜索变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  // 打开用户搜索模态框
  const handleOpenUserSearchModal = () => {
    setShowUserSearchModal(true)
  }

  // 关闭用户搜索模态框
  const handleCloseUserSearchModal = () => {
    setShowUserSearchModal(false)
    newChatForm.resetFields()
  }

  // 创建新聊天
  const handleCreateNewChat = async () => {
    try {
      const values = await newChatForm.validateFields()
      // 这里应该调用创建聊天的API
      message.success('聊天创建成功')
      handleCloseUserSearchModal()
      // 刷新聊天列表
      // refetchChats()
    } catch (err) {
      message.error(`创建聊天失败: ${err instanceof Error ? err.message : '未知错误'}`)
    }
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  // 渲染聊天列表
  const renderChatList = () => {
    if (isLoadingChats) {
      return (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <Spin size="small" />
        </div>
      )
    }

    if (chatsError) {
      return (
        <div style={{ padding: '16px', textAlign: 'center', color: '#ff4d4f' }}>
          <AlertOutlined style={{ marginRight: '8px' }} />
          获取聊天列表失败
        </div>
      )
    }

    if (!chatsData?.data || chatsData.data.length === 0) {
      return (
        <Empty description="暂无聊天记录" />
      )
    }

    return (
      <List
        dataSource={chatsData.data}
        itemLayout="horizontal"
        style={{ overflow: 'auto', maxHeight: 'calc(100vh - 180px)' }}
        renderItem={(chat) => (
          <List.Item
            onClick={() => handleChatSelect(chat)}
            style={{
              cursor: 'pointer',
              backgroundColor: selectedChat?.id === chat.id ? (theme === 'dark' ? '#2d2d2d' : '#f0f0f0') : 'transparent',
              borderLeft: selectedChat?.id === chat.id ? '3px solid #1890ff' : '3px solid transparent',
              transition: 'all 0.2s'
            }}
          >
            <List.Item.Meta
              avatar={<Avatar src={chat.otherUser.avatar} alt={chat.otherUser.name} />}
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{chat.otherUser.name}</span>
                  <span style={{ fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>{formatTime(chat.lastMessage.createdAt)}</span>
                </div>
              }
              description={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ fontSize: '14px', color: theme === 'dark' ? '#ccc' : '#666', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', maxWidth: '200px' }}>
                    {chat.lastMessage.senderId === chat.currentUserId ? '我: ' : ''}{chat.lastMessage.content}
                  </span>
                  {chat.unreadCount > 0 && (
                    <Badge count={chat.unreadCount} size="small" style={{ backgroundColor: '#ff4d4f' }} />
                  )}
                </div>
              }
            />
          </List.Item>
        )}
      </List>
    )
  }

  // 渲染消息列表
  const renderMessageList = () => {
    if (!selectedChat) {
      return (
        <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: 'calc(100vh - 180px)' }}>
          <MessageOutlined style={{ fontSize: '48px', color: theme === 'dark' ? '#666' : '#ccc', marginBottom: '16px' }} />
          <p style={{ color: theme === 'dark' ? '#999' : '#666' }}>请选择一个聊天</p>
        </div>
      )
    }

    if (isLoadingMessages) {
      return (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 'calc(100vh - 180px)' }}>
          <Spin size="small" />
        </div>
      )
    }

    if (messagesError) {
      return (
        <div style={{ padding: '16px', textAlign: 'center', color: '#ff4d4f' }}>
          <AlertOutlined style={{ marginRight: '8px' }} />
          获取消息失败
        </div>
      )
    }

    if (!messagesData?.data || messagesData.data.length === 0) {
      return (
        <Empty description="暂无消息" />
      )
    }

    return (
      <div style={{ overflow: 'auto', maxHeight: 'calc(100vh - 260px)', padding: '16px' }}>
        {messagesData.data.map((message) => (
          <div key={message.id} style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'flex-start' }}>
              {message.senderId !== selectedChat.currentUserId && (
                <Avatar src={selectedChat.otherUser.avatar} alt={selectedChat.otherUser.name} style={{ marginRight: '8px' }} size="small" />
              )}
              <div
                style={{
                  backgroundColor: message.senderId === selectedChat.currentUserId ? '#1890ff' : (theme === 'dark' ? '#2d2d2d' : '#f0f0f0'),
                  color: message.senderId === selectedChat.currentUserId ? '#fff' : (theme === 'dark' ? '#fff' : '#333'),
                  padding: '8px 12px',
                  borderRadius: '8px',
                  maxWidth: '70%',
                  marginLeft: message.senderId === selectedChat.currentUserId ? 'auto' : '0',
                  marginRight: message.senderId === selectedChat.currentUserId ? '0' : 'auto'
                }}
              >
                <p style={{ margin: '0 0 4px 0' }}>{message.content}</p>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  <span style={{ fontSize: '12px', color: message.senderId === selectedChat.currentUserId ? 'rgba(255, 255, 255, 0.7)' : theme === 'dark' ? '#999' : '#666' }}>{formatTime(message.createdAt)}</span>
                  {message.senderId === selectedChat.currentUserId && (
                    <span style={{ display: 'flex', alignItems: 'center', marginLeft: '4px' }}>
                      {message.status === MessageStatus.SENDING && <ClockOutlined style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.7)' }} />}
                      {message.status === MessageStatus.SENT && <CheckOutlined style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.7)' }} />}
                      {message.status === MessageStatus.READ && <CheckOutlined style={{ fontSize: '12px', color: '#52c41a' }} />}
                      {message.status === MessageStatus.FAILED && <AlertOutlined style={{ fontSize: '12px', color: '#ff4d4f' }} />}
                    </span>)
                  }
                </div>
              </div>
              {message.senderId === selectedChat.currentUserId && (
                <Avatar src={message.senderAvatar} alt={message.senderName} style={{ marginLeft: '8px' }} size="small" />
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px', display: 'flex', flexDirection: 'column', height: 'calc(100vh - 48px)' }}>
      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: '24px',
          flex: '1',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px', padding: '0 16px' }}>
          <h2 style={{ margin: 0, color: theme === 'dark' ? '#fff' : '#333', display: 'flex', alignItems: 'center' }}>
            <MessageOutlined style={{ marginRight: '8px' }} />
            聊天管理
          </h2>
          <Button
            type="primary"
            icon={<UserOutlined />}
            onClick={handleOpenUserSearchModal}
          >
            新聊天
          </Button>
        </div>

        <div style={{ display: 'flex', height: 'calc(100% - 52px)' }}>
          {/* 聊天列表 */}
          <div style={{ width: '350px', borderRight: `1px solid ${theme === 'dark' ? '#333' : '#e8e8e8'}`, display: 'flex', flexDirection: 'column' }}>
            <div style={{ padding: '12px', borderBottom: `1px solid ${theme === 'dark' ? '#333' : '#e8e8e8'}` }}>
              <Input
                placeholder="搜索聊天..."
                prefix={<SearchOutlined />}
                value={searchQuery}
                onChange={handleSearchChange}
                style={{ backgroundColor: theme === 'dark' ? '#2d2d2d' : '#f5f5f5', border: 'none' }}
              />
            </div>
            {renderChatList()}
          </div>

          {/* 消息区域 */}
          <div style={{ flex: '1', display: 'flex', flexDirection: 'column' }}>
            {/* 聊天标题 */}
            {selectedChat && (
              <div style={{ padding: '12px 16px', borderBottom: `1px solid ${theme === 'dark' ? '#333' : '#e8e8e8'}`, display: 'flex', alignItems: 'center' }}>
                <Avatar src={selectedChat.otherUser.avatar} alt={selectedChat.otherUser.name} style={{ marginRight: '12px' }} />
                <div>
                  <p style={{ margin: '0 0 2px 0', fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{selectedChat.otherUser.name}</p>
                  <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>在线</p>
                </div>
              </div>
            )}

            {/* 消息列表 */}
            {renderMessageList()}

            {/* 消息输入框 */}
            {selectedChat && (
              <div style={{ padding: '16px', borderTop: `1px solid ${theme === 'dark' ? '#333' : '#e8e8e8'}` }}>
                <TextArea
                  placeholder="输入消息..."
                  value={messageContent}
                  onChange={handleMessageChange}
                  rows={4}
                  style={{ backgroundColor: theme === 'dark' ? '#2d2d2d' : '#fff', color: theme === 'dark' ? '#fff' : '#333' }}
                />
                <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '8px' }}>
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleSendMessage}
                    loading={isSendingMessage}
                    disabled={!messageContent.trim()}
                  >
                    发送
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* 用户搜索模态框 */}
      <Modal
        title="创建新聊天"
        visible={showUserSearchModal}
        onCancel={handleCloseUserSearchModal}
        onOk={handleCreateNewChat}
        footer={[
          <Button key="cancel" onClick={handleCloseUserSearchModal}>取消</Button>,
          <Button key="ok" type="primary" onClick={handleCreateNewChat}>创建</Button>
        ]}
      >
        <Form form={newChatForm} layout="vertical">
          <Form.Item
            name="userId"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select placeholder="搜索并选择用户">
              {/* 这里应该动态加载用户列表 */}
              <Option value="1">张三</Option>
              <Option value="2">李四</Option>
              <Option value="3">王五</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="initialMessage"
            label="初始消息"
            rules={[{ required: true, message: '请输入初始消息' }]}
          >
            <TextArea placeholder="输入初始消息" rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Chat