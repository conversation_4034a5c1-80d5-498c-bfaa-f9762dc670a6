import React, { useEffect } from 'react'
import { Card, Row, Col, Statistic, Spin, message, Divider } from 'antd'
import { ArrowUpOutlined, ArrowDownOutlined, UserOutlined, ShoppingOutlined, AlertOutlined, Bar<PERSON><PERSON>Outlined, CheckCircleOutlined, StarOutlined, SwapOutlined } from '@ant-design/icons'
import { useGetDashboardStatsQuery, useGetDashboardChartsQuery } from '@services/api'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts'
import { useNavigate } from 'react-router-dom'

// 格式化百分比
const formatPercentage = (value: number) => {
  return `${(value * 100).toFixed(0)}%`;
};

const Dashboard: React.FC<{}> = () => {
  const { data: statsData, error: statsError, isLoading: statsLoading, refetch: refetchStats } = useGetDashboardStatsQuery()
  const { data: weeklyData, error: weeklyError, isLoading: weeklyLoading } = useGetDashboardChartsQuery({
    type: 'weekly',
    period: '30d'
  })
  const { data: categoryData, error: categoryError, isLoading: categoryLoading } = useGetDashboardChartsQuery({
    type: 'category',
    period: 'all'
  })
  const navigate = useNavigate()

  useEffect(() => {
    if (statsError) {
      message.error('获取仪表板数据失败')
    }
    if (weeklyError || categoryError) {
      message.error('获取图表数据失败')
    }
  }, [statsError, weeklyError, categoryError])

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A28DFF']

  return (
    <div style={{ padding: 24 }}>
      <h1 style={{ marginBottom: 24 }}>仪表板</h1>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card
            hoverable
            onClick={() => navigate('/users')}
            style={{ cursor: 'pointer' }}
          >
            <Statistic
              title="总用户数"
              value={statsLoading ? 0 : statsData?.data?.totalUsers || 0}
              prefix={<UserOutlined />}
              suffix="人"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card
            hoverable
            onClick={() => navigate('/items')}
            style={{ cursor: 'pointer' }}
          >
            <Statistic
              title="物品总数"
              value={statsLoading ? 0 : statsData?.data?.totalItems || 0}
              prefix={<ShoppingOutlined />}
              suffix="件"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card
            hoverable
            onClick={() => navigate('/exchanges')}
            style={{ cursor: 'pointer' }}
          >
            <Statistic
              title="交换总数"
              value={statsLoading ? 0 : statsData?.data?.totalExchanges || 0}
              prefix={<SwapOutlined />}
              suffix="次"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card
            hoverable
            onClick={() => navigate('/reports')}
            style={{ cursor: 'pointer' }}
          >
            <Statistic
              title="待处理举报"
              value={statsLoading ? 0 : statsData?.data?.pendingExchanges || 0}
              prefix={<AlertOutlined />}
              suffix="个"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="完成交换率"
              value={statsLoading ? 0 : formatPercentage(statsData?.data?.cancellationRate || 0)}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#2fc25b' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="平均评分"
              value={statsLoading ? 0 : statsData?.data?.averageRating || 0}
              prefix={<StarOutlined />}
              suffix="/5"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Divider orientation="left">数据图表</Divider>
      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card title="每周交换趋势" style={{ height: '100%' }}>
            {weeklyLoading ? (
              <Spin style={{ display: 'flex', justifyContent: 'center', padding: 40 }} />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={weeklyData?.data || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="label" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="value" stroke="#1890ff" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            )}
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="物品分类占比" style={{ height: '100%' }}>
            {categoryLoading ? (
              <Spin style={{ display: 'flex', justifyContent: 'center', padding: 40 }} />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={categoryData?.data || []}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {categoryData?.data?.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard