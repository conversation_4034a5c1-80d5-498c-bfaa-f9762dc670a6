import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Select, DatePicker, Spin, message } from 'antd'
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts'
import { useGetStatisticsQuery } from '@services/api'

// 定义API响应类型
interface ApiResponse<T> {
  success: boolean
  data: T
  error?: string | null
}
import { Dayjs } from 'dayjs'

type TimeRangeType = [Dayjs | null, Dayjs | null] | null

type DataType = 'daily' | 'weekly' | 'monthly'

type ChartDataType = {
  date: string
  activeUsers: number
  newUsers: number
}

type DistributionType = {
  name: string
  value: number
}

// 统计数据响应类型
interface StatisticsData {
  chartData: ChartDataType[]
  userDistribution: DistributionType[]
  exchangeDistribution: DistributionType[]
  totalUsers: number
  totalItems: number
  totalExchanges: number
  totalReports: number
}

const { Option } = Select
const { RangePicker } = DatePicker

// 颜色配置
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']

const Statistics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<TimeRangeType>([null, null])
  const [dataType, setDataType] = useState<DataType>('daily')
  const [loading, setLoading] = useState(false)
  const [chartData, setChartData] = useState<ChartDataType[]>([])
  const [userDistribution, setUserDistribution] = useState<DistributionType[]>([])
  const [exchangeDistribution, setExchangeDistribution] = useState<DistributionType[]>([])

  // 格式化日期为YYYY-MM-DD
  const formatDate = (date: Dayjs): string => {
    return date.format('YYYY-MM-DD')
  }

  // 获取数据
  const { data, refetch } = useGetStatisticsQuery<ApiResponse<StatisticsData>>({
    startDate: timeRange && timeRange[0] ? formatDate(timeRange[0]) : '',
    endDate: timeRange && timeRange[1] ? formatDate(timeRange[1]) : '',
    type: dataType,
    period: dataType
  })
  // 明确指定data的类型为ApiResponse<StatisticsData> | undefined
  const apiResponse = data as unknown as ApiResponse<StatisticsData> | undefined
  const statisticsData = apiResponse?.data
  const isLoading = apiResponse === undefined
  const error = apiResponse?.error

  // 监听数据变化
  useEffect(() => {
    if (statisticsData) {
      // 设置图表数据
      setChartData(statisticsData.chartData || [])
      setUserDistribution(statisticsData.userDistribution || [])
      setExchangeDistribution(statisticsData.exchangeDistribution || [])
    }

    if (error) {
      message.error(error || '获取统计数据失败')
    }
  }, [apiResponse, error])

  // 处理时间范围选择
  const handleTimeRangeChange = (dates: TimeRangeType) => {
    setTimeRange(dates)
    setLoading(true)
    
    refetch()
      .then(() => {
        setLoading(false)
      })
      .catch(() => {
        setLoading(false)
        message.error('获取统计数据失败')
      })
  }

  // 处理数据类型选择
  const handleDataTypeChange = (value: DataType) => {
    setDataType(value)
    setLoading(true)
    // 延迟执行以显示加载状态
    setTimeout(() => {
      refetch()
      setLoading(false)
    }, 500)
  }

  return (
    <div style={{ padding: 24 }}>
      <Card title="数据统计">
        <div style={{ marginBottom: 16, display: 'flex', flexWrap: 'wrap', gap: 10, alignItems: 'center' }}>
          <RangePicker
            value={timeRange as [Dayjs | null, Dayjs | null]}
            onChange={(dates) => handleTimeRangeChange(dates)}
            placeholder={['开始日期', '结束日期']}
            style={{ width: 300 }}
          />
          <Select
            defaultValue="daily"
            style={{ width: 150 }}
            onChange={handleDataTypeChange}
          >
            <Option value="daily">按日</Option>
            <Option value="weekly">按周</Option>
            <Option value="monthly">按月</Option>
          </Select>
        </div>

        <Spin spinning={loading || isLoading}>
          {/* 统计卡片 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card bordered={false} style={{ backgroundColor: '#f0f2f5' }}>
                <div style={{ textAlign: 'center' }}>
                  <h3>总用户数</h3>
                  <p style={{ fontSize: 24, fontWeight: 'bold' }}>{statisticsData?.totalUsers || 0}</p>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card bordered={false} style={{ backgroundColor: '#f0f2f5' }}>
                <div style={{ textAlign: 'center' }}>
                  <h3>总物品数</h3>
                  <p style={{ fontSize: 24, fontWeight: 'bold' }}>{statisticsData?.totalItems || 0}</p>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card bordered={false} style={{ backgroundColor: '#f0f2f5' }}>
                <div style={{ textAlign: 'center' }}>
                  <h3>总交换数</h3>
                  <p style={{ fontSize: 24, fontWeight: 'bold' }}>{statisticsData?.totalExchanges || 0}</p>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card bordered={false} style={{ backgroundColor: '#f0f2f5' }}>
                <div style={{ textAlign: 'center' }}>
                  <h3>总举报数</h3>
                  <p style={{ fontSize: 24, fontWeight: 'bold' }}>{statisticsData?.totalReports || 0}</p>
                </div>
              </Card>
            </Col>
          </Row>

          {/* 图表区域 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="活跃用户与新增用户趋势">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={chartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="activeUsers" name="活跃用户数" fill="#8884d8" />
                    <Bar dataKey="newUsers" name="新增用户数" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="用户分布">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={userDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {userDistribution.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24}>
              <Card title="交换类型分布">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={exchangeDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(0)}%`}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {exchangeDistribution.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>
        </Spin>
      </Card>
    </div>
  )
}

export default Statistics