import React, { useState } from 'react'
import { Button, Card, Table, Input, Space, Popconfirm, message, Form, Select, Upload, Image } from 'antd'
import { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, FilterOutlined, UploadOutlined } from '@ant-design/icons'
import { useGetItemsQuery, useDeleteItemMutation } from '@services/api'
import { useNavigate } from 'react-router-dom'

const { Search } = Input
const { Option } = Select

const Items: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchText, setSearchText] = useState('')
  const [filterCategory, setFilterCategory] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [deleteItem] = useDeleteItemMutation()
  const navigate = useNavigate()

  const { data, error, isLoading, refetch } = useGetItemsQuery({
    page: currentPage,
    pageSize,
    search: searchText,
    category: filterCategory,
    status: filterStatus
  })

  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  const handleDelete = async (id: string) => {
    try {
      await deleteItem(id).unwrap()
      message.success('删除物品成功')
      refetch()
    } catch (err: any) {
      message.error(err?.data?.error?.message || '删除物品失败')
    }
  }

  const handleCategoryFilter = (category: string) => {
    setFilterCategory(category)
    setCurrentPage(1)
  }

  const handleStatusFilter = (status: string) => {
    setFilterStatus(status)
    setCurrentPage(1)
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '图片',
      dataIndex: 'image',
      key: 'image',
      render: (image: string) => (
        <Image
          src={image}
          alt="物品图片"
          width={60}
          height={60}
          style={{ objectFit: 'cover' }}
        />
      )
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '所有者',
      dataIndex: 'owner',
      key: 'owner',
      render: (owner: any) => owner?.username || '未知'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span style={{ color: status === 'available' ? '#52c41a' : '#faad14' }}>
          {status === 'available' ? '可交换' : '已交换'}
        </span>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => navigate(`/items/${record.id}`)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个物品吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="物品管理"
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/items/create')}
            >
              添加物品
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: 16, display: 'flex', flexWrap: 'wrap', gap: 10 }}>
          <Search
            placeholder="搜索物品名称"
            allowClear
            enterButton={<SearchOutlined />}
            size="middle"
            onSearch={handleSearch}
            style={{ width: 300 }}
          />
          <Form layout="inline">
            <Form.Item name="category">
              <Select
                placeholder="筛选分类"
                allowClear
                style={{ width: 150 }}
                onChange={handleCategoryFilter}
              >
                <Option value="all">全部</Option>
                <Option value="electronics">电子产品</Option>
                <Option value="clothing">服装</Option>
                <Option value="home">家居</Option>
                <Option value="books">图书</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>
            <Form.Item name="status">
              <Select
                placeholder="筛选状态"
                allowClear
                style={{ width: 150 }}
                onChange={handleStatusFilter}
              >
                <Option value="all">全部</Option>
                <Option value="available">可交换</Option>
                <Option value="exchanged">已交换</Option>
              </Select>
            </Form.Item>
          </Form>
        </div>

        <Table
          columns={columns}
          dataSource={data?.data?.items || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize,
            total: data?.data?.total || 0,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
          }}
        />
      </Card>
    </div>
  )
}

export default Items