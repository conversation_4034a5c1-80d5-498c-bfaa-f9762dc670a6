import React, { useState } from 'react'
import { Card, Form, Input, Button, Switch, Upload, message, Divider, InputNumber } from 'antd'
import { UploadOutlined, SaveOutlined, AlertOutlined } from '@ant-design/icons'
import { useUpdateSettingsMutation } from '@services/api'
import type { Settings } from '@types/api'

const { TextArea } = Input

const Settings: React.FC = () => {
  const [form] = Form.useForm<Settings>()
  const [loading, setLoading] = useState(false)
  const [updateSettings] = useUpdateSettingsMutation()
  const [logoUrl, setLogoUrl] = useState('')

  // 初始表单值
  const initialValues: Partial<Settings> = {
    siteName: '闲置物品交换平台',
    siteDescription: '一个方便用户交换闲置物品的平台',
    contactEmail: '<EMAIL>',
    contactPhone: '************',
    maxItemPerUser: 10,
    exchangeTimeoutDays: 7,
    enableRegistration: true,
    enableGuestBrowsing: true,
    notificationEmail: true,
    notificationSms: false
  }

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)

      // 构建请求数据
      const settingsData = {
        ...values,
        logoUrl
      }

      await updateSettings(settingsData).unwrap()
      message.success('保存设置成功')
    } catch (err: any) {
      message.error(err?.data?.error?.message || '保存设置失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理文件上传
  const handleUpload = async (file: any) => {
    // 这里只是模拟上传，实际项目中需要调用上传API
    try {
      // 模拟上传延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      // 模拟返回图片URL
      const mockUrl = `https://example.com/logo-${Date.now()}.png`
      setLogoUrl(mockUrl)
      message.success('上传logo成功')
      return {
        status: 'success',
        url: mockUrl
      }
    } catch (error) {
      message.error('上传logo失败')
      return {
        status: 'error'
      }
    }
  }

  return (
    <div style={{ padding: 24 }}>
      <Card title="系统设置">
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <AlertOutlined style={{ color: '#faad14', marginRight: 8 }} />
          <span style={{ color: '#faad14', fontWeight: 'bold' }}>提示：修改设置后需要点击保存按钮生效</span>
        </div>

        <Divider style={{ margin: '16px 0' }} />

        <Form
          form={form}
          layout="vertical"
          initialValues={initialValues}
        >
          <Form.Item name="siteName" label="网站名称" rules={[{ required: true, message: '请输入网站名称' }]}>
            <Input placeholder="请输入网站名称" />
          </Form.Item>

          <Form.Item name="siteDescription" label="网站描述" rules={[{ required: true, message: '请输入网站描述' }]}>
            <TextArea rows={4} placeholder="请输入网站描述" />
          </Form.Item>

          <Form.Item label="网站Logo">
            <Upload
              name="logo"
              listType="picture"
              customRequest={handleUpload}
              maxCount={1}
              fileList={logoUrl ? [{ uid: '1', name: 'logo.png', status: 'done', url: logoUrl }] : []}
            >
              <Button icon={<UploadOutlined />}>上传Logo</Button>
            </Upload>
          </Form.Item>

          <Form.Item name="contactEmail" label="联系邮箱" rules={[{ required: true, message: '请输入联系邮箱' }, { type: 'email', message: '请输入有效的邮箱地址' }]}>
            <Input placeholder="请输入联系邮箱" />
          </Form.Item>

          <Form.Item name="contactPhone" label="联系电话" rules={[{ required: true, message: '请输入联系电话' }]}>
            <Input placeholder="请输入联系电话" />
          </Form.Item>

          <Divider style={{ margin: '24px 0' }} />

          <h3 style={{ marginBottom: 16 }}>用户设置</h3>

          <Form.Item name="maxItemPerUser" label="用户最大物品数" rules={[{ required: true, message: '请输入用户最大物品数' }, { type: 'number', min: 1, message: '至少为1' }]}>
            <InputNumber min={1} placeholder="请输入用户最大物品数" />
          </Form.Item>

          <Form.Item name="exchangeTimeoutDays" label="交换超时天数" rules={[{ required: true, message: '请输入交换超时天数' }, { type: 'number', min: 1, message: '至少为1' }]}>
            <InputNumber min={1} placeholder="请输入交换超时天数" />
          </Form.Item>

          <Form.Item name="enableRegistration" label="启用注册">
            <Switch />
          </Form.Item>

          <Form.Item name="enableGuestBrowsing" label="启用游客浏览">
            <Switch />
          </Form.Item>

          <Divider style={{ margin: '24px 0' }} />

          <h3 style={{ marginBottom: 16 }}>通知设置</h3>

          <Form.Item name="notificationEmail" label="启用邮件通知">
            <Switch />
          </Form.Item>

          <Form.Item name="notificationSms" label="启用短信通知">
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginTop: 24 }}>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSubmit}
              loading={loading}
              style={{ marginRight: 8 }}
            >
              保存设置
            </Button>
            <Button onClick={() => form.resetFields()}>重置</Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  )
}

export default Settings