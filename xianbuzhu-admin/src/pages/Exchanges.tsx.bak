import React, { useState } from 'react'
import { Button, Card, Table, Input, Space, Popconfirm, message, Form, Select } from 'antd'
import { SearchOutlined, EditOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { useGetExchangesQuery, useUpdateExchangeStatusMutation } from '@services/api'
import { useNavigate } from 'react-router-dom'

const { Search } = Input
const { Option } = Select

const Exchanges: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [updateExchangeStatus] = useUpdateExchangeStatusMutation()
  const navigate = useNavigate()

  const { data, error, isLoading, refetch } = useGetExchangesQuery({
    page: currentPage,
    pageSize,
    search: searchText,
    status: filterStatus
  })

  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  const handleUpdateStatus = async (id: string, status: string) => {
    try {
      await updateExchangeStatus({
        id,
        status
      }).unwrap()
      message.success('更新交换状态成功')
      refetch()
    } catch (err: any) {
      message.error(err?.data?.error?.message || '更新交换状态失败')
    }
  }

  const handleFilter = (status: string) => {
    setFilterStatus(status)
    setCurrentPage(1)
  }

  const columns = [
    {
      title: '交换ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '物品A',
      dataIndex: 'itemA',
      key: 'itemA',
      render: (item: any) => item?.name || '未知'
    },
    {
      title: '物品B',
      dataIndex: 'itemB',
      key: 'itemB',
      render: (item: any) => item?.name || '未知'
    },
    {
      title: '用户A',
      dataIndex: 'userA',
      key: 'userA',
      render: (user: any) => user?.username || '未知'
    },
    {
      title: '用户B',
      dataIndex: 'userB',
      key: 'userB',
      render: (user: any) => user?.username || '未知'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = '#faad14'
        let text = '处理中'

        if (status === 'completed') {
          color = '#52c41a'
          text = '已完成'
        } else if (status === 'rejected') {
          color = '#ff4d4f'
          text = '已拒绝'
        }

        return (
          <span style={{ color }}>
            {text}
          </span>
        )
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => navigate(`/exchanges/${record.id}`)}
          >
            查看详情
          </Button>
          {record.status === 'pending' && (
            <>
              <Button
                type="primary"
                icon={<CheckCircleOutlined />}
                size="small"
                onClick={() => handleUpdateStatus(record.id, 'completed')}
              >
                确认完成
              </Button>
              <Button
                danger
                icon={<AlertCircleOutlined />}
                size="small"
                onClick={() => handleUpdateStatus(record.id, 'rejected')}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ]

  return (
    <div style={{ padding: 24 }}>
      <Card title="交换管理">
        <div style={{ marginBottom: 16, display: 'flex', flexWrap: 'wrap', gap: 10 }}>
          <Search
            placeholder="搜索交换ID或物品名称"
            allowClear
            enterButton={<SearchOutlined />}
            size="middle"
            onSearch={handleSearch}
            style={{ width: 300 }}
          />
          <Form layout="inline">
            <Form.Item name="status">
              <Select
                placeholder="筛选状态"
                allowClear
                style={{ width: 150 }}
                onChange={handleFilter}
              >
                <Option value="all">全部</Option>
                <Option value="pending">处理中</Option>
                <Option value="completed">已完成</Option>
                <Option value="rejected">已拒绝</Option>
              </Select>
            </Form.Item>
          </Form>
        </div>

        <Table
          columns={columns}
          dataSource={data?.data?.items || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize,
            total: data?.data?.total || 0,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
          }}
        />
      </Card>
    </div>
  )
}

export default Exchanges