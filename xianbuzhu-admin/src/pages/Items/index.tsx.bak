import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, Table, Button, Input, Select, message, Popconfirm, Spin } from 'antd'
import { SearchOutlined, FilterOutlined, PlusOutlined, EditOutlined, DeleteOutlined, ShoppingOutlined, CheckOutlined, CloseOutlined, EyeOutlined } from '@ant-design/icons'
import { useAppDispatch, useAppSelector } from '@store/index'
import { setBreadcrumb, setPageTitle, setTableSettings, selectTableSettings } from '@store/slices/uiSlice'
import { useGetItemsQuery, useUpdateItemStatusMutation, useDeleteItemMutation } from '@services/api'
import { Item, ItemStatus, ItemCondition, ItemCategory } from '@/types/api'
import { selectTheme } from '@store/slices/uiSlice'

const { Search } = Input
const { Option } = Select

const Items: React.FC = () => {
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const theme = useAppSelector(selectTheme)
  const tableKey = 'items'
  const tableSettings = useAppSelector((state) => selectTableSettings(state, tableKey))
  const [searchKeyword, setSearchKeyword] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(tableSettings.pageSize || 1)
  const [pageSize, setPageSize] = useState(tableSettings.pageSize || 10)
  const [categories, setCategories] = useState<ItemCategory[]>([])

  // API hooks
  const { data, isLoading, error, refetch } = useGetItemsQuery({
    page: currentPage,
    pageSize,
    search: searchKeyword,
    status: statusFilter,
    category: categoryFilter
  })
  const [updateItemStatus] = useUpdateItemStatusMutation()
  const [deleteItem] = useDeleteItemMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '物品管理' }
    ]))
    dispatch(setPageTitle('物品管理'))

      // 获取真实分类数据
    try {
      // 调用API获取分类
      const response = await fetch('/api/categories');
      if (!response.ok) throw new Error('网络响应异常');
      const data = await response.json();
      setCategories(data || []);
    } catch (error) {
      console.error('获取分类数据失败:', error);
      // 出错时使用模拟数据
      const mockCategories = [
        { id: '1', name: '电子产品' },
        { id: '2', name: '家居用品' },
        { id: '3', name: '服装服饰' },
        { id: '4', name: '图书文具' },
        { id: '5', name: '运动器材' },
        { id: '6', name: '其他' }
      ];
      setCategories(mockCategories);
    }
  }, [dispatch])

  // 保存表格设置
  useEffect(() => {
    dispatch(setTableSettings({
      tableKey,
      settings: {
        pageSize
      }
    }))
  }, [pageSize, dispatch, tableKey])

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value)
    setCurrentPage(1)
  }

  // 处理状态筛选
  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
    // 立即触发重新获取数据
    refetch();
  }

  // 处理分类筛选
  const handleCategoryChange = (value: string) => {
    setCategoryFilter(value);
    setCurrentPage(1);
    // 立即触发重新获取数据
    refetch();
  }

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrentPage(pagination.current)
    setPageSize(pagination.pageSize)
  }

  // 处理物品状态更新
  const handleStatusUpdate = async (itemId: string, status: ItemStatus) => {
    try {
      const reason = status === ItemStatus.REJECTED ? '不符合平台规定' : ''
      updateItemStatus({
        itemId,
        status,
        reason
      }).unwrap()
      message.success('物品状态更新成功')
      refetch()
    } catch (err) {
      message.error(`更新失败: ${err.error?.message || '未知错误'}`)
    }
  }

  // 处理物品删除
  const handleDelete = async (itemId: string) => {
    try {
      deleteItem(itemId).unwrap()
      message.success('物品删除成功')
      refetch()
    } catch (err) {
      message.error(`删除失败: ${err.error?.message || '未知错误'}`)
    }
  }

  // 获取物品状态文本
  const getStatusText = (status: ItemStatus) => {
    switch (status) {
      case ItemStatus.AVAILABLE: return '可交换'
      case ItemStatus.EXCHANGING: return '交换中'
      case ItemStatus.EXCHANGED: return '已交换'
      case ItemStatus.REMOVED: return '已移除'
      case ItemStatus.PENDING_REVIEW: return '待审核'
      case ItemStatus.REJECTED: return '已拒绝'
      default: return status
    }
  }

  // 获取物品状态样式
  const getStatusStyle = (status: ItemStatus) => {
    switch (status) {
      case ItemStatus.AVAILABLE: return { bg: '#52c41a20', color: '#52c41a' }
      case ItemStatus.EXCHANGING: return { bg: '#faad1420', color: '#faad14' }
      case ItemStatus.EXCHANGED: return { bg: '#1890ff20', color: '#1890ff' }
      case ItemStatus.REMOVED: return { bg: '#ff4d4f20', color: '#ff4d4f' }
      case ItemStatus.PENDING_REVIEW: return { bg: '#722ed120', color: '#722ed1' }
      case ItemStatus.REJECTED: return { bg: '#ff4d4f20', color: '#ff4d4f' }
      default: return { bg: '#f0f0f0', color: '#666' }
    }
  }

  // 获取物品新旧程度文本
  const getConditionText = (condition: ItemCondition) => {
    switch (condition) {
      case ItemCondition.NEW: return '全新'
      case ItemCondition.LIKE_NEW: return '几乎全新'
      case ItemCondition.GOOD: return '良好'
      case ItemCondition.FAIR: return '一般'
      case ItemCondition.POOR: return '较差'
      default: return condition
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '物品ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '物品信息',
      key: 'info',
      render: (record: Item) => (
        <div style={{ display: 'flex', alignItems: 'flex-start' }}>
          <div
            style={{
              width: '60px',
              height: '60px',
              borderRadius: '4px',
              backgroundColor: '#f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: '12px',
              overflow: 'hidden',
              flexShrink: 0
            }}
          >
            {record.images && record.images.length > 0 ? (
              <img src={record.images[0]} alt={record.title} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
            ) : (
              <ShoppingOutlined size={24} />
            )}
          </div>
          <div style={{ flex: 1 }}>
            <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{record.title}</p>
            <p style={{ margin: '4px 0', fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>{record.description.substring(0, 30)}...</p>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginTop: '4px' }}>
              <span
                style={{
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  backgroundColor: '#1890ff20',
                  color: '#1890ff'
                }}
              >
                {record.category.name}
              </span>
              <span
                style={{
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  backgroundColor: '#faad1420',
                  color: '#faad14'
                }}
              >
                {getConditionText(record.condition)}
              </span>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '发布者',
      dataIndex: 'publisherName',
      key: 'publisherName',
      width: 120
    },
    {
      title: '社区',
      dataIndex: 'communityName',
      key: 'communityName',
      width: 120
    },
    {
      title: '估值',
      dataIndex: 'estimatedValue',
      key: 'estimatedValue',
      width: 80,
      render: (value: number) => (
        <span>¥{value}</span>
      )
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Item) => {
        const style = getStatusStyle(record.status)
        return (
          <span
            style={{
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              backgroundColor: style.bg,
              color: style.color
            }}
          >
            {getStatusText(record.status)}
          </span>
        )
      },
      width: 100
    },
    {
      title: '发布时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => (
        <span>{new Date(text).toLocaleString()}</span>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      render: (record: Item) => (
        <div style={{ display: 'flex', justifyContent: 'center', gap: '8px' }}>
          {record.status === ItemStatus.PENDING_REVIEW && (
              <Button
                type="primary"
                size="small"
                icon={<CheckOutlined size={16} />}
                onClick={() => handleStatusUpdate(record.id, ItemStatus.AVAILABLE)}
              >
                通过
              </Button>
            )}
            {record.status === ItemStatus.PENDING_REVIEW && (
              <Button
                type="danger"
                size="small"
                icon={<CloseOutlined size={16} />}
                onClick={() => handleStatusUpdate(record.id, ItemStatus.REJECTED)}
              >
                拒绝
              </Button>
            )}
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined size={16} />}
              onClick={() => navigate(`/items/${record.id}/edit`)}
            >
              编辑
            </Button>
            <Button
              size="small"
              icon={<EyeOutlined size={16} />}
              onClick={() => navigate(`/items/${record.id}`)}
            >
              查看
            </Button>
            <Popconfirm
              title="确认删除此物品？"
              onConfirm={() => handleDelete(record.id)}
              okText="确认"
              cancelText="取消"
            >
              <Button
                type="danger"
                size="small"
                icon={<DeleteOutlined size={16} />}
              >
                删除
              </Button>
            </Popconfirm>
        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
          <AlertCircleOutlined style={{ marginRight: '8px' }} />
          <span>获取数据失败: {error.error || '未知错误'}</span>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px' }}>
      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: '24px'
        }}
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            size="middle"
            onClick={() => navigate('/items/create')}
          >
            添加物品
          </Button>
        }
      >
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', alignItems: 'center' }}>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <Search
              placeholder="搜索物品..."
              prefix={<SearchOutlined />}
              allowClear
              enterButton="搜索"
              size="middle"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flexWrap: 'wrap' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <FilterOutlined style={{ marginRight: '8px', color: '#666' }} />
              <Select
                placeholder="筛选状态"
                size="middle"
                value={statusFilter}
                onChange={handleStatusChange}
                allowClear
              >
                <Option value={ItemStatus.PENDING_REVIEW}>待审核</Option>
                <Option value={ItemStatus.AVAILABLE}>可交换</Option>
                <Option value={ItemStatus.EXCHANGING}>交换中</Option>
                <Option value={ItemStatus.EXCHANGED}>已交换</Option>
                <Option value={ItemStatus.REJECTED}>已拒绝</Option>
                <Option value={ItemStatus.REMOVED}>已移除</Option>
              </Select>
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <FilterOutlined style={{ marginRight: '8px', color: '#666' }} />
              <Select
                placeholder="筛选分类"
                size="middle"
                value={categoryFilter}
                onChange={handleCategoryChange}
                allowClear
              >
                {categories.map(category => (
                  <Option key={category.id} value={category.id}>{category.name}</Option>
                ))}
              </Select>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="middle"
              onClick={() => navigate('/items/create')}
            >
              新增物品
            </Button>
          </div>
        </div>
      </Card>

      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
        }}
      >
        <Table
          columns={columns}
          dataSource={data?.data?.items || []}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize,
            total: data?.data?.total || 0,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true
          }}
          onChange={handleTableChange}
          loading={isLoading}
          style={{ color: theme === 'dark' ? '#fff' : '#333' }}
        />
      </Card>
    </div>
  )
}

export default Items