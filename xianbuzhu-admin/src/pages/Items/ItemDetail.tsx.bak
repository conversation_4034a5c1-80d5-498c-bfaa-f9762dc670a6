import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, Button, Descriptions, Image, message, Spin, Modal } from 'antd'
import { ArrowLeftOutlined, EditOutlined, DeleteOutlined, AlertCircleOutlined } from '@ant-design/icons'
import { useAppDispatch } from '@store/index'
import { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'
import { useGetItemByIdQuery, useDeleteItemMutation } from '@services/api'
import { Item, ItemStatus, ItemCondition } from '@/types/api'
import { selectTheme } from '@store/slices/uiSlice'
import { useAppSelector } from '@store/index'

const ItemDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const theme = useAppSelector(selectTheme)
  const [deleteItem] = useDeleteItemMutation()
  const [loading, setLoading] = useState(true)
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [previewVisible, setPreviewVisible] = useState(false)

  // 获取物品详情
  // 处理id可能为undefined的情况
  const { data: item, isLoading, error } = useGetItemByIdQuery(id || '', { skip: !id })

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '物品管理', path: '/items' },
      { title: '物品详情' }
    ]))
    dispatch(setPageTitle('物品详情'))
  }, [dispatch])

  // 处理加载状态
  useEffect(() => {
    setLoading(isLoading)
  }, [isLoading])

  // 处理删除物品
  const handleDelete = async () => {
    if (!id) return

    try {
      await deleteItem(id).unwrap()
      message.success('物品删除成功')
      navigate('/items')
    } catch (err: any) {
      message.error(`删除失败: ${err?.data?.message || '未知错误'}`)
    }
  }

  // 获取物品状态文本
  const getStatusText = (status: ItemStatus) => {
    switch (status) {
      case ItemStatus.AVAILABLE: return '可交换'
      case ItemStatus.EXCHANGING: return '交换中'
      case ItemStatus.EXCHANGED: return '已交换'
      case ItemStatus.REMOVED: return '已移除'
      case ItemStatus.PENDING_REVIEW: return '待审核'
      case ItemStatus.REJECTED: return '已拒绝'
      default: return status
    }
  }

  // 获取物品状态样式
  const getStatusStyle = (status: ItemStatus) => {
    switch (status) {
      case ItemStatus.AVAILABLE: return { bg: '#52c41a20', color: '#52c41a' }
      case ItemStatus.EXCHANGING: return { bg: '#faad1420', color: '#faad14' }
      case ItemStatus.EXCHANGED: return { bg: '#1890ff20', color: '#1890ff' }
      case ItemStatus.REMOVED: return { bg: '#ff4d4f20', color: '#ff4d4f' }
      case ItemStatus.PENDING_REVIEW: return { bg: '#722ed120', color: '#722ed1' }
      case ItemStatus.REJECTED: return { bg: '#ff4d4f20', color: '#ff4d4f' }
      default: return { bg: '#f0f0f0', color: '#666' }
    }
  }

  // 获取物品新旧程度文本
  const getConditionText = (condition: ItemCondition) => {
    switch (condition) {
      case ItemCondition.NEW: return '全新'
      case ItemCondition.LIKE_NEW: return '几乎全新'
      case ItemCondition.GOOD: return '良好'
      case ItemCondition.FAIR: return '一般'
      case ItemCondition.POOR: return '较差'
      default: return condition
    }
  }

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error || !item?.data || !id) {
    return (
      <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
          <AlertCircleOutlined style={{ marginRight: '8px' }} />
          <span>获取物品详情失败: {(error as any)?.data?.message || (id ? '物品不存在' : '缺少物品ID')}</span>
        </div>
      </div>
    )
  }

  const itemData = item.data

  return (
    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px' }}>
      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: '24px'
        }}
        extra={
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              type="primary"
              icon={<EditOutlined size={16} />}
              onClick={() => navigate(`/items/${id}/edit`)} // 注意这里的路径
            >
              编辑
            </Button>
            <Button
              danger
              icon={<DeleteOutlined size={16} />}
              onClick={handleDelete}
            >
              删除
            </Button>
          </div>
        }
      >
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/items')}
            style={{ marginRight: '16px' }}
          >
            返回列表
          </Button>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>
            {itemData.title}
          </h1>
        </div>
      </Card>

      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: '24px'
        }}
      >
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '24px' }}>
          <div style={{ flex: '0 0 400px', maxWidth: '100%' }}>
            <Image
              width="100%"
              src={itemData.images && itemData.images.length > 0 ? itemData.images[0] : 'https://via.placeholder.com/400x300?text=No+Image'}
              alt={itemData.title}
              style={{ borderRadius: '8px' }}
            />
            <div style={{ display: 'flex', gap: '8px', marginTop: '12px', flexWrap: 'wrap' }}>
              {(itemData.images || []).map((img, index) => (
                <Image
                  key={index}
                  width={80}
                  height={80}
                  src={img}
                  alt={`${itemData.title} - ${index + 1}`}
                  style={{ borderRadius: '4px', cursor: 'pointer', objectFit: 'cover' }}
                  onClick={() => {
                    setPreviewImage(img)
                    setPreviewVisible(true)
                  }}
                />
              ))}
            </div>
            <Modal
              visible={previewVisible}
              title="图片预览"
              footer={null}
              onCancel={() => setPreviewVisible(false)}
              width={800}
            >
              {previewImage && (
                <Image
                  width="100%"
                  src={previewImage}
                  alt="预览图片"
                />
              )}
            </Modal>
          </div>
          <div style={{ flex: '1', minWidth: '300px' }}>
            <Descriptions
              column={1}
              title="物品信息"
              bordered
              layout="vertical"
              style={{ backgroundColor: theme === 'dark' ? '#252525' : '#fafafa', borderRadius: '8px' }}
            >
              <Descriptions.Item label="物品ID" style={{ fontWeight: 'bold' }}>{itemData.id}</Descriptions.Item>
              <Descriptions.Item label="标题">{itemData.title}</Descriptions.Item>
              <Descriptions.Item label="描述" style={{ whiteSpace: 'pre-wrap' }}>{itemData.description}</Descriptions.Item>
              <Descriptions.Item label="分类">{itemData.category?.name || '未分类'}</Descriptions.Item>
              <Descriptions.Item label="新旧程度">{getConditionText(itemData.condition)}</Descriptions.Item>
              <Descriptions.Item label="估值">¥{itemData.estimatedValue}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <span
                  style={{
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    ...getStatusStyle(itemData.status)
                  }}
                >
                  {getStatusText(itemData.status)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="发布者">{itemData.publisher?.name || '未知'}</Descriptions.Item>
              <Descriptions.Item label="发布者ID">{itemData.publisherId || '未知'}</Descriptions.Item>
              <Descriptions.Item label="所在社区">{itemData.community?.name || '未知'}</Descriptions.Item>
              <Descriptions.Item label="发布时间">{new Date(itemData.createdAt).toLocaleString()}</Descriptions.Item>
              {itemData.updatedAt && (
                <Descriptions.Item label="更新时间">{new Date(itemData.updatedAt).toLocaleString()}</Descriptions.Item>
              )}
            </Descriptions>
          </div>
        </div>
      </Card>

      {/* 交换记录部分已移除，因为Item类型中没有exchange属性 */}
    </div>
  )
}

export default ItemDetail