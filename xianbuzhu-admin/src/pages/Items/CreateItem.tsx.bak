import React, { useEffect, useState } from 'react'
import { Card, Form, Input, Select, Upload, Button, message } from 'antd'
import { ArrowLeftOutlined, CheckOutlined, UploadOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch } from '@store/index'
import { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'
import { useCreateItemMutation } from '@services/api'
import { ItemCategory, ItemCondition } from '@/types/api'
import { useAppSelector } from '@store/index'
import { selectTheme } from '@store/slices/uiSlice'

const { Option } = Select
const { TextArea } = Input

const CreateItem: React.FC = () => {
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const theme = useAppSelector(selectTheme)
  const [form] = Form.useForm<{
    title: string;
    description: string;
    categoryId: string;
    condition: ItemCondition;
    estimatedValue: number;
    location: string;
  }>()
  const [categories, setCategories] = useState<ItemCategory[]>([])
  const [images, setImages] = useState<string[]>([])
  const [submitLoading, setSubmitLoading] = useState(false)

  // API hooks
  const [createItem] = useCreateItemMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '物品管理', path: '/items' },
      { title: '创建物品' }
    ]))
    dispatch(setPageTitle('创建物品'))

    // 获取分类数据
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (!response.ok) throw new Error('网络响应异常');
      const data = await response.json();
      setCategories(data || []);
    } catch (error) {
      console.error('获取分类数据失败:', error);
      // 出错时使用模拟数据
      const mockCategories = [
        { id: '1', name: '电子产品' },
        { id: '2', name: '家居用品' },
        { id: '3', name: '服装服饰' },
        { id: '4', name: '图书文具' },
        { id: '5', name: '运动器材' },
        { id: '6', name: '其他' }
      ];
      setCategories(mockCategories);
    }
  }

  fetchCategories();
  }, [dispatch])

  // 处理表单提交
  const handleSubmit = async (values: {
    title: string;
    description: string;
    categoryId: string;
    condition: ItemCondition;
    estimatedValue: number;
    location: string;
  }) => {
    try {
      setSubmitLoading(true)
      const formData = new FormData()
      formData.append('title', values.title)
      formData.append('description', values.description)
      formData.append('categoryId', values.categoryId)
      formData.append('condition', values.condition)
      formData.append('estimatedValue', values.estimatedValue.toString())
      // 移除isFeatured字段，因为Item类型中没有这个属性
      formData.append('location', values.location)

      // 添加已上传的图片URL
      images.forEach((imageUrl, index) => {
        formData.append(`images[${index}]`, imageUrl)
      })

      await createItem(formData).unwrap()
      message.success('物品创建成功')
      navigate('/items')
    } catch (err: any) {
      message.error(`创建失败: ${err?.data?.message || '未知错误'}`)
    } finally {
      setSubmitLoading(false)
    }
  }

  // 处理图片上传
  const handleImageUpload = async (options: {
    file: File;
    onSuccess: (response: any) => void;
    onError: (error: Error) => void;
  }) => {
    const { file } = options;
    try {
      const formData = new FormData();
      formData.append('image', file);
      
      // 实际项目中使用真实的上传API
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });
      const data = await response.json();
      
      if (response.ok && data.success) {
        message.success(`${file.name} 上传成功`);
        const newImageUrl = data.data.url;
        setImages([...images, newImageUrl]);
        return {
          status: 'success',
          url: newImageUrl
        };
      } else {
        const error = new Error(data.message || '上传失败');
        options.onError(error);
        throw error;
      }
    } catch (error) {
      message.error(`${file.name} 上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    options.onError(error instanceof Error ? error : new Error('未知错误'));
    return {
      status: 'error'
    };
    }
  }

  // 获取物品新旧程度选项
  const getConditionOptions = () => {
    return [
      { value: ItemCondition.NEW, label: '全新' },
      { value: ItemCondition.LIKE_NEW, label: '几乎全新' },
      { value: ItemCondition.GOOD, label: '良好' },
      { value: ItemCondition.FAIR, label: '一般' },
      { value: ItemCondition.POOR, label: '较差' },
    ]
  }

  return (
    <div style={{ padding: 24 }}>
      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: 24
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/items')}
            style={{ marginRight: 16 }}
          >
            返回
          </Button>
          <h2 style={{ margin: 0, fontSize: 20, fontWeight: 600 }}>创建物品</h2>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{}}
        >
          <Form.Item
            name="title"
            label="物品标题"
            rules={[{
              required: true,
              message: '请输入物品标题'
            }, {
              max: 50,
              message: '标题长度不能超过50个字符'
            }]}
          >
            <Input placeholder="请输入物品标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="物品描述"
            rules={[{
              required: true,
              message: '请输入物品描述'
            }, {
              min: 10,
              message: '描述长度不能少于10个字符'
            }]}
          >
            <TextArea rows={5} placeholder="请详细描述物品情况，包括品牌、型号、使用时长等信息" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="物品分类"
            rules={[{
              required: true,
              message: '请选择物品分类'
            }]}
          >
            <Select placeholder="请选择物品分类">
              {categories.map(category => (
                <Option key={category.id} value={category.id}>{category.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="condition"
            label="新旧程度"
            rules={[{
              required: true,
              message: '请选择物品新旧程度'
            }]}
          >
            <Select placeholder="请选择物品新旧程度">
              {getConditionOptions().map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="estimatedValue"
            label="物品估值（元）"
            rules={[{
              required: true,
              message: '请输入物品估值'
            }, {
              pattern: /^\d+(\.\d{1,2})?$/,
              message: '请输入有效的金额格式'
            }]}
          >
            <Input type="number" placeholder="请输入物品估值" />
          </Form.Item>

          <Form.Item
            name="location"
            label="物品位置"
            rules={[{
              required: true,
              message: '请输入物品位置'
            }]}
          >
            <Input placeholder="请输入物品所在位置" />
          </Form.Item>

          {/* 移除isFeatured字段，因为Item类型中没有这个属性 */}

          <Form.Item label="物品图片">
            <Upload
            customRequest={handleImageUpload}
            listType="picture-card"
            maxCount={5}
            fileList={images.map((url, index) => ({
              uid: index.toString(),
              name: `image-${index + 1}.jpg`,
              status: 'done',
              url
            }))}
            onRemove={(file) => {
              const index = parseInt(file.uid);
              setImages(images.filter((_, i) => i !== index));
              message.success('图片已删除');
            }}
            beforeUpload={() => false}
          >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
            <div style={{ marginTop: 16 }}>
              {images.map((image, index) => (
                <div key={index} style={{ display: 'inline-block', marginRight: 16, position: 'relative' }}>
                  <img src={image} alt={`物品图片${index + 1}`} style={{ width: 100, height: 100, objectFit: 'cover' }} />
                </div>
              ))}
            </div>
          </Form.Item>

          <Form.Item style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 24 }}>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              loading={submitLoading}
              htmlType="submit"
            >
              创建物品
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  )
}

export default CreateItem