import React, { useEffect } from 'react'
import { Card, Descriptions, Button, Divider, message, Spin, AlertCircleOutlined, UserOutlined } from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import { useGetUserByIdQuery, useUpdateUserStatusMutation } from '@services/api'
import { useAppDispatch } from '@store/index'
import { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'
import { User } from '@/types'

const UserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { data, isLoading, error, refetch } = useGetUserByIdQuery(id || '')
  const [updateUserStatus] = useUpdateUserStatusMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '用户管理', path: '/users' },
      { title: '用户详情' }
    ]))
    dispatch(setPageTitle('用户详情'))
  }, [dispatch, id])

  const handleStatusChange = async (status: string) => {
    if (!id) return

    try {
      await updateUserStatus({
        userId: id,
        status,
        reason: status === 'banned' ? '违反平台规定' : ''
      }).unwrap()
      message.success('用户状态更新成功')
      refetch()
    } catch (err) {
      message.error(`更新失败: ${err.error?.message || '未知错误'}`)
    }
  }

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error || !data?.data) {
    return (
      <div style={{ padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
          <AlertCircleOutlined style={{ marginRight: '8px' }} />
          <span>获取用户信息失败: {error instanceof Error ? error.message : error.error || '未知错误'}</span>
        </div>
      </div>
    )
  }

  const user = data.data as User

  return (
    <div style={{ padding: 24 }}>
      <Card title="用户详情" extra={<Button onClick={() => navigate('/users')}>返回列表</Button>}>
        <div style={{ marginBottom: 24, display: 'flex', alignItems: 'center' }}>
          <div
            style={{
              width: '100px',
              height: '100px',
              borderRadius: '50%',
              backgroundColor: '#f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: '24px',
              overflow: 'hidden'
            }}
          >
            {user.avatar ? (
              <img src={user.avatar} alt={user.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
            ) : (
              <UserOutlined size={40} />
            )}
          </div>
          <div>
            <h2 style={{ margin: 0 }}>{user.name}</h2>
            <p style={{ color: '#666', margin: '4px 0' }}>{user.phone}</p>
            <span
              style={{
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                backgroundColor: user.status === 'active' ? '#52c41a20' :
                                user.status === 'suspended' ? '#faad1420' : '#ff4d4f20',
                color: user.status === 'active' ? '#52c41a' :
                      user.status === 'suspended' ? '#faad14' : '#ff4d4f'
              }}
            >
              {user.status === 'active' ? '活跃' :
               user.status === 'suspended' ? '暂停' : '禁用'}
            </span>
          </div>
        </div>

        <Descriptions title="基本信息" bordered column={2}>
          <Descriptions.Item label="用户ID">{user.id}</Descriptions.Item>
          <Descriptions.Item label="邮箱">{user.email || '-'}</Descriptions.Item>
          <Descriptions.Item label="手机号">{user.phone || '-'}</Descriptions.Item>
          <Descriptions.Item label="注册时间">{new Date(user.createdAt).toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="最后登录">{user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : '-'}</Descriptions.Item>
          <Descriptions.Item label="社区">{user.communityName || '-'}</Descriptions.Item>
          <Descriptions.Item label="信任等级">{user.trustLevel?.level || '-'}</Descriptions.Item>
          <Descriptions.Item label="积分">{user.points || 0}</Descriptions.Item>
        </Descriptions>

        <Divider style={{ margin: '24px 0' }} />

        <div style={{ display: 'flex', gap: '16px' }}>
          <Button
            type="primary"
            onClick={() => handleStatusChange(user.status === 'active' ? 'suspended' : 'active')}
          >
            {user.status === 'active' ? '暂停账号' : '启用账号'}
          </Button>
          <Button
            danger
            onClick={() => handleStatusChange(user.status === 'banned' ? 'active' : 'banned')}
          >
            {user.status === 'banned' ? '解封账号' : '禁用账号'}
          </Button>
          <Button onClick={() => navigate(`/users/${id}/edit`)}>编辑信息</Button>
        </div>
      </Card>
    </div>
  )
}



export default UserDetail