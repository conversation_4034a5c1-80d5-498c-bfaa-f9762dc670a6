import React, { useEffect, useState } from 'react'
import { Card, Form, Input, Button, Select, message, Spin } from 'antd'
import { AlertCircleOutlined } from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'
import { useGetUserByIdQuery, useUpdateUserMutation } from '@services/api'
import { useAppDispatch } from '@store/index'
import { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'
import { User } from '@/types'
import type { FetchBaseQueryError, SerializedError } from '@reduxjs/toolkit/query'

const { Option } = Select

const UserEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const { data, isLoading: fetchLoading, error } = useGetUserByIdQuery(id || '')
  const [updateUser] = useUpdateUserMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setPageTitle('编辑用户'))
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '用户管理', path: '/users' },
      { title: '编辑用户', path: `/users/${id}/edit` }
    ]))
  }, [id, dispatch])

  // 加载用户数据到表单
  useEffect(() => {
    if (data?.data) {
      const user = data.data as User
      form.setFieldsValue({
        name: user.name,
        email: user.email || '',
        phone: user.phone || '',
        status: user.status,
        communityId: user.communityId || '',
        trustLevel: user.trustLevel?.level || 1
      })
    }
  }, [data, form])

  const handleSubmit = async (values: any) => {
    if (!id) return

    setLoading(true)
    try {
      await updateUser({
        userId: id,
        data: values
      }).unwrap()
      message.success('用户信息更新成功')
      navigate(`/users/${id}`)
    } catch (err) {
      const errorMessage = (
        (err as FetchBaseQueryError).data?.message ||
        (err as SerializedError).message ||
        '未知错误'
      )
      message.error(`更新失败: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  // 返回用户详情页
  const handleCancel = () => {
    navigate(`/users/${id}`)
  }

  if (fetchLoading) {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载用户数据中...</p>
      </div>
    )
  }

  if (error || !data?.data) {
    const errorMessage = (
      error && (error as FetchBaseQueryError).data?.message ||
      error && (error as SerializedError).message ||
      '未知错误'
    )
    return (
      <div style={{ padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f', marginBottom: 16 }}>
          <AlertCircleOutlined style={{ marginRight: '8px' }} />
          <span>获取用户信息失败: {errorMessage}</span>
        </div>
        <Button onClick={() => window.location.reload()}>重新加载</Button>
      </div>
    )
  }

  return (
    <div style={{ padding: 24 }}>
      <Card title="编辑用户信息" extra={<Button onClick={handleCancel}>返回详情</Button>}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ status: 'active', trustLevel: 1 }}
        >
          <Form.Item name="name" label="用户名称" rules={[{ required: true, message: '请输入用户名称' }]}>
            <Input placeholder="请输入用户名称" />
          </Form.Item>

          <Form.Item name="email" label="电子邮箱" rules={[{ required: true, message: '请输入电子邮箱' }, { type: 'email', message: '请输入有效的电子邮箱' }]}>
            <Input placeholder="请输入电子邮箱" />
          </Form.Item>

          <Form.Item name="phone" label="手机号" rules={[{ required: true, message: '请输入手机号' }, { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }]}>
            <Input placeholder="请输入手机号" />
          </Form.Item>

          <Form.Item name="status" label="用户状态" rules={[{ required: true, message: '请选择用户状态' }]}>
            <Select placeholder="请选择用户状态">
              <Option value="active">活跃</Option>
              <Option value="suspended">暂停</Option>
              <Option value="banned">禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item name="communityId" label="所属社区ID" rules={[{ required: true, message: '请输入所属社区ID' }]}>
            <Input placeholder="请输入所属社区ID" />
          </Form.Item>

          <Form.Item name="trustLevel" label="信任等级" rules={[{ required: true, message: '请选择信任等级' }]}>
            <Select placeholder="请选择信任等级">
              {[1, 2, 3, 4, 5].map(level => (
                <Option key={level} value={level}>等级 {level}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginTop: 24 }}>
            <Button type="primary" htmlType="submit" loading={loading} style={{ marginRight: 16 }}>
              保存修改
            </Button>
            <Button onClick={handleCancel}>取消</Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  )
}


export default UserEdit