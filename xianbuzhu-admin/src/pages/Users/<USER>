import React, { useEffect, useState } from 'react'
import { Card, Form, Input, Button, Select, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import { useCreateUserMutation } from '@services/api'
import { useAppDispatch } from '@store/index'
import { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'

const { Option } = Select

const CreateUser: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [form] = Form.useForm()
  const [loading, setLoading] = React.useState(false)
  const [createUser] = useCreateUserMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '用户管理', path: '/users' },
      { title: '创建用户' }
    ]))
    dispatch(setPageTitle('创建用户'))
  }, [dispatch])

  const handleSubmit = async (values: any) => {
    setLoading(true)
    try {
      await createUser(values).unwrap()
      message.success('用户创建成功')
      navigate('/users')
    } catch (err) {
      message.error(`创建失败: ${err.error?.message || '未知错误'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div style={{ padding: 24 }}>
      <Card title="创建用户" extra={<Button onClick={() => navigate('/users')}>返回列表</Button>}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ status: 'active', trustLevel: 1 }}
        >
          <Form.Item name="username" label="用户名" rules={[{ required: true, message: '请输入用户名' }]}>
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item name="password" label="密码" rules={[{ required: true, message: '请输入密码' }, { min: 6, message: '密码长度不能少于6位' }]}>
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <Form.Item name="name" label="用户名称" rules={[{ required: true, message: '请输入用户名称' }]}>
            <Input placeholder="请输入用户名称" />
          </Form.Item>

          <Form.Item name="email" label="邮箱" rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}>
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item name="phone" label="手机号" rules={[{ pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }]}>
            <Input placeholder="请输入手机号" />
          </Form.Item>

          <Form.Item name="status" label="用户状态" rules={[{ required: true, message: '请选择用户状态' }]}>
            <Select placeholder="请选择用户状态">
              <Option value="active">活跃</Option>
              <Option value="suspended">暂停</Option>
            </Select>
          </Form.Item>

          <Form.Item name="communityId" label="所属社区ID">
            <Input placeholder="请输入所属社区ID" />
          </Form.Item>

          <Form.Item name="trustLevel" label="信任等级">
            <Select placeholder="请选择信任等级">
              {[1, 2, 3, 4, 5].map(level => (
                <Option key={level} value={level}>等级 {level}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginTop: 24 }}>
            <Button type="primary" htmlType="submit" loading={loading} style={{ marginRight: 16 }}>
              创建用户
            </Button>
            <Button onClick={() => navigate('/users')}>取消</Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  )
}



export default CreateUser