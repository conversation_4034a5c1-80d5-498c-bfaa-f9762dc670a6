import React, { useEffect, useState } from 'react'
import { Card, Table, Button, Input, Select, message, Popconfirm, Spin } from 'antd'
import { SearchOutlined, FilterOutlined, PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, CheckOutlined, CloseOutlined, AlertCircleOutlined, EyeOutlined } from '@ant-design/icons'
import { useAppDispatch, useAppSelector } from '@store/index'
import { setBreadcrumb, setPageTitle, setTableSettings, selectTableSettings } from '@store/slices/uiSlice'
import { useGetUsersQuery, useUpdateUserStatusMutation } from '@services/api'
import { User, PaginatedResponse } from '@/types'
import { selectTheme } from '@store/slices/uiSlice'
import { useNavigate } from 'react-router-dom'

const { Search } = Input
const { Option } = Select

const Users: React.FC = () => {
  const dispatch = useAppDispatch()
  const theme = useAppSelector(selectTheme)
  const navigate = useNavigate()
  const tableKey = 'users'
  const tableSettings = useAppSelector((state) => selectTableSettings(state, tableKey))
  const [searchKeyword, setSearchKeyword] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(tableSettings.current || 1)
  const [pageSize, setPageSize] = useState(tableSettings.pageSize || 10)

  // API hooks
  const { data, isLoading, error, refetch } = useGetUsersQuery({
    page: currentPage,
    pageSize,
    search: searchKeyword,
    status: statusFilter
  })
  const [updateUserStatus] = useUpdateUserStatusMutation()

  // 设置面包屑和页面标题
  useEffect(() => {
    dispatch(setBreadcrumb([
      { title: '首页', path: '/dashboard' },
      { title: '用户管理' }
    ]))
    dispatch(setPageTitle('用户管理'))
  }, [dispatch])

  // 保存表格设置
  useEffect(() => {
    dispatch(setTableSettings({
      tableKey,
      settings: {
        pageSize
      }
    }))
  }, [pageSize, dispatch, tableKey])

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value)
    setCurrentPage(1)
  }

  // 处理状态筛选
  const handleStatusChange = (value: string) => {
    setStatusFilter(value)
    setCurrentPage(1)
  }

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setCurrentPage(pagination.current)
    setPageSize(pagination.pageSize)
  }

  // 处理用户状态更新
  const handleStatusUpdate = async (userId: string, status: string) => {
    try {
      const reason = status === 'banned' ? '违反平台规定' : ''
      await updateUserStatus({
        userId,
        status,
        reason
      }).unwrap()
      message.success('用户状态更新成功')
      refetch()
    } catch (err) {
      message.error(`更新失败: ${err.error?.message || '未知错误'}`)
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '用户ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '用户信息',
      key: 'info',
      render: (record: User) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div
            style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: '#f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: '12px',
              overflow: 'hidden'
            }}
          >
            {record.avatar ? (
              <img src={record.avatar} alt={record.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
            ) : (
              <UserOutlined size={20} />
            )}
          </div>
          <div>
            <p style={{ margin: 0, fontWeight: 'bold', color: theme === 'dark' ? '#fff' : '#333' }}>{record.name}</p>
            <p style={{ margin: 0, fontSize: '12px', color: theme === 'dark' ? '#999' : '#666' }}>{record.phone}</p>
          </div>
        </div>
      )
    },
    {
      title: '社区',
      dataIndex: 'communityName',
      key: 'communityName',
      width: 120
    },
    {
      title: '信任等级',
      key: 'trustLevel',
      render: (record: User) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div
            style={{
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              backgroundColor: '#1890ff20',
              color: '#1890ff',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: '8px',
              fontSize: '12px',
              fontWeight: 'bold'
            }}
          >
            {record.trustLevel.level}
          </div>
          <span>{record.trustLevel.score}分</span>
        </div>
      ),
      width: 120
    },
    {
      title: '积分',
      dataIndex: 'points',
      key: 'points',
      width: 80
    },
    {
      title: '状态',
      key: 'status',
      render: (record: User) => (
        <span
          style={{
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            backgroundColor: record.status === 'active' ? '#52c41a20' :
                            record.status === 'suspended' ? '#faad1420' : '#ff4d4f20',
            color: record.status === 'active' ? '#52c41a' :
                  record.status === 'suspended' ? '#faad14' : '#ff4d4f'
          }}
        >
          {record.status === 'active' ? '活跃' :
           record.status === 'suspended' ? '暂停' : '禁用'}
        </span>
      ),
      width: 100
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => (
        <span>{new Date(text).toLocaleString()}</span>
      )
    },
    {title: '操作',
      key: 'actions',
      width: 220,
      render: (record: User) => (
        <div style={{ display: 'flex', justifyContent: 'center', gap: '8px' }}>
          <Button
            type="default"
            size="small"
            icon={<EyeOutlined size={16} />}
            onClick={() => navigate(`/users/${record.id}`)}
          >
            查看详情
          </Button>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined size={16} />}
            onClick={() => navigate(`/users/${record.id}/edit`)}
          >
            编辑
          </Button>
          <Button
            type="dashed"
            size="small"
            onClick={() => handleStatusUpdate(record.id, record.status === 'active' ? 'suspended' : 'active')}
          >
            {record.status === 'active' ? '暂停' : '启用'}
          </Button>
          <Popconfirm
            title={`确定要${record.status === 'banned' ? '解封' : '禁用'}该用户吗？`}
            onConfirm={() => handleStatusUpdate(record.id, record.status === 'banned' ? 'active' : 'banned')}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined size={16} />}
            >
              {record.status === 'banned' ? '解封' : '禁用'}
            </Button>
          </Popconfirm>
        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>
          <AlertCircleOutlined style={{ marginRight: '8px' }} />
          <span>获取数据失败: {error instanceof Error ? error.message : error.error || '未知错误'}</span>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px' }}>
      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          marginBottom: '24px'
        }}
      >
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', alignItems: 'center' }}>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <Search
              placeholder="搜索用户..."
              prefix={<SearchOutlined />}
              allowClear
              enterButton="搜索"
              size="middle"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <FilterOutlined style={{ marginRight: '8px', color: '#666' }} />
              <Select
                placeholder="筛选状态"
                size="middle"
                value={statusFilter}
                onChange={handleStatusChange}
                allowClear
              >
                <Option value="active">活跃</Option>
                <Option value="suspended">暂停</Option>
                <Option value="banned">禁用</Option>
              </Select>
            </div>
            <Button
              type="primary"
              size="middle"
              icon={<PlusOutlined />}
              onClick={() => navigate('/users/create')}
            >
              新增用户
            </Button>
          </div>
        </div>
      </Card>

      <Card
        bordered={false}
        style={{
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
        }}
      >
        <Table
          columns={columns}
          dataSource={data?.data?.items || []}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize,
            total: data?.data?.total || 0,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true
          }}
          onChange={handleTableChange}
          loading={isLoading}
          style={{ color: theme === 'dark' ? '#fff' : '#333' }}
        />
      </Card>
    </div>
  )
}

export default Users