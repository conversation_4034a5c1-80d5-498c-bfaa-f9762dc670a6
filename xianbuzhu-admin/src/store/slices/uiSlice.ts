import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: number
  duration?: number
}

interface BreadcrumbItem {
  title: string
  path?: string
}

interface UIState {
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
  loading: {
    global: boolean
    [key: string]: boolean
  }
  notifications: Notification[]
  breadcrumb: BreadcrumbItem[]
  pageTitle: string
  // 表格相关状态
  tableSettings: {
    [key: string]: {
      pageSize: number
      sortField?: string
      sortOrder?: 'ascend' | 'descend'
      filters?: Record<string, any>
    }
  }
}

const initialState: UIState = {
  sidebarCollapsed: localStorage.getItem('sidebar_collapsed') === 'true',
  theme: (localStorage.getItem('theme') as 'light' | 'dark') || 'light',
  loading: {
    global: false,
  },
  notifications: [],
  breadcrumb: [],
  pageTitle: '管理后台',
  tableSettings: {},
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed
      localStorage.setItem('sidebar_collapsed', state.sidebarCollapsed.toString())
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload
      localStorage.setItem('sidebar_collapsed', action.payload.toString())
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload
      localStorage.setItem('theme', action.payload)
    },
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loading[action.payload.key] = action.payload.loading
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload
    },
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        timestamp: Date.now(),
      }
      state.notifications.unshift(notification)
      // 最多保留20条通知
      if (state.notifications.length > 20) {
        state.notifications = state.notifications.slice(0, 20)
      }
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      )
    },
    clearNotifications: (state) => {
      state.notifications = []
    },
    setBreadcrumb: (state, action: PayloadAction<BreadcrumbItem[]>) => {
      state.breadcrumb = action.payload
    },
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload
      document.title = `${action.payload} - 社区物品置换管理后台`
    },
    // 表格设置相关
    setTableSettings: (state, action: PayloadAction<{ 
      tableKey: string
      settings: Partial<UIState['tableSettings'][string]>
    }>) => {
      const { tableKey, settings } = action.payload
      state.tableSettings[tableKey] = {
        ...state.tableSettings[tableKey],
        ...settings,
      }
    },
    resetTableSettings: (state, action: PayloadAction<string>) => {
      delete state.tableSettings[action.payload]
    },
  },
})

export const {
  toggleSidebar,
  setSidebarCollapsed,
  setTheme,
  setLoading,
  setGlobalLoading,
  addNotification,
  removeNotification,
  clearNotifications,
  setBreadcrumb,
  setPageTitle,
  setTableSettings,
  resetTableSettings,
} = uiSlice.actions

export default uiSlice.reducer

// Selectors
export const selectUI = (state: { ui: UIState }) => state.ui
export const selectSidebarCollapsed = (state: { ui: UIState }) => state.ui.sidebarCollapsed
export const selectTheme = (state: { ui: UIState }) => state.ui.theme
export const selectLoading = (state: { ui: UIState }) => state.ui.loading
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications
export const selectBreadcrumb = (state: { ui: UIState }) => state.ui.breadcrumb
export const selectPageTitle = (state: { ui: UIState }) => state.ui.pageTitle
export const selectTableSettings = (state: { ui: UIState }, tableKey: string) => 
  state.ui.tableSettings[tableKey] || { pageSize: 10 }