import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface Admin {
  id: string
  username: string
  name: string
  role: string
  permissions: string[]
  avatar?: string
  email?: string
}

interface AuthState {
  token: string | null
  admin: Admin | null
  isAuthenticated: boolean
  isLoading: boolean
  loginError: string | null
}

const initialState: AuthState = {
  token: localStorage.getItem('admin_token'),
  admin: null,
  isAuthenticated: false,
  isLoading: false,
  loginError: null,
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.isLoading = true
      state.loginError = null
    },
    loginSuccess: (state, action: PayloadAction<{ token: string; admin: Admin }>) => {
      state.isLoading = false
      state.token = action.payload.token
      state.admin = action.payload.admin
      state.isAuthenticated = true
      state.loginError = null
      localStorage.setItem('admin_token', action.payload.token)
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false
      state.token = null
      state.admin = null
      state.isAuthenticated = false
      state.loginError = action.payload
      localStorage.removeItem('admin_token')
    },
    logout: (state) => {
      state.token = null
      state.admin = null
      state.isAuthenticated = false
      state.loginError = null
      localStorage.removeItem('admin_token')
    },
    updateProfile: (state, action: PayloadAction<Partial<Admin>>) => {
      if (state.admin) {
        state.admin = { ...state.admin, ...action.payload }
      }
    },
    clearError: (state) => {
      state.loginError = null
    },
    // 初始化时检查token
    initializeAuth: (state) => {
      const token = localStorage.getItem('admin_token')
      if (token) {
        state.token = token
        // 这里可以添加验证token有效性的逻辑
      }
    },
  },
})

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  updateProfile,
  clearError,
  initializeAuth,
} = authSlice.actions

export default authSlice.reducer

// Selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated
export const selectCurrentAdmin = (state: { auth: AuthState }) => state.auth.admin
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.isLoading
export const selectLoginError = (state: { auth: AuthState }) => state.auth.loginError