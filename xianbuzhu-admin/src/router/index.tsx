import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Spin } from 'antd'
import { MainLayout } from '@components/Layout/MainLayout'
// 暂时注释掉LoginPage导入
// import LoginPage from '@pages/LoginPage.tsx'
import { ProtectedRoute } from '@components/Common/ProtectedRoute'

// 懒加载页面组件 - 暂时只保留Dashboard
const Dashboard = React.lazy(() => import('@pages/Dashboard'))
// 其他页面暂时注释掉
// const Users = React.lazy(() => import('@pages/Users'))
// const UserDetail = React.lazy(() => import('@pages/Users/<USER>'))
// const UserEdit = React.lazy(() => import('@pages/Users/<USER>'))
// const CreateUser = React.lazy(() => import('@pages/Users/<USER>'))
// const Items = React.lazy(() => import('@pages/Items'))
// const CreateItem = React.lazy(() => import('@pages/Items/CreateItem'))
// const EditItem = React.lazy(() => import('@pages/Items/EditItem'))
// const ItemDetail = React.lazy(() => import('@pages/Items/ItemDetail'))
// const Exchanges = React.lazy(() => import('@pages/Exchanges'))
// const Reports = React.lazy(() => import('@pages/Reports'))
// const Statistics = React.lazy(() => import('@pages/Statistics'))
// const Settings = React.lazy(() => import('@pages/Settings'))

const PageLoading = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '200px' 
  }}>
    <Spin size="large" />
  </div>
)

export const AppRouter: React.FC = () => {
  return (
    <Routes>
      {/* 登录页面 - 暂时注释掉 */}
      {/* <Route path="/login" element={<LoginPage />} /> */}
      
      {/* 主应用路由 */}
      <Route path="/" element={
        <ProtectedRoute>
          <MainLayout />
        </ProtectedRoute>
      }>
        {/* 默认重定向到仪表板 */}
        <Route index element={<Navigate to="/dashboard" replace />} />
        
        {/* 仪表板 */}
        <Route path="dashboard" element={
          <Suspense fallback={<PageLoading />}>
            <Dashboard />
          </Suspense>
        } />
        
        {/* 用户管理 - 暂时注释掉 */}
        {/* <Route path="users" element={
          <Suspense fallback={<PageLoading />}>
            <Users />
          </Suspense>
        } />
        <Route path="users/create" element={
          <Suspense fallback={<PageLoading />}>
            <CreateUser />
          </Suspense>
        } />
        <Route path="users/:id" element={
          <Suspense fallback={<PageLoading />}>
            <UserDetail />
          </Suspense>
        } />
        <Route path="users/:id/edit" element={
          <Suspense fallback={<PageLoading />}>
            <UserEdit />
          </Suspense>
        } /> */}

        {/* 其他路由暂时注释掉 */}
        {/* 物品管理 */}
        {/* <Route path="items" element={
          <Suspense fallback={<PageLoading />}>
            <Items />
          </Suspense>
        } />
        <Route path="items/create" element={
          <Suspense fallback={<PageLoading />}>
            <CreateItem />
          </Suspense>
        } />
        <Route path="items/:id" element={
          <Suspense fallback={<PageLoading />}>
            <ItemDetail />
          </Suspense>
        } />
        <Route path="items/:id/edit" element={
          <Suspense fallback={<PageLoading />}>
            <EditItem />
          </Suspense>
        } />

        交换管理
        <Route path="exchanges" element={
          <Suspense fallback={<PageLoading />}>
            <Exchanges />
          </Suspense>
        } />

        举报管理
        <Route path="reports" element={
          <Suspense fallback={<PageLoading />}>
            <Reports />
          </Suspense>
        } />

        数据统计
        <Route path="statistics" element={
          <Suspense fallback={<PageLoading />}>
            <Statistics />
          </Suspense>
        } />

        系统设置
        <Route path="settings" element={
          <Suspense fallback={<PageLoading />}>
            <Settings />
          </Suspense>
        } /> */}
      </Route>
      
      {/* 404页面 */}
      <Route path="*" element={
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <h1>404 - 页面不存在</h1>
          <p>您访问的页面不存在，请检查URL是否正确。</p>
        </div>
      } />
    </Routes>
  )
}