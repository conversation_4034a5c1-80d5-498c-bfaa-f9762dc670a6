import { createGlobalStyle } from 'styled-components'

export const GlobalStyle = createGlobalStyle`
  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f5f5f5;
  }

  * {
    box-sizing: border-box;
  }

  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: 600;
  }

  p {
    margin: 0;
  }

  a {
    color: #1890ff;
    text-decoration: none;
    transition: color 0.2s;
  }

  a:hover {
    color: #40a9ff;
    text-decoration: underline;
  }

  .site-form-item-icon {
    color: rgba(0, 0, 0, 0.25);
  }

  /* Ant Design 覆盖样式 */
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }

  .ant-btn-primary {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-btn-primary:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }

  .ant-table-thead > tr > th {
    background-color: #f7f7f7;
    font-weight: 600;
  }

  .ant-menu-item-selected {
    background-color: #e6f7ff !important;
    color: #1890ff !important;
  }

  .ant-menu-item-selected .ant-menu-item-icon {
    color: #1890ff !important;
  }
`