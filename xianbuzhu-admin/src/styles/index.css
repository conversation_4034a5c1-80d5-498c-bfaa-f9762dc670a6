/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100vh;
}

/* Ant Design 样式覆盖 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}

.ant-menu-dark {
  background: #001529;
}

.ant-layout-header {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px;
  }
}
