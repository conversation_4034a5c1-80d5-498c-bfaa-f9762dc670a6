import React, { useRef } from 'react'
import { LineChart, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts'

interface ChartProps {
  type: 'line' | 'bar' | 'pie'
  data: any[]
  xField: string
  yField: string | string[]
  seriesName?: string | string[]
  colors?: string[]
  theme: 'light' | 'dark'
}

export const Chart: React.FC<ChartProps> = ({ type, data, xField, yField, seriesName, colors, theme }) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const isMultiSeries = Array.isArray(yField)

  // 处理主题样式
  const textColor = theme === 'dark' ? '#fff' : '#333'
  const gridColor = theme === 'dark' ? '#333' : '#eee'
  const tooltipBackgroundColor = theme === 'dark' ? '#1e1e1e' : '#fff'
  const tooltipBorderColor = theme === 'dark' ? '#333' : '#ddd'

  // 格式化图表数据
  const formattedData = data.map(item => {
    if (isMultiSeries) {
      return item
    } else {
      return {
        [xField]: item[xField],
        value: item[yField as string]
      }
    }
  })

  if (type === 'line') {
    return (
      <ResponsiveContainer width="100%" height="100%" ref={chartRef}>
        <LineChart data={formattedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
          <XAxis dataKey={xField} stroke={textColor} />
          <YAxis stroke={textColor} />
          <Tooltip
            contentStyle={{
              backgroundColor: tooltipBackgroundColor,
              borderColor: tooltipBorderColor,
              color: textColor
            }}
          />
          {seriesName && <Legend />}
          {isMultiSeries ? (
            (yField as string[]).map((field, index) => (
              <Line
                key={index}
                type="monotone"
                dataKey={field}
                name={Array.isArray(seriesName) ? seriesName[index] : undefined}
                stroke={colors && colors[index] ? colors[index] : undefined}
                activeDot={{ r: 8 }}
              />
            ))
          ) : (
            <Line
              type="monotone"
              dataKey="value"
              name={typeof seriesName === 'string' ? seriesName : undefined}
              stroke={colors && colors[0] ? colors[0] : undefined}
              activeDot={{ r: 8 }}
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    )
  } else if (type === 'bar') {
    // 此处可以实现柱状图
    return (
      <div style={{ textAlign: 'center', padding: '40px', color: textColor }}>
        柱状图功能待实现
      </div>
    )
  } else if (type === 'pie') {
    // 此处可以实现饼图
    return (
      <div style={{ textAlign: 'center', padding: '40px', color: textColor }}>
        饼图功能待实现
      </div>
    )
  } else {
    return (
      <div style={{ textAlign: 'center', padding: '40px', color: textColor }}>
        不支持的图表类型
      </div>
    )
  }
}