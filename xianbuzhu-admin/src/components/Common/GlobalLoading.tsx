import React from 'react'
import { Spin } from 'antd'

interface GlobalLoadingProps {
  tip?: string
}

export const GlobalLoading: React.FC<GlobalLoadingProps> = ({ tip = '加载中...' }) => {
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        background: 'rgba(255, 255, 255, 0.8)',
        zIndex: 1000,
      }}
    >
      <Spin size="large" tip={tip} />
    </div>
  )
}