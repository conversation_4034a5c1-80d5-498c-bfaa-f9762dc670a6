import React from 'react'
import { Typography } from 'antd'

const { Title } = Typography

interface LogoProps {
  collapsed: boolean
}

export const Logo: React.FC<LogoProps> = ({ collapsed }) => {
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div
        style={{
          width: 32,
          height: 32,
          borderRadius: 8,
          background: '#1890ff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: collapsed ? 0 : 10,
        }}
      >
        <span style={{ color: 'white', fontWeight: 'bold' }}>XBZ</span>
      </div>
      {!collapsed && (
        <Title level={5} style={{ margin: 0, color: '#1890ff' }}>
          闲不猪交换平台
        </Title>
      )}
    </div>
  )
}