import React, { ReactNode } from 'react'
import { Navigate } from 'react-router-dom'
import { useAppSelector } from '@store/index'
import { selectIsAuthenticated } from '@store/slices/authSlice'

type ProtectedRouteProps = {
  children: ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const isAuthenticated = useAppSelector(selectIsAuthenticated)

  // 如果未认证，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  // 如果已认证，渲染子组件
  return children
}