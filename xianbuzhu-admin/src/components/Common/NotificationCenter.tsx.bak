import React, { useState, useEffect } from 'react'
import { Button, notification } from 'antd'
import { BellOutlined } from '@ant-design/icons'
import { useAppSelector } from '@store/index'
import { selectNotifications } from '@store/slices/uiSlice'
// 移除不存在的类型导入

// 通知类型定义
interface NotificationItem {
  id: string
  title: string
  message: string
  type: 'success' | 'info' | 'warning' | 'error'
  timestamp: number
  read: boolean
}

export const NotificationCenter: React.FC = () => {
  const notifications = useAppSelector<NotificationItem[]>(selectNotifications)
  const [visibleCount, setVisibleCount] = useState(0)

  // 监听通知变化并显示
  useEffect(() => {
    const newNotifications = notifications.filter(item => !item.read)
    setVisibleCount(newNotifications.length)

    newNotifications.forEach(item => {
      notification[item.type]({
        message: item.title,
        description: item.message,
        key: item.id,
      })
    })
  }, [notifications])

  const handleOpenNotifications = () => {
    // 这里可以打开通知中心面板
    notification.info({
      message: '通知中心',
      description: '通知中心功能正在开发中...',
    })
  }

  return (
    <Button
      type="text"
      icon={<BellOutlined />}
      onClick={handleOpenNotifications}
      style={{
        position: 'fixed',
        bottom: 24,
        right: 24,
        width: 56,
        height: 56,
        borderRadius: '50%',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        background: '#1890ff',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
      }}
    >
      {visibleCount > 0 && (
        <span
          style={{
            position: 'absolute',
            top: -4,
            right: -4,
            width: 20,
            height: 20,
            borderRadius: '50%',
            background: '#ff4d4f',
            color: 'white',
            fontSize: 12,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {visibleCount}
        </span>
      )}
    </Button>
  )
}