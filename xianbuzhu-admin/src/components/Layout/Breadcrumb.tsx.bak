import React from 'react'
import { Breadcrumb as AntdBreadcrumb, Tooltip } from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import { HomeOutlined } from '@ant-design/icons'
import type { BreadcrumbItemProps } from 'antd'

// 路由路径与面包屑名称的映射
const routeMap: Record<string, string> = {
  '/dashboard': '仪表板',
  '/users': '用户管理',
  '/users/create': '添加用户',
  '/users/:id': '编辑用户',
  '/items': '物品管理',
  '/items/create': '添加物品',
  '/items/:id': '编辑物品',
  '/exchanges': '交换管理',
  '/exchanges/:id': '交换详情',
  '/reports': '举报管理',
  '/statistics': '数据统计',
  '/settings': '系统设置',
}

export const Breadcrumb: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { pathname } = location

  // 生成面包屑项
  const generateBreadcrumbItems = () => {
    const items = [{
      key: 'home',
      title: <HomeOutlined />,
      href: '/dashboard',
    }]

    const pathSegments = pathname.split('/').filter(segment => segment)
    let currentPath = ''

    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      let displayName = routeMap[currentPath]

      // 如果没有精确匹配，尝试使用通配符匹配
      if (!displayName) {
        const wildcardKey = Object.keys(routeMap).find(key => {
          const keySegments = key.split('/').filter(k => k)
          if (keySegments.length !== pathSegments.length) return false
          return keySegments.every((k, i) => k === pathSegments[i] || k.startsWith(':'))
        })

        displayName = wildcardKey ? routeMap[wildcardKey] : segment
      }

      items.push({
        key: currentPath,
        title: (
          <Tooltip title={displayName}>
            <span>{displayName}</span>
          </Tooltip>
        ),
        href: currentPath,
      })
    })

    return items
  }

  const items = generateBreadcrumbItems()

  // 自定义面包屑项渲染
  const itemRender = (
    item: BreadcrumbItemProps,
    params: { separator: React.ReactNode },
  ) => {
    return (
      <span
        style={{ cursor: 'pointer' }}
        onClick={() => item.href && navigate(item.href)}
      >
        {item.title}
      </span>
    );
  };

  return (
    <AntdBreadcrumb
      items={items}
      style={{
        padding: '12px 16px',
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
      }}
      separator="/"
      itemRender={itemRender}
    />
  )
}