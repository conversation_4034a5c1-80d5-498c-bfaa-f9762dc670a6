import React, { useEffect } from 'react'
import { Outlet, useLocation } from 'react-router-dom'
import { Layout } from 'antd'
import { useAppSelector } from '@store/index'
import { Sidebar } from './Sidebar'
import { PageHeader } from './Header'
// 暂时注释掉有问题的组件导入
// import { Breadcrumb } from './Breadcrumb'
import { GlobalLoading } from '@components/Common/GlobalLoading'
// import { NotificationCenter } from '@components/Common/NotificationCenter'
import { selectSidebarCollapsed, selectLoading } from '@store/slices/uiSlice'

const { Content } = Layout

export const MainLayout: React.FC = () => {
  const location = useLocation()
  const sidebarCollapsed = useAppSelector(selectSidebarCollapsed)
  const loading = useAppSelector(selectLoading)

  // 页面切换时滚动到顶部
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [location.pathname])

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sidebar />
      
      {/* 主内容区 */}
      <Layout
        style={{
          marginLeft: sidebarCollapsed ? 80 : 200,
          transition: 'margin-left 0.2s',
        }}
      >
        {/* 顶部导航 */}
        <PageHeader />
        
        {/* 面包屑导航 - 暂时注释掉 */}
        {/* <Breadcrumb /> */}
        
        {/* 页面内容 */}
        <Content
          style={{
            margin: '16px',
            padding: '24px',
            background: '#fff',
            borderRadius: '8px',
            minHeight: 'calc(100vh - 112px)',
            position: 'relative',
            overflow: 'auto',
          }}
        >
          {loading.global && <GlobalLoading />}
          <Outlet />
        </Content>
      </Layout>
      
      {/* 通知中心 - 暂时注释掉 */}
      {/* <NotificationCenter /> */}
    </Layout>
  )
}