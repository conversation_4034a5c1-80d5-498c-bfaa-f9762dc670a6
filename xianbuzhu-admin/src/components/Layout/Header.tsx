import React from 'react'
import { Layout, Button, Dropdown, <PERSON>u, Avatar, Badge } from 'antd'
import { useAppDispatch } from '@store/index'
import { toggleSidebar } from '@store/slices/uiSlice'
import { MenuUnfoldOutlined, BellOutlined, UserOutlined, LogoutOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useLogoutMutation } from '@services/api'
import { useAppSelector } from '@store/index'
import { selectIsAuthenticated } from '@store/slices/authSlice'

const { Header: AntHeader } = Layout

export const PageHeader: React.FC = () => {
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const [logout] = useLogoutMutation()
  const isAuthenticated = useAppSelector(selectIsAuthenticated)

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar())
  }

  const handleLogout = async () => {
    try {
      await logout().unwrap()
      navigate('/login')
    } catch (err: any) {
      console.error('退出登录失败:', err)
    }
  }

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        个人资料
      </Menu.Item>
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        退出登录
      </Menu.Item>
    </Menu>
  )

  return (
    <AntHeader
      style={{
        padding: '0 16px',
        background: '#fff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
        position: 'sticky',
        top: 0,
        zIndex: 100,
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Button
          type="text"
          icon={<MenuUnfoldOutlined />}
          onClick={handleToggleSidebar}
          style={{ marginRight: 16 }}
        />
      </div>

      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Badge count={5} style={{ marginRight: 16 }}>
          <Button type="text" icon={<BellOutlined />} />
        </Badge>

        <Dropdown overlay={userMenu} placement="bottomRight">
          <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <Avatar icon={<UserOutlined />} style={{ marginRight: 8 }} />
            <span style={{ fontWeight: 500 }}>{isAuthenticated ? '管理员' : '未登录'}</span>
          </div>
        </Dropdown>
      </div>
    </AntHeader>
  )
}