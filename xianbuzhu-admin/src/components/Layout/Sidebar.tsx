import React from 'react'
import { Layout, Menu } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '@store/index'
import {
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  SwapOutlined,
  WarningOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons'
import { selectSidebarCollapsed, toggleSidebar } from '@store/slices/uiSlice'
import { Logo } from '@components/Common/Logo'

const { Sider } = Layout

const menuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: '/users',
    icon: <UserOutlined />,
    label: '用户管理',
  },
  {
    key: '/items',
    icon: <ShoppingOutlined />,
    label: '物品管理',
  },
  {
    key: '/exchanges',
    icon: <SwapOutlined />,
    label: '交换管理',
  },
  {
    key: '/reports',
    icon: <WarningOutlined />,
    label: '举报管理',
  },
  {
    key: '/statistics',
    icon: <BarChartOutlined />,
    label: '数据统计',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '系统设置',
  },
]

export const Sidebar: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useAppDispatch()
  const sidebarCollapsed = useAppSelector(selectSidebarCollapsed)

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  const handleToggle = () => {
    dispatch(toggleSidebar())
  }

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={sidebarCollapsed}
      width={200}
      style={{
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 1000,
        boxShadow: '2px 0 8px rgba(0, 0, 0, 0.15)',
      }}
    >
      {/* Logo区域 */}
      <div
        style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0',
          background: '#fff',
        }}
      >
        <Logo collapsed={sidebarCollapsed} />
      </div>

      {/* 菜单区域 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{
          height: 'calc(100vh - 128px)',
          borderRight: 0,
        }}
      />

      {/* 折叠按钮 */}
      <div
        style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderTop: '1px solid #f0f0f0',
          background: '#001529',
          cursor: 'pointer',
          color: '#fff',
          transition: 'all 0.2s',
        }}
        onClick={handleToggle}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = '#1890ff'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = '#001529'
        }}
      >
        {sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
      </div>
    </Sider>
  )
}