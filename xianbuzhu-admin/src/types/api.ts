// 通用API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: {
    type: string
    code: string
    message: string
    details?: any
  }
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户相关类型
export interface User {
  id: string
  phone: string
  name: string
  avatar?: string
  communityId: string
  communityName?: string
  trustLevel: TrustLevel
  points: number
  isVerified: boolean
  isActive: boolean
  status: 'active' | 'suspended' | 'banned'
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
}

export interface TrustLevel {
  level: number
  score: number
  exchangeCount: number
  successRate: number
  averageRating: number
}

// 物品相关类型
export interface Item {
  id: string
  title: string
  description: string
  category: ItemCategory
  images: string[]
  condition: ItemCondition
  estimatedValue: number
  desiredItems: string[]
  status: ItemStatus
  publisherId: string
  publisherName?: string
  communityId: string
  communityName?: string
  location: Location
  viewCount: number
  favoriteCount: number
  createdAt: string
  updatedAt: string
}

export interface ItemCategory {
  id: string
  name: string
  parentId?: string
  icon?: string
}

export enum ItemCondition {
  NEW = 'new',
  LIKE_NEW = 'like_new',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

export enum ItemStatus {
  AVAILABLE = 'available',
  EXCHANGING = 'exchanging',
  EXCHANGED = 'exchanged',
  REMOVED = 'removed',
  PENDING_REVIEW = 'pending_review',
  REJECTED = 'rejected'
}

export interface Location {
  latitude: number
  longitude: number
  address?: string
}

// 交换相关类型
export interface Exchange {
  id: string
  requesterId: string
  requesterName?: string
  targetItemId: string
  targetItem?: Item
  offeredItemId: string
  offeredItem?: Item
  status: ExchangeStatus
  message: string
  confirmedAt?: string
  completedAt?: string
  createdAt: string
  updatedAt: string
  rating?: ExchangeRating
}

export enum ExchangeStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  COMPLETED = 'completed',
  DISPUTED = 'disputed',
  CANCELLED = 'cancelled'
}

export interface ExchangeRating {
  fromRequester?: Rating
  fromOwner?: Rating
}

export interface Rating {
  score: number
  comment?: string
  createdAt: string
}

// 举报相关类型
export interface Report {
  id: string
  reporterId: string
  reporterName?: string
  targetType: 'user' | 'item' | 'exchange'
  targetId: string
  targetInfo?: any
  reason: string
  description?: string
  status: ReportStatus
  handledBy?: string
  handledAt?: string
  handlerNote?: string
  createdAt: string
  updatedAt: string
}

export enum ReportStatus {
  PENDING = 'pending',
  REVIEWING = 'reviewing',
  RESOLVED = 'resolved',
  REJECTED = 'rejected'
}

// 仪表板统计类型
export interface DashboardStats {
  users: {
    total: number
    active: number
    newToday: number
    growth: number
  }
  items: {
    total: number
    available: number
    newToday: number
    growth: number
  }
  exchanges: {
    total: number
    completed: number
    todayCompleted: number
    successRate: number
  }
  reports: {
    total: number
    pending: number
    resolved: number
    todayNew: number
  }
}

// 图表数据类型
export interface ChartData {
  date: string
  value: number
  category?: string
  type?: string
}

// 筛选器类型

// 统计数据响应类型
export interface StatisticsResponse {
  totalUsers: number
  totalItems: number
  totalExchanges: number
  totalReports: number
  userDistribution: Array<{ name: string; value: number }>
  exchangeDistribution: Array<{ name: string; value: number }>
  activeUsers: ChartData[]
  newUsers: ChartData[]
}
export interface UserFilters {
  search?: string
  status?: string
  communityId?: string
  trustLevel?: number
  dateRange?: [string, string]
}

export interface ItemFilters {
  search?: string
  status?: string
  category?: string
  communityId?: string
  condition?: string
  dateRange?: [string, string]
}

export interface ExchangeFilters {
  status?: string
  dateRange?: [string, string]
  communityId?: string
}

export interface ReportFilters {
  status?: string
  type?: string
  dateRange?: [string, string]
}

// 导出相关类型
export interface ExportOptions {
  type: 'users' | 'items' | 'exchanges' | 'reports'
  format: 'excel' | 'csv'
  filters?: any
  dateRange?: [string, string]
  fields?: string[]
}