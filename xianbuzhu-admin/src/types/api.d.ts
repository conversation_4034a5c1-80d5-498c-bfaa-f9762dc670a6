/**
 * API类型定义
 */

declare module '@types/api' {
  // 用户相关类型
  export interface User {
    id: string
    username: string
    email: string
    avatar: string
    role: 'admin' | 'user'
    createdAt: string
    updatedAt: string
  }

  // 物品相关类型
  export interface Item {
    id: string
    title: string
    description: string
    price: number
    images: string[]
    category: string
    status: 'available' | 'sold' | 'reserved'
    sellerId: string
    createdAt: string
    updatedAt: string
  }

  // 交易相关类型
  export interface Transaction {
    id: string
    itemId: string
    buyerId: string
    sellerId: string
    amount: number
    status: 'pending' | 'completed' | 'cancelled'
    createdAt: string
    updatedAt: string
  }

  // API响应类型
  export interface ApiResponse<T> {
    success: boolean
    data: T
    message?: string
  }

  // API错误类型
  export interface ApiError {
    success: boolean
    error: {
      code: string
      message: string
      details?: any
    }
  }
}