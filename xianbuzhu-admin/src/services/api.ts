import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { RootState } from '@store/index'
import type {
  ApiResponse,
  User,
  Item,
} from '@/types'

// 补充缺失的类型定义
interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// 从@/types/api导入Exchange类型以保持一致性
import type { Exchange } from '@/types/api'

// 移除重复的Exchange接口定义

interface DashboardStats {
  totalUsers: number
  totalItems: number
  totalExchanges: number
  pendingExchanges: number
  completedExchanges: number
  cancellationRate: number
  averageRating: number
}

interface ChartData {
  label: string
  value: number
  color?: string
}

// 基础查询配置
const baseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_API_BASE_URL || '/api/admin',
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as RootState).auth.token
    if (token) {
      headers.set('authorization', `Bearer ${token}`)
    }
    headers.set('content-type', 'application/json')
    return headers
  },
})

// 带错误处理的查询
const baseQueryWithReauth = async (args: any, api: any, extraOptions: any) => {
  const result = await baseQuery(args, api, extraOptions)
  
  if (result.error && result.error.status === 401) {
    // Token过期，清除认证状态
    api.dispatch({ type: 'auth/logout' })
  }
  
  return result
}

export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['User', 'Item', 'Exchange', 'Report', 'Statistics'],
  endpoints: (builder) => ({
    // 认证相关
    login: builder.mutation<
      ApiResponse<{ token: string; admin: any }>,
      { username: string; password: string }
    >({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
    }),
    
    logout: builder.mutation<ApiResponse, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
    }),
    
    // 仪表板数据
    getDashboardStats: builder.query<ApiResponse<DashboardStats>, void>({
      query: () => '/dashboard/stats',
      providesTags: ['Statistics'],
    }),
    
    getDashboardCharts: builder.query<
      ApiResponse<ChartData[]>,
      { type: string; period: string }
    >({
      query: (params) => ({
        url: '/dashboard/charts',
        params,
      }),
      providesTags: ['Statistics'],
    }),
    
    // 用户管理
    deleteUser: builder.mutation<ApiResponse, string>({
      query: (id) => ({
        url: `/users/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['User'],
    }),

    // 创建用户
    createUser: builder.mutation<ApiResponse, any>({
      query: (data) => ({
        url: '/users',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    // 更新用户
    updateUser: builder.mutation<ApiResponse, {userId: string; data: any}>({
      query: ({userId, data}) => ({
        url: `/users/${userId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    getUsers: builder.query<
      ApiResponse<PaginatedResponse<User>>,
      { page?: number; pageSize?: number; search?: string; status?: string }
    >({
      query: (params) => ({
        url: '/users',
        params,
      }),
      providesTags: ['User'],
    }),
    
    getUserById: builder.query<ApiResponse<User>, string>({
      query: (id) => `/users/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'User', id }],
    }),
    
    updateUserStatus: builder.mutation<
      ApiResponse,
      { userId: string; status: string; reason?: string }
    >({
      query: ({ userId, ...body }) => ({
        url: `/users/${userId}/status`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['User'],
    }),
    
    banUser: builder.mutation<
      ApiResponse,
      { userId: string; reason: string; duration?: number }
    >({
      query: ({ userId, ...body }) => ({
        url: `/users/${userId}/ban`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['User'],
    }),
    
    // 物品管理
    getItems: builder.query<
      ApiResponse<PaginatedResponse<Item>>,
      { page?: number; pageSize?: number; status?: string; search?: string; category?: string }
    >({
      query: (params) => ({
        url: '/items',
        params,
      }),
      providesTags: ['Item'],
    }),
    
    getItemById: builder.query<ApiResponse<Item>, string>({
      query: (id) => `/items/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'Item', id }],
    }),
    
    updateItemStatus: builder.mutation<
      ApiResponse,
      { itemId: string; status: string; reason?: string }
    >({
      query: ({ itemId, ...body }) => ({
        url: `/items/${itemId}/status`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['Item'],
    }),
    
    // 更新物品
    updateItem: builder.mutation<
      ApiResponse,
      { itemId: string; data: FormData }
    >({
      query: ({ itemId, data }) => ({
        url: `/items/${itemId}`,
        method: 'PUT',
        body: data,
        formData: true,
      }),
      invalidatesTags: ['Item'],
    }),
    
    deleteItem: builder.mutation<ApiResponse, string>({
      query: (id) => ({
        url: `/items/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Item'],
    }),
    
    createItem: builder.mutation<ApiResponse, FormData>({
      query: (data) => ({
        url: '/items',
        method: 'POST',
        body: data,
        formData: true,
      }),
      invalidatesTags: ['Item'],
    }),
    
    // 交换管理
    getExchanges: builder.query<
      ApiResponse<PaginatedResponse<Exchange>>,
      { page?: number; pageSize?: number; status?: string; search?: string }
    >({
      query: (params) => ({
        url: '/exchanges',
        params,
      }),
      providesTags: ['Exchange'],
    }),

    // 获取单个交换记录详情
    getExchangeById: builder.query<ApiResponse<Exchange>, string>({
      query: (id) => `/exchanges/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'Exchange', id }],
    }),
    
    // 举报管理
    getReports: builder.query<
      ApiResponse<PaginatedResponse<any>>,
      { page?: number; pageSize?: number; status?: string; type?: string }
    >({
      query: (params) => ({
        url: '/reports',
        params,
      }),
      providesTags: ['Report'],
    }),
    
    handleReport: builder.mutation<
      ApiResponse,
      { reportId: string; action: string; reason?: string }
    >({
      query: ({ reportId, ...body }) => ({
        url: `/reports/${reportId}/handle`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['Report'],
    }),
    
    // 统计数据
    getStatistics: builder.query<
      ApiResponse<any>,
      { type: string; period: string; startDate?: string; endDate?: string }
    >({
      query: (params) => ({
        url: '/statistics/overview',
        params,
      }),
      providesTags: ['Statistics'],
    }),
    
    exportStatistics: builder.mutation<
      Blob,
      { type: string; format: string; startDate?: string; endDate?: string }
    >({
      query: (params) => ({
        url: '/statistics/export',
        method: 'POST',
        body: params,
        responseHandler: (response: Response) => response.blob(),
      }),
    }),
    
    // 更新交换状态
    updateExchangeStatus: builder.mutation<
      ApiResponse,
      { id: string; status: string; reason?: string }
    >({
      query: ({ id, ...body }) => ({
        url: `/exchanges/${id}/status`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['Exchange'],
    }),
  }),
})

// 导出hooks
export const {
  useLoginMutation,
  useLogoutMutation,
  useGetDashboardStatsQuery,
  useGetDashboardChartsQuery,
  useGetUsersQuery,
  useGetUserByIdQuery,
  useUpdateUserStatusMutation,
  useBanUserMutation,
  useCreateUserMutation,
  useUpdateUserMutation,
  useGetItemsQuery,
  useGetItemByIdQuery,
  useUpdateItemStatusMutation,
  useUpdateItemMutation,
  useDeleteItemMutation,
  useGetExchangesQuery,
  useGetExchangeByIdQuery,
  useUpdateExchangeStatusMutation,
  useCreateItemMutation,
  useGetReportsQuery,
  useHandleReportMutation,
  useGetStatisticsQuery,
  useExportStatisticsMutation,
} = api