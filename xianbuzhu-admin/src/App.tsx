// React导入在JSX中隐式使用
import { BrowserRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { store } from '@store/index'
import { AppRouter } from './router'
import { ErrorBoundary } from '@components/Common/ErrorBoundary'
import { GlobalStyle } from '@styles/GlobalStyle'
import 'dayjs/locale/zh-cn'

function App() {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <ConfigProvider
          locale={zhCN}
          theme={{
            token: {
              colorPrimary: '#1890ff',
              borderRadius: 6,
              colorBgContainer: '#ffffff',
            },
            components: {
              Layout: {
                siderBg: '#001529',
                triggerBg: '#002140',
              },
              Menu: {
                darkItemBg: '#001529',
                darkSubMenuItemBg: '#000c17',
                darkItemSelectedBg: '#1890ff',
              },
            },
          }}
        >
          <BrowserRouter>
            <GlobalStyle />
            <AppRouter />
          </BrowserRouter>
        </ConfigProvider>
      </Provider>
    </ErrorBoundary>
  )
}

export default App