import{u as R,a as v,s as w,b as I,c as C,d as k,j as e}from"./index-CSkGGLAO.js";import{r as S}from"./vendor-BM8LEypB.js";import{c as $,d as z,e as D,m as p,S as E,n as g,p as u,q as j,s as A,t as B,u as T}from"./antd-sY6o5fYU.js";import"./redux-Bzo8j05z.js";const N=()=>{var l,n,a,c,d,x,f,h;const o=R(),r=v(w),{data:s,isLoading:y,error:i}=I();S.useEffect(()=>{o(C([{title:"首页",path:"/dashboard"}])),o(k("仪表板"))},[o]);const m=[{title:"总用户数",icon:e.jsx($,{}),value:((l=s==null?void 0:s.users)==null?void 0:l.total)||0,growth:((n=s==null?void 0:s.users)==null?void 0:n.growth)||0,color:"#1890ff"},{title:"总物品数",icon:e.jsx(z,{}),value:((a=s==null?void 0:s.items)==null?void 0:a.total)||0,growth:((c=s==null?void 0:s.items)==null?void 0:c.growth)||0,color:"#52c41a"},{title:"总交换数",icon:e.jsx(D,{}),value:((d=s==null?void 0:s.exchanges)==null?void 0:d.total)||0,growth:((x=s==null?void 0:s.exchanges)==null?void 0:x.successRate)||0,color:"#faad14"},{title:"待处理举报",icon:e.jsx(p,{}),value:((f=s==null?void 0:s.reports)==null?void 0:f.pending)||0,growth:((h=s==null?void 0:s.reports)==null?void 0:h.todayNew)||0,color:"#ff4d4f"}];return y?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px"},children:e.jsx(E,{size:"large"})}):i?e.jsx("div",{style:{padding:"24px",backgroundColor:"#fff",borderRadius:"8px"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",color:"#ff4d4f"},children:[e.jsx(p,{style:{marginRight:"8px"}}),e.jsxs("span",{children:["获取数据失败: ",(i==null?void 0:i.message)||"未知错误"]})]})}):e.jsxs("div",{style:{padding:"24px",backgroundColor:r==="dark"?"#0f0f0f":"#f5f5f5",borderRadius:"8px"},children:[e.jsx(g,{gutter:[16,16],style:{marginBottom:"24px"},children:m.map((t,b)=>e.jsx(u,{xs:24,sm:12,md:6,children:e.jsx(j,{bordered:!1,style:{backgroundColor:r==="dark"?"#1e1e1e":"#fff",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.08)"},children:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("div",{style:{width:"40px",height:"40px",borderRadius:"50%",backgroundColor:`${t.color}20`,display:"flex",justifyContent:"center",alignItems:"center",marginRight:"16px"},children:e.jsx("span",{style:{color:t.color},children:t.icon})}),e.jsxs("div",{children:[e.jsx("p",{style:{color:r==="dark"?"#999":"#666",margin:0},children:t.title}),e.jsx(A,{value:t.value,style:{color:r==="dark"?"#fff":"#333"},valueStyle:{fontSize:"24px",fontWeight:"bold"}})]})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",color:t.growth>=0?"#52c41a":"#ff4d4f"},children:[t.growth>=0?e.jsx(B,{size:16,style:{marginRight:"4px"}}):e.jsx(T,{size:16,style:{marginRight:"4px"}}),e.jsxs("span",{children:[Math.abs(t.growth),"%"]})]})]})})},b))}),e.jsx(g,{gutter:[16,16],children:e.jsx(u,{xs:24,md:24,children:e.jsx(j,{title:"平台数据趋势",bordered:!1,style:{backgroundColor:r==="dark"?"#1e1e1e":"#fff",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.08)"},children:e.jsx("div",{style:{height:"300px"},children:e.jsx("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center",background:"#f5f5f5",borderRadius:"8px"},children:e.jsx("span",{style:{color:"#999"},children:"图表组件开发中..."})})})})})})]})};export{N as default};
//# sourceMappingURL=index-CcEm1oee.js.map
