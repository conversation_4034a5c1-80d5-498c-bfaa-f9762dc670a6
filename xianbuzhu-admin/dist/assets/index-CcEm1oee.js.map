{"version": 3, "file": "index-CcEm1oee.js", "sources": ["../../src/pages/Dashboard/index.tsx"], "sourcesContent": ["import React, { useEffect } from 'react'\nimport { Card, Row, Col, Statistic, Spin } from 'antd'\nimport { ArrowUpOutlined, ArrowDownOutlined, UserOutlined, ShoppingOutlined, SwapOutlined, ExclamationCircleOutlined } from '@ant-design/icons'\nimport { useAppDispatch, useAppSelector } from '@store/index'\nimport { setBreadcrumb, setPageTitle } from '@store/slices/uiSlice'\nimport { useGetDashboardStatsQuery } from '@services/api'\n// 暂时注释掉Chart组件导入\n// import Chart from '../../components/Charts/Chart'\nimport { selectTheme } from '@store/slices/uiSlice'\n\nconst Dashboard: React.FC = () => {\n  const dispatch = useAppDispatch()\n  const theme = useAppSelector(selectTheme)\n  const { data, isLoading, error } = useGetDashboardStatsQuery()\n\n  // 设置面包屑和页面标题\n  useEffect(() => {\n    dispatch(setBreadcrumb([{ title: '首页', path: '/dashboard' }]))\n    dispatch(setPageTitle('仪表板'))\n  }, [dispatch])\n\n// 图表数据\nconst chartData: any[] = [\n    { date: '1月', users: 120, items: 80, exchanges: 40 },\n    { date: '2月', users: 140, items: 100, exchanges: 55 },\n    { date: '3月', users: 180, items: 130, exchanges: 75 },\n    { date: '4月', users: 220, items: 150, exchanges: 90 },\n    { date: '5月', users: 250, items: 180, exchanges: 110 },\n    { date: '6月', users: 300, items: 220, exchanges: 140 },\n  ]\n\n  // 统计卡片配置\n  const statsCards = [\n    {\n      title: '总用户数',\n      icon: <UserOutlined />,\n      value: (data as any)?.users?.total || 0,\n      growth: (data as any)?.users?.growth || 0,\n      color: '#1890ff'\n    },\n    {\n      title: '总物品数',\n      icon: <ShoppingOutlined />,\n      value: (data as any)?.items?.total || 0,\n      growth: (data as any)?.items?.growth || 0,\n      color: '#52c41a'\n    },\n    {\n      title: '总交换数',\n      icon: <SwapOutlined />,\n      value: (data as any)?.exchanges?.total || 0,\n      growth: (data as any)?.exchanges?.successRate || 0,\n      color: '#faad14'\n    },\n    {\n      title: '待处理举报',\n      icon: <ExclamationCircleOutlined />,\n      value: (data as any)?.reports?.pending || 0,\n      growth: (data as any)?.reports?.todayNew || 0,\n      color: '#ff4d4f'\n    }\n  ]\n\n  if (isLoading) {\n    return (\n      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>\n        <Spin size=\"large\" />\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div style={{ padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}>\n        <div style={{ display: 'flex', alignItems: 'center', color: '#ff4d4f' }}>\n          <ExclamationCircleOutlined style={{ marginRight: '8px' }} />\n          <span>获取数据失败: {(error as any)?.message || '未知错误'}</span>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div style={{ padding: '24px', backgroundColor: theme === 'dark' ? '#0f0f0f' : '#f5f5f5', borderRadius: '8px' }}>\n      {/* 统计卡片区域 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        {statsCards.map((card, index) => (\n          <Col xs={24} sm={12} md={6} key={index}>\n            <Card\n              bordered={false}\n              style={{\n                backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',\n                borderRadius: '8px',\n                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'\n              }}\n            >\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                <div style={{ display: 'flex', alignItems: 'center' }}>\n                  <div\n                    style={{\n                      width: '40px',\n                      height: '40px',\n                      borderRadius: '50%',\n                      backgroundColor: `${card.color}20`,\n                      display: 'flex',\n                      justifyContent: 'center',\n                      alignItems: 'center',\n                      marginRight: '16px'\n                    }}\n                  >\n                    <span style={{ color: card.color }}>{card.icon}</span>\n                  </div>\n                  <div>\n                    <p style={{ color: theme === 'dark' ? '#999' : '#666', margin: 0 }}>{card.title}</p>\n                    <Statistic\n                      value={card.value}\n                      style={{ color: theme === 'dark' ? '#fff' : '#333' }}\n                      valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}\n                    />\n                  </div>\n                </div>\n                <div\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    color: card.growth >= 0 ? '#52c41a' : '#ff4d4f'\n                  }}\n                >\n                  {card.growth >= 0 ? (\n                    <ArrowUpOutlined size={16} style={{ marginRight: '4px' }} />\n                  ) : (\n                    <ArrowDownOutlined size={16} style={{ marginRight: '4px' }} />\n                  )}\n                  <span>{Math.abs(card.growth)}%</span>\n                </div>\n              </div>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n\n      {/* 图表区域 */}\n      <Row gutter={[16, 16]}>\n        <Col xs={24} md={24}>\n          <Card\n            title=\"平台数据趋势\"\n            bordered={false}\n            style={{\n              backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',\n              borderRadius: '8px',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'\n            }}\n          >\n            <div style={{ height: '300px' }}>\n              {/* 暂时注释掉Chart组件 */}\n              <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#f5f5f5', borderRadius: '8px' }}>\n                <span style={{ color: '#999' }}>图表组件开发中...</span>\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  )\n}\n\nexport default Dashboard"], "names": ["Dashboard", "dispatch", "useAppDispatch", "theme", "useAppSelector", "selectTheme", "data", "isLoading", "error", "useGetDashboardStatsQuery", "useEffect", "setBreadcrumb", "setPageTitle", "statsCards", "UserOutlined", "_a", "_b", "ShoppingOutlined", "_c", "_d", "SwapOutlined", "_e", "_f", "ExclamationCircleOutlined", "_g", "_h", "Spin", "jsx", "jsxs", "Row", "card", "index", "Col", "Card", "Statistic", "ArrowUpOutlined", "ArrowDownOutlined"], "mappings": "oQAUA,MAAMA,EAAsB,IAAM,qBAChC,MAAMC,EAAWC,EAAA,EACXC,EAAQC,EAAeC,CAAW,EAClC,CAAE,KAAAC,EAAM,UAAAC,EAAW,MAAAC,CAAA,EAAUC,EAAA,EAGnCC,EAAAA,UAAU,IAAM,CACdT,EAASU,EAAc,CAAC,CAAE,MAAO,KAAM,KAAM,YAAA,CAAc,CAAC,CAAC,EAC7DV,EAASW,EAAa,KAAK,CAAC,CAC9B,EAAG,CAACX,CAAQ,CAAC,EAab,MAAMY,EAAa,CACjB,CACE,MAAO,OACP,WAAOC,EAAA,EAAa,EACpB,QAAQC,EAAAT,GAAA,YAAAA,EAAc,QAAd,YAAAS,EAAqB,QAAS,EACtC,SAASC,EAAAV,GAAA,YAAAA,EAAc,QAAd,YAAAU,EAAqB,SAAU,EACxC,MAAO,SAAA,EAET,CACE,MAAO,OACP,WAAOC,EAAA,EAAiB,EACxB,QAAQC,EAAAZ,GAAA,YAAAA,EAAc,QAAd,YAAAY,EAAqB,QAAS,EACtC,SAASC,EAAAb,GAAA,YAAAA,EAAc,QAAd,YAAAa,EAAqB,SAAU,EACxC,MAAO,SAAA,EAET,CACE,MAAO,OACP,WAAOC,EAAA,EAAa,EACpB,QAAQC,EAAAf,GAAA,YAAAA,EAAc,YAAd,YAAAe,EAAyB,QAAS,EAC1C,SAASC,EAAAhB,GAAA,YAAAA,EAAc,YAAd,YAAAgB,EAAyB,cAAe,EACjD,MAAO,SAAA,EAET,CACE,MAAO,QACP,WAAOC,EAAA,EAA0B,EACjC,QAAQC,EAAAlB,GAAA,YAAAA,EAAc,UAAd,YAAAkB,EAAuB,UAAW,EAC1C,SAASC,EAAAnB,GAAA,YAAAA,EAAc,UAAd,YAAAmB,EAAuB,WAAY,EAC5C,MAAO,SAAA,CACT,EAGF,OAAIlB,QAEC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,eAAgB,SAAU,WAAY,SAAU,OAAQ,SACrF,eAACmB,EAAA,CAAK,KAAK,QAAQ,EACrB,EAIAlB,EAEAmB,MAAC,OAAI,MAAO,CAAE,QAAS,OAAQ,gBAAiB,OAAQ,aAAc,KAAA,EACpE,SAAAC,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,WAAY,SAAU,MAAO,SAAA,EAC1D,SAAA,CAAAD,EAAAA,IAACJ,EAAA,CAA0B,MAAO,CAAE,YAAa,OAAS,SACzD,OAAA,CAAK,SAAA,CAAA,YAAUf,GAAA,YAAAA,EAAe,UAAW,MAAA,CAAA,CAAO,CAAA,CAAA,CACnD,CAAA,CACF,EAKFoB,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,gBAAiBzB,IAAU,OAAS,UAAY,UAAW,aAAc,OAEtG,SAAA,CAAAwB,EAAAA,IAACE,EAAA,CAAI,OAAQ,CAAC,GAAI,EAAE,EAAG,MAAO,CAAE,aAAc,MAAA,EAC3C,WAAW,IAAI,CAACC,EAAMC,IACrBJ,MAACK,EAAA,CAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EACvB,SAAAL,EAAAA,IAACM,EAAA,CACC,SAAU,GACV,MAAO,CACL,gBAAiB9B,IAAU,OAAS,UAAY,OAChD,aAAc,MACd,UAAW,+BAAA,EAGb,SAAAyB,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,eAAgB,gBAAiB,WAAY,QAAA,EAC1E,SAAA,CAAAA,OAAC,OAAI,MAAO,CAAE,QAAS,OAAQ,WAAY,UACzC,SAAA,CAAAD,EAAAA,IAAC,MAAA,CACC,MAAO,CACL,MAAO,OACP,OAAQ,OACR,aAAc,MACd,gBAAiB,GAAGG,EAAK,KAAK,KAC9B,QAAS,OACT,eAAgB,SAChB,WAAY,SACZ,YAAa,MAAA,EAGf,SAAAH,EAAAA,IAAC,QAAK,MAAO,CAAE,MAAOG,EAAK,KAAA,EAAU,SAAAA,EAAK,IAAA,CAAK,CAAA,CAAA,SAEhD,MAAA,CACC,SAAA,CAAAH,EAAAA,IAAC,IAAA,CAAE,MAAO,CAAE,MAAOxB,IAAU,OAAS,OAAS,OAAQ,OAAQ,CAAA,EAAM,SAAA2B,EAAK,MAAM,EAChFH,EAAAA,IAACO,EAAA,CACC,MAAOJ,EAAK,MACZ,MAAO,CAAE,MAAO3B,IAAU,OAAS,OAAS,MAAA,EAC5C,WAAY,CAAE,SAAU,OAAQ,WAAY,MAAA,CAAO,CAAA,CACrD,CAAA,CACF,CAAA,EACF,EACAyB,EAAAA,KAAC,MAAA,CACC,MAAO,CACL,QAAS,OACT,WAAY,SACZ,MAAOE,EAAK,QAAU,EAAI,UAAY,SAAA,EAGvC,SAAA,CAAAA,EAAK,QAAU,EACdH,MAACQ,EAAA,CAAgB,KAAM,GAAI,MAAO,CAAE,YAAa,KAAA,EAAS,EAE1DR,EAAAA,IAACS,GAAkB,KAAM,GAAI,MAAO,CAAE,YAAa,OAAS,SAE7D,OAAA,CAAM,SAAA,CAAA,KAAK,IAAIN,EAAK,MAAM,EAAE,GAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAChC,CAAA,CACF,CAAA,CAAA,GAhD6BC,CAkDjC,CACD,EACH,EAGAJ,EAAAA,IAACE,EAAA,CAAI,OAAQ,CAAC,GAAI,EAAE,EAClB,SAAAF,EAAAA,IAACK,EAAA,CAAI,GAAI,GAAI,GAAI,GACf,SAAAL,EAAAA,IAACM,EAAA,CACC,MAAM,SACN,SAAU,GACV,MAAO,CACL,gBAAiB9B,IAAU,OAAS,UAAY,OAChD,aAAc,MACd,UAAW,+BAAA,EAGb,SAAAwB,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,OAAQ,OAAA,EAEpB,SAAAA,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,OAAQ,IAAK,QAAS,OAAQ,WAAY,SAAU,eAAgB,SAAU,WAAY,UAAW,aAAc,OAC/H,SAAAA,MAAC,OAAA,CAAK,MAAO,CAAE,MAAO,MAAA,EAAU,SAAA,YAAA,CAAU,EAC5C,CAAA,CACF,CAAA,CAAA,EAEJ,CAAA,CACF,CAAA,EACF,CAEJ"}