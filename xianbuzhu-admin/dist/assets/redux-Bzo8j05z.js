var ct=Object.defineProperty;var st=(e,t,r)=>t in e?ct(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var ce=(e,t,r)=>st(e,typeof t!="symbol"?t+"":t,r);import{r as P}from"./vendor-BM8LEypB.js";var Le={exports:{}},Ve={};/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var H=P;function ft(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var at=typeof Object.is=="function"?Object.is:ft,lt=H.useSyncExternalStore,dt=H.useRef,pt=H.useEffect,yt=H.useMemo,ht=H.useDebugValue;Ve.useSyncExternalStoreWithSelector=function(e,t,r,n,o){var i=dt(null);if(i.current===null){var u={hasValue:!1,value:null};i.current=u}else u=i.current;i=yt(function(){function s(d){if(!l){if(l=!0,f=d,d=n(d),o!==void 0&&u.hasValue){var y=u.value;if(o(y,d))return p=y}return p=d}if(y=p,at(f,d))return y;var m=n(d);return o!==void 0&&o(y,m)?(f=d,y):(f=d,p=m)}var l=!1,f,p,a=r===void 0?null:r;return[function(){return s(t())},a===null?void 0:function(){return s(a())}]},[t,r,n,o]);var c=lt(e,i[0],i[1]);return pt(function(){u.hasValue=!0,u.value=c},[c]),ht(c),c};Le.exports=Ve;var mt=Le.exports;function Be(e){e()}function wt(){let e=null,t=null;return{clear(){e=null,t=null},notify(){Be(()=>{let r=e;for(;r;)r.callback(),r=r.next})},get(){const r=[];let n=e;for(;n;)r.push(n),n=n.next;return r},subscribe(r){let n=!0;const o=t={callback:r,next:null,prev:t};return o.prev?o.prev.next=o:e=o,function(){!n||e===null||(n=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}var Pe={notify(){},get:()=>[]};function _t(e,t){let r,n=Pe,o=0,i=!1;function u(m){f();const _=n.subscribe(m);let b=!1;return()=>{b||(b=!0,_(),p())}}function c(){n.notify()}function s(){y.onStateChange&&y.onStateChange()}function l(){return i}function f(){o++,r||(r=e.subscribe(s),n=wt())}function p(){o--,r&&o===0&&(r(),r=void 0,n.clear(),n=Pe)}function a(){i||(i=!0,f())}function d(){i&&(i=!1,p())}const y={addNestedSub:u,notifyNestedSubs:c,handleChangeWrapper:s,isSubscribed:l,trySubscribe:a,tryUnsubscribe:d,getListeners:()=>n};return y}var bt=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",gt=bt(),vt=()=>typeof navigator<"u"&&navigator.product==="ReactNative",St=vt(),Et=()=>gt||St?P.useLayoutEffect:P.useEffect,Ct=Et();function Ae(e,t){return e===t?e!==0||t!==0||1/e===1/t:e!==e&&t!==t}function zr(e,t){if(Ae(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;const r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let o=0;o<r.length;o++)if(!Object.prototype.hasOwnProperty.call(t,r[o])||!Ae(e[r[o]],t[r[o]]))return!1;return!0}var se=Symbol.for("react-redux-context"),fe=typeof globalThis<"u"?globalThis:{};function Rt(){if(!P.createContext)return{};const e=fe[se]??(fe[se]=new Map);let t=e.get(P.createContext);return t||(t=P.createContext(null),e.set(P.createContext,t)),t}var z=Rt();function Ot(e){const{children:t,context:r,serverState:n,store:o}=e,i=P.useMemo(()=>{const s=_t(o);return{store:o,subscription:s,getServerState:n?()=>n:void 0}},[o,n]),u=P.useMemo(()=>o.getState(),[o]);Ct(()=>{const{subscription:s}=i;return s.onStateChange=s.notifyNestedSubs,s.trySubscribe(),u!==o.getState()&&s.notifyNestedSubs(),()=>{s.tryUnsubscribe(),s.onStateChange=void 0}},[i,u]);const c=r||z;return P.createElement(c.Provider,{value:i},t)}var jr=Ot;function Se(e=z){return function(){return P.useContext(e)}}var Ue=Se();function qe(e=z){const t=e===z?Ue:Se(e),r=()=>{const{store:n}=t();return n};return Object.assign(r,{withTypes:()=>r}),r}var Pt=qe();function At(e=z){const t=e===z?Pt:qe(e),r=()=>t().dispatch;return Object.assign(r,{withTypes:()=>r}),r}var Ir=At(),Mt=(e,t)=>e===t;function xt(e=z){const t=e===z?Ue:Se(e),r=(n,o={})=>{const{equalityFn:i=Mt}=typeof o=="function"?{equalityFn:o}:o,u=t(),{store:c,subscription:s,getServerState:l}=u;P.useRef(!0);const f=P.useCallback({[n.name](a){return n(a)}}[n.name],[n]),p=mt.useSyncExternalStoreWithSelector(s.addNestedSub,c.getState,l||c.getState,f,i);return P.useDebugValue(p),p};return Object.assign(r,{withTypes:()=>r}),r}var Fr=xt(),Wr=Be;function R(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Dt=typeof Symbol=="function"&&Symbol.observable||"@@observable",Me=Dt,ae=()=>Math.random().toString(36).substring(7).split("").join("."),kt={INIT:`@@redux/INIT${ae()}`,REPLACE:`@@redux/REPLACE${ae()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${ae()}`},te=kt;function Ee(e){if(typeof e!="object"||e===null)return!1;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||Object.getPrototypeOf(e)===null}function Ke(e,t,r){if(typeof e!="function")throw new Error(R(2));if(typeof t=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(R(0));if(typeof t=="function"&&typeof r>"u"&&(r=t,t=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(R(1));return r(Ke)(e,t)}let n=e,o=t,i=new Map,u=i,c=0,s=!1;function l(){u===i&&(u=new Map,i.forEach((_,b)=>{u.set(b,_)}))}function f(){if(s)throw new Error(R(3));return o}function p(_){if(typeof _!="function")throw new Error(R(4));if(s)throw new Error(R(5));let b=!0;l();const g=c++;return u.set(g,_),function(){if(b){if(s)throw new Error(R(6));b=!1,l(),u.delete(g),i=null}}}function a(_){if(!Ee(_))throw new Error(R(7));if(typeof _.type>"u")throw new Error(R(8));if(typeof _.type!="string")throw new Error(R(17));if(s)throw new Error(R(9));try{s=!0,o=n(o,_)}finally{s=!1}return(i=u).forEach(g=>{g()}),_}function d(_){if(typeof _!="function")throw new Error(R(10));n=_,a({type:te.REPLACE})}function y(){const _=p;return{subscribe(b){if(typeof b!="object"||b===null)throw new Error(R(11));function g(){const h=b;h.next&&h.next(f())}return g(),{unsubscribe:_(g)}},[Me](){return this}}}return a({type:te.INIT}),{dispatch:a,subscribe:p,getState:f,replaceReducer:d,[Me]:y}}function Tt(e){Object.keys(e).forEach(t=>{const r=e[t];if(typeof r(void 0,{type:te.INIT})>"u")throw new Error(R(12));if(typeof r(void 0,{type:te.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(R(13))})}function Nt(e){const t=Object.keys(e),r={};for(let i=0;i<t.length;i++){const u=t[i];typeof e[u]=="function"&&(r[u]=e[u])}const n=Object.keys(r);let o;try{Tt(r)}catch(i){o=i}return function(u={},c){if(o)throw o;let s=!1;const l={};for(let f=0;f<n.length;f++){const p=n[f],a=r[p],d=u[p],y=a(d,c);if(typeof y>"u")throw c&&c.type,new Error(R(14));l[p]=y,s=s||y!==d}return s=s||n.length!==Object.keys(u).length,s?l:u}}function re(...e){return e.length===0?t=>t:e.length===1?e[0]:e.reduce((t,r)=>(...n)=>t(r(...n)))}function zt(...e){return t=>(r,n)=>{const o=t(r,n);let i=()=>{throw new Error(R(15))};const u={getState:o.getState,dispatch:(s,...l)=>i(s,...l)},c=e.map(s=>s(u));return i=re(...c)(o.dispatch),{...o,dispatch:i}}}function jt(e){return Ee(e)&&"type"in e&&typeof e.type=="string"}var Ce=Symbol.for("immer-nothing"),L=Symbol.for("immer-draftable"),A=Symbol.for("immer-state");function O(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var I=Object.getPrototypeOf;function T(e){return!!e&&!!e[A]}function D(e){var t;return e?He(e)||Array.isArray(e)||!!e[L]||!!((t=e.constructor)!=null&&t[L])||G(e)||X(e):!1}var It=Object.prototype.constructor.toString();function He(e){if(!e||typeof e!="object")return!1;const t=I(e);if(t===null)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object?!0:typeof r=="function"&&Function.toString.call(r)===It}function $r(e){return T(e)||O(15,e),e[A].base_}function B(e,t){F(e)===0?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function F(e){const t=e[A];return t?t.type_:Array.isArray(e)?1:G(e)?2:X(e)?3:0}function U(e,t){return F(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function le(e,t){return F(e)===2?e.get(t):e[t]}function Ge(e,t,r){const n=F(e);n===2?e.set(t,r):n===3?e.add(r):e[t]=r}function Ft(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function G(e){return e instanceof Map}function X(e){return e instanceof Set}function j(e){return e.copy_||e.base_}function he(e,t){if(G(e))return new Map(e);if(X(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=He(e);if(t===!0||t==="class_only"&&!r){const n=Object.getOwnPropertyDescriptors(e);delete n[A];let o=Reflect.ownKeys(n);for(let i=0;i<o.length;i++){const u=o[i],c=n[u];c.writable===!1&&(c.writable=!0,c.configurable=!0),(c.get||c.set)&&(n[u]={configurable:!0,writable:!0,enumerable:c.enumerable,value:e[u]})}return Object.create(I(e),n)}else{const n=I(e);if(n!==null&&r)return{...e};const o=Object.create(n);return Object.assign(o,e)}}function Re(e,t=!1){return ie(e)||T(e)||!D(e)||(F(e)>1&&(e.set=e.add=e.clear=e.delete=Wt),Object.freeze(e),t&&Object.entries(e).forEach(([r,n])=>Re(n,!0))),e}function Wt(){O(2)}function ie(e){return Object.isFrozen(e)}var me={};function W(e){const t=me[e];return t||O(0,e),t}function $t(e,t){me[e]||(me[e]=t)}var q;function Xe(){return q}function Lt(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function xe(e,t){t&&(W("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function we(e){_e(e),e.drafts_.forEach(Vt),e.drafts_=null}function _e(e){e===q&&(q=e.parent_)}function De(e){return q=Lt(q,e)}function Vt(e){const t=e[A];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function ke(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return e!==void 0&&e!==r?(r[A].modified_&&(we(t),O(4)),D(e)&&(e=ne(t,e),t.parent_||oe(t,e)),t.patches_&&W("Patches").generateReplacementPatches_(r[A].base_,e,t.patches_,t.inversePatches_)):e=ne(t,r,[]),we(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==Ce?e:void 0}function ne(e,t,r){if(ie(t))return t;const n=t[A];if(!n)return B(t,(o,i)=>Te(e,n,t,o,i,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return oe(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const o=n.copy_;let i=o,u=!1;n.type_===3&&(i=new Set(o),o.clear(),u=!0),B(i,(c,s)=>Te(e,n,o,c,s,r,u)),oe(e,o,!1),r&&e.patches_&&W("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function Te(e,t,r,n,o,i,u){if(T(o)){const c=i&&t&&t.type_!==3&&!U(t.assigned_,n)?i.concat(n):void 0,s=ne(e,o,c);if(Ge(r,n,s),T(s))e.canAutoFreeze_=!1;else return}else u&&r.add(o);if(D(o)&&!ie(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;ne(e,o),(!t||!t.scope_.parent_)&&typeof n!="symbol"&&Object.prototype.propertyIsEnumerable.call(r,n)&&oe(e,o)}}function oe(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&Re(t,r)}function Bt(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:Xe(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=Oe;r&&(o=[n],i=K);const{revoke:u,proxy:c}=Proxy.revocable(o,i);return n.draft_=c,n.revoke_=u,c}var Oe={get(e,t){if(t===A)return e;const r=j(e);if(!U(r,t))return Ut(e,r,t);const n=r[t];return e.finalized_||!D(n)?n:n===de(e.base_,t)?(pe(e),e.copy_[t]=ge(n,e)):n},has(e,t){return t in j(e)},ownKeys(e){return Reflect.ownKeys(j(e))},set(e,t,r){const n=Qe(j(e),t);if(n!=null&&n.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const o=de(j(e),t),i=o==null?void 0:o[A];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(Ft(r,o)&&(r!==void 0||U(e.base_,t)))return!0;pe(e),be(e)}return e.copy_[t]===r&&(r!==void 0||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty(e,t){return de(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,pe(e),be(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const r=j(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:n.enumerable,value:r[t]}},defineProperty(){O(11)},getPrototypeOf(e){return I(e.base_)},setPrototypeOf(){O(12)}},K={};B(Oe,(e,t)=>{K[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});K.deleteProperty=function(e,t){return K.set.call(this,e,t,void 0)};K.set=function(e,t,r){return Oe.set.call(this,e[0],t,r,e[0])};function de(e,t){const r=e[A];return(r?j(r):e)[t]}function Ut(e,t,r){var o;const n=Qe(t,r);return n?"value"in n?n.value:(o=n.get)==null?void 0:o.call(e.draft_):void 0}function Qe(e,t){if(!(t in e))return;let r=I(e);for(;r;){const n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=I(r)}}function be(e){e.modified_||(e.modified_=!0,e.parent_&&be(e.parent_))}function pe(e){e.copy_||(e.copy_=he(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var qt=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,r,n)=>{if(typeof t=="function"&&typeof r!="function"){const i=r;r=t;const u=this;return function(s=i,...l){return u.produce(s,f=>r.call(this,f,...l))}}typeof r!="function"&&O(6),n!==void 0&&typeof n!="function"&&O(7);let o;if(D(t)){const i=De(this),u=ge(t,void 0);let c=!0;try{o=r(u),c=!1}finally{c?we(i):_e(i)}return xe(i,n),ke(o,i)}else if(!t||typeof t!="object"){if(o=r(t),o===void 0&&(o=t),o===Ce&&(o=void 0),this.autoFreeze_&&Re(o,!0),n){const i=[],u=[];W("Patches").generateReplacementPatches_(t,o,i,u),n(i,u)}return o}else O(1,t)},this.produceWithPatches=(t,r)=>{if(typeof t=="function")return(u,...c)=>this.produceWithPatches(u,s=>t(s,...c));let n,o;return[this.produce(t,r,(u,c)=>{n=u,o=c}),n,o]},typeof(e==null?void 0:e.autoFreeze)=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof(e==null?void 0:e.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){D(e)||O(8),T(e)&&(e=Kt(e));const t=De(this),r=ge(e,void 0);return r[A].isManual_=!0,_e(t),r}finishDraft(e,t){const r=e&&e[A];(!r||!r.isManual_)&&O(9);const{scope_:n}=r;return xe(n,t),ke(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const o=t[r];if(o.path.length===0&&o.op==="replace"){e=o.value;break}}r>-1&&(t=t.slice(r+1));const n=W("Patches").applyPatches_;return T(e)?n(e,t):this.produce(e,o=>n(o,t))}};function ge(e,t){const r=G(e)?W("MapSet").proxyMap_(e,t):X(e)?W("MapSet").proxySet_(e,t):Bt(e,t);return(t?t.scope_:Xe()).drafts_.push(r),r}function Kt(e){return T(e)||O(10,e),Je(e)}function Je(e){if(!D(e)||ie(e))return e;const t=e[A];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=he(e,t.scope_.immer_.useStrictShallowCopy_)}else r=he(e,!0);return B(r,(n,o)=>{Ge(r,n,Je(o))}),t&&(t.finalized_=!1),r}function Lr(){const t="replace",r="add",n="remove";function o(a,d,y,m){switch(a.type_){case 0:case 2:return u(a,d,y,m);case 1:return i(a,d,y,m);case 3:return c(a,d,y,m)}}function i(a,d,y,m){let{base_:_,assigned_:b}=a,g=a.copy_;g.length<_.length&&([_,g]=[g,_],[y,m]=[m,y]);for(let w=0;w<_.length;w++)if(b[w]&&g[w]!==_[w]){const h=d.concat([w]);y.push({op:t,path:h,value:p(g[w])}),m.push({op:t,path:h,value:p(_[w])})}for(let w=_.length;w<g.length;w++){const h=d.concat([w]);y.push({op:r,path:h,value:p(g[w])})}for(let w=g.length-1;_.length<=w;--w){const h=d.concat([w]);m.push({op:n,path:h})}}function u(a,d,y,m){const{base_:_,copy_:b}=a;B(a.assigned_,(g,w)=>{const h=le(_,g),v=le(b,g),E=w?U(_,g)?t:r:n;if(h===v&&E===t)return;const S=d.concat(g);y.push(E===n?{op:E,path:S}:{op:E,path:S,value:v}),m.push(E===r?{op:n,path:S}:E===n?{op:r,path:S,value:p(h)}:{op:t,path:S,value:p(h)})})}function c(a,d,y,m){let{base_:_,copy_:b}=a,g=0;_.forEach(w=>{if(!b.has(w)){const h=d.concat([g]);y.push({op:n,path:h,value:w}),m.unshift({op:r,path:h,value:w})}g++}),g=0,b.forEach(w=>{if(!_.has(w)){const h=d.concat([g]);y.push({op:r,path:h,value:w}),m.unshift({op:n,path:h,value:w})}g++})}function s(a,d,y,m){y.push({op:t,path:[],value:d===Ce?void 0:d}),m.push({op:t,path:[],value:a})}function l(a,d){return d.forEach(y=>{const{path:m,op:_}=y;let b=a;for(let v=0;v<m.length-1;v++){const E=F(b);let S=m[v];typeof S!="string"&&typeof S!="number"&&(S=""+S),(E===0||E===1)&&(S==="__proto__"||S==="constructor")&&O(19),typeof b=="function"&&S==="prototype"&&O(19),b=le(b,S),typeof b!="object"&&O(18,m.join("/"))}const g=F(b),w=f(y.value),h=m[m.length-1];switch(_){case t:switch(g){case 2:return b.set(h,w);case 3:O(16);default:return b[h]=w}case r:switch(g){case 1:return h==="-"?b.push(w):b.splice(h,0,w);case 2:return b.set(h,w);case 3:return b.add(w);default:return b[h]=w}case n:switch(g){case 1:return b.splice(h,1);case 2:return b.delete(h);case 3:return b.delete(y.value);default:return delete b[h]}default:O(17,_)}}),a}function f(a){if(!D(a))return a;if(Array.isArray(a))return a.map(f);if(G(a))return new Map(Array.from(a.entries()).map(([y,m])=>[y,f(m)]));if(X(a))return new Set(Array.from(a).map(f));const d=Object.create(I(a));for(const y in a)d[y]=f(a[y]);return U(a,L)&&(d[L]=a[L]),d}function p(a){return T(a)?f(a):a}$t("Patches",{applyPatches_:l,generatePatches_:o,generateReplacementPatches_:s})}var M=new qt,Ye=M.produce,Vr=M.produceWithPatches.bind(M);M.setAutoFreeze.bind(M);M.setUseStrictShallowCopy.bind(M);var Br=M.applyPatches.bind(M);M.createDraft.bind(M);M.finishDraft.bind(M);function Ht(e,t=`expected a function, instead received ${typeof e}`){if(typeof e!="function")throw new TypeError(t)}function Gt(e,t=`expected an object, instead received ${typeof e}`){if(typeof e!="object")throw new TypeError(t)}function Xt(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(r=>typeof r=="function")){const r=e.map(n=>typeof n=="function"?`function ${n.name||"unnamed"}()`:typeof n).join(", ");throw new TypeError(`${t}[${r}]`)}}var Ne=e=>Array.isArray(e)?e:[e];function Qt(e){const t=Array.isArray(e[0])?e[0]:e;return Xt(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}function Jt(e,t){const r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}var Yt=class{constructor(e){this.value=e}deref(){return this.value}},Zt=typeof WeakRef<"u"?WeakRef:Yt,er=0,ze=1;function Y(){return{s:er,v:void 0,o:null,p:null}}function Ze(e,t={}){let r=Y();const{resultEqualityCheck:n}=t;let o,i=0;function u(){var p;let c=r;const{length:s}=arguments;for(let a=0,d=s;a<d;a++){const y=arguments[a];if(typeof y=="function"||typeof y=="object"&&y!==null){let m=c.o;m===null&&(c.o=m=new WeakMap);const _=m.get(y);_===void 0?(c=Y(),m.set(y,c)):c=_}else{let m=c.p;m===null&&(c.p=m=new Map);const _=m.get(y);_===void 0?(c=Y(),m.set(y,c)):c=_}}const l=c;let f;if(c.s===ze)f=c.v;else if(f=e.apply(null,arguments),i++,n){const a=((p=o==null?void 0:o.deref)==null?void 0:p.call(o))??o;a!=null&&n(a,f)&&(f=a,i!==0&&i--),o=typeof f=="object"&&f!==null||typeof f=="function"?new Zt(f):f}return l.s=ze,l.v=f,f}return u.clearCache=()=>{r=Y(),u.resetResultsCount()},u.resultsCount=()=>i,u.resetResultsCount=()=>{i=0},u}function tr(e,...t){const r=typeof e=="function"?{memoize:e,memoizeOptions:t}:e,n=(...o)=>{let i=0,u=0,c,s={},l=o.pop();typeof l=="object"&&(s=l,l=o.pop()),Ht(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);const f={...r,...s},{memoize:p,memoizeOptions:a=[],argsMemoize:d=Ze,argsMemoizeOptions:y=[]}=f,m=Ne(a),_=Ne(y),b=Qt(o),g=p(function(){return i++,l.apply(null,arguments)},...m),w=d(function(){u++;const v=Jt(b,arguments);return c=g.apply(null,v),c},..._);return Object.assign(w,{resultFunc:l,memoizedResultFunc:g,dependencies:b,dependencyRecomputations:()=>u,resetDependencyRecomputations:()=>{u=0},lastResult:()=>c,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:p,argsMemoize:d})};return Object.assign(n,{withTypes:()=>n}),n}var rr=tr(Ze),nr=Object.assign((e,t=rr)=>{Gt(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);const r=Object.keys(e),n=r.map(i=>e[i]);return t(n,(...i)=>i.reduce((u,c,s)=>(u[r[s]]=c,u),{}))},{withTypes:()=>nr});function et(e){return({dispatch:r,getState:n})=>o=>i=>typeof i=="function"?i(r,n,e):o(i)}var or=et(),ir=et,ur=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?re:re.apply(null,arguments)},cr=e=>e&&typeof e.match=="function";function V(e,t){function r(...n){if(t){let o=t(...n);if(!o)throw new Error(k(0));return{type:e,payload:o.payload,..."meta"in o&&{meta:o.meta},..."error"in o&&{error:o.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=n=>jt(n)&&n.type===e,r}var tt=class $ extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,$.prototype)}static get[Symbol.species](){return $}concat(...t){return super.concat.apply(this,t)}prepend(...t){return t.length===1&&Array.isArray(t[0])?new $(...t[0].concat(this)):new $(...t.concat(this))}};function je(e){return D(e)?Ye(e,()=>{}):e}function Z(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}function sr(e){return typeof e=="boolean"}var fr=()=>function(t){const{thunk:r=!0,immutableCheck:n=!0,serializableCheck:o=!0,actionCreatorCheck:i=!0}=t??{};let u=new tt;return r&&(sr(r)?u.push(or):u.push(ir(r.extraArgument))),u},rt="RTK_autoBatch",Ur=()=>e=>({payload:e,meta:{[rt]:!0}}),Ie=e=>t=>{setTimeout(t,e)},ar=(e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let o=!0,i=!1,u=!1;const c=new Set,s=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:Ie(10):e.type==="callback"?e.queueNotification:Ie(e.timeout),l=()=>{u=!1,i&&(i=!1,c.forEach(f=>f()))};return Object.assign({},n,{subscribe(f){const p=()=>o&&f(),a=n.subscribe(p);return c.add(f),()=>{a(),c.delete(f)}},dispatch(f){var p;try{return o=!((p=f==null?void 0:f.meta)!=null&&p[rt]),i=!o,i&&(u||(u=!0,s(l))),n.dispatch(f)}finally{o=!0}}})},lr=e=>function(r){const{autoBatch:n=!0}=r??{};let o=new tt(e);return n&&o.push(ar(typeof n=="object"?n:void 0)),o};function qr(e){const t=fr(),{reducer:r=void 0,middleware:n,devTools:o=!0,preloadedState:i=void 0,enhancers:u=void 0}=e||{};let c;if(typeof r=="function")c=r;else if(Ee(r))c=Nt(r);else throw new Error(k(1));let s;typeof n=="function"?s=n(t):s=t();let l=re;o&&(l=ur({trace:!1,...typeof o=="object"&&o}));const f=zt(...s),p=lr(f);let a=typeof u=="function"?u(p):p();const d=l(...a);return Ke(c,i,d)}function nt(e){const t={},r=[];let n;const o={addCase(i,u){const c=typeof i=="string"?i:i.type;if(!c)throw new Error(k(28));if(c in t)throw new Error(k(29));return t[c]=u,o},addMatcher(i,u){return r.push({matcher:i,reducer:u}),o},addDefaultCase(i){return n=i,o}};return e(o),[t,r,n]}function dr(e){return typeof e=="function"}function pr(e,t){let[r,n,o]=nt(t),i;if(dr(e))i=()=>je(e());else{const c=je(e);i=()=>c}function u(c=i(),s){let l=[r[s.type],...n.filter(({matcher:f})=>f(s)).map(({reducer:f})=>f)];return l.filter(f=>!!f).length===0&&(l=[o]),l.reduce((f,p)=>{if(p)if(T(f)){const d=p(f,s);return d===void 0?f:d}else{if(D(f))return Ye(f,a=>p(a,s));{const a=p(f,s);if(a===void 0){if(f===null)return f;throw Error("A case reducer on a non-draftable value must not return undefined")}return a}}return f},c)}return u.getInitialState=i,u}var ot=(e,t)=>cr(e)?e.match(t):e(t);function Q(...e){return t=>e.some(r=>ot(r,t))}function Fe(...e){return t=>e.every(r=>ot(r,t))}function ue(e,t){if(!e||!e.meta)return!1;const r=typeof e.meta.requestId=="string",n=t.indexOf(e.meta.requestStatus)>-1;return r&&n}function J(e){return typeof e[0]=="function"&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function yr(...e){return e.length===0?t=>ue(t,["pending"]):J(e)?Q(...e.map(t=>t.pending)):yr()(e[0])}function ve(...e){return e.length===0?t=>ue(t,["rejected"]):J(e)?Q(...e.map(t=>t.rejected)):ve()(e[0])}function hr(...e){const t=r=>r&&r.meta&&r.meta.rejectedWithValue;return e.length===0?Fe(ve(...e),t):J(e)?Fe(ve(...e),t):hr()(e[0])}function mr(...e){return e.length===0?t=>ue(t,["fulfilled"]):J(e)?Q(...e.map(t=>t.fulfilled)):mr()(e[0])}function wr(...e){return e.length===0?t=>ue(t,["pending","fulfilled","rejected"]):J(e)?Q(...e.flatMap(t=>[t.pending,t.rejected,t.fulfilled])):wr()(e[0])}var _r="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",br=(e=21)=>{let t="",r=e;for(;r--;)t+=_r[Math.random()*64|0];return t},gr=["name","message","stack","code"],ye=class{constructor(e,t){ce(this,"_type");this.payload=e,this.meta=t}},We=class{constructor(e,t){ce(this,"_type");this.payload=e,this.meta=t}},vr=e=>{if(typeof e=="object"&&e!==null){const t={};for(const r of gr)typeof e[r]=="string"&&(t[r]=e[r]);return t}return{message:String(e)}},$e="External signal was aborted",Kr=(()=>{function e(t,r,n){const o=V(t+"/fulfilled",(s,l,f,p)=>({payload:s,meta:{...p||{},arg:f,requestId:l,requestStatus:"fulfilled"}})),i=V(t+"/pending",(s,l,f)=>({payload:void 0,meta:{...f||{},arg:l,requestId:s,requestStatus:"pending"}})),u=V(t+"/rejected",(s,l,f,p,a)=>({payload:p,error:(n&&n.serializeError||vr)(s||"Rejected"),meta:{...a||{},arg:f,requestId:l,rejectedWithValue:!!p,requestStatus:"rejected",aborted:(s==null?void 0:s.name)==="AbortError",condition:(s==null?void 0:s.name)==="ConditionError"}}));function c(s,{signal:l}={}){return(f,p,a)=>{const d=n!=null&&n.idGenerator?n.idGenerator(s):br(),y=new AbortController;let m,_;function b(w){_=w,y.abort()}l&&(l.aborted?b($e):l.addEventListener("abort",()=>b($e),{once:!0}));const g=async function(){var v,E;let w;try{let S=(v=n==null?void 0:n.condition)==null?void 0:v.call(n,s,{getState:p,extra:a});if(Er(S)&&(S=await S),S===!1||y.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};const x=new Promise((C,N)=>{m=()=>{N({name:"AbortError",message:_||"Aborted"})},y.signal.addEventListener("abort",m)});f(i(d,s,(E=n==null?void 0:n.getPendingMeta)==null?void 0:E.call(n,{requestId:d,arg:s},{getState:p,extra:a}))),w=await Promise.race([x,Promise.resolve(r(s,{dispatch:f,getState:p,extra:a,requestId:d,signal:y.signal,abort:b,rejectWithValue:(C,N)=>new ye(C,N),fulfillWithValue:(C,N)=>new We(C,N)})).then(C=>{if(C instanceof ye)throw C;return C instanceof We?o(C.payload,d,s,C.meta):o(C,d,s)})])}catch(S){w=S instanceof ye?u(null,d,s,S.payload,S.meta):u(S,d,s)}finally{m&&y.signal.removeEventListener("abort",m)}return n&&!n.dispatchConditionRejection&&u.match(w)&&w.meta.condition||f(w),w}();return Object.assign(g,{abort:b,requestId:d,arg:s,unwrap(){return g.then(Sr)}})}}return Object.assign(c,{pending:i,rejected:u,fulfilled:o,settled:Q(u,o),typePrefix:t})}return e.withTypes=()=>e,e})();function Sr(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function Er(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var Cr=Symbol.for("rtk-slice-createasyncthunk");function Rr(e,t){return`${e}/${t}`}function Or({creators:e}={}){var r;const t=(r=e==null?void 0:e.asyncThunk)==null?void 0:r[Cr];return function(o){const{name:i,reducerPath:u=i}=o;if(!i)throw new Error(k(11));const c=(typeof o.reducers=="function"?o.reducers(Ar()):o.reducers)||{},s=Object.keys(c),l={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},f={addCase(h,v){const E=typeof h=="string"?h:h.type;if(!E)throw new Error(k(12));if(E in l.sliceCaseReducersByType)throw new Error(k(13));return l.sliceCaseReducersByType[E]=v,f},addMatcher(h,v){return l.sliceMatchers.push({matcher:h,reducer:v}),f},exposeAction(h,v){return l.actionCreators[h]=v,f},exposeCaseReducer(h,v){return l.sliceCaseReducersByName[h]=v,f}};s.forEach(h=>{const v=c[h],E={reducerName:h,type:Rr(i,h),createNotation:typeof o.reducers=="function"};xr(v)?kr(E,v,f,t):Mr(E,v,f)});function p(){const[h={},v=[],E=void 0]=typeof o.extraReducers=="function"?nt(o.extraReducers):[o.extraReducers],S={...h,...l.sliceCaseReducersByType};return pr(o.initialState,x=>{for(let C in S)x.addCase(C,S[C]);for(let C of l.sliceMatchers)x.addMatcher(C.matcher,C.reducer);for(let C of v)x.addMatcher(C.matcher,C.reducer);E&&x.addDefaultCase(E)})}const a=h=>h,d=new Map,y=new WeakMap;let m;function _(h,v){return m||(m=p()),m(h,v)}function b(){return m||(m=p()),m.getInitialState()}function g(h,v=!1){function E(x){let C=x[h];return typeof C>"u"&&v&&(C=Z(y,E,b)),C}function S(x=a){const C=Z(d,v,()=>new WeakMap);return Z(C,x,()=>{const N={};for(const[it,ut]of Object.entries(o.selectors??{}))N[it]=Pr(ut,x,()=>Z(y,x,b),v);return N})}return{reducerPath:h,getSelectors:S,get selectors(){return S(E)},selectSlice:E}}const w={name:i,reducer:_,actions:l.actionCreators,caseReducers:l.sliceCaseReducersByName,getInitialState:b,...g(u),injectInto(h,{reducerPath:v,...E}={}){const S=v??u;return h.inject({reducerPath:S,reducer:_},E),{...w,...g(S,!0)}}};return w}}function Pr(e,t,r,n){function o(i,...u){let c=t(i);return typeof c>"u"&&n&&(c=r()),e(c,...u)}return o.unwrapped=e,o}var Hr=Or();function Ar(){function e(t,r){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...r}}return e.withTypes=()=>e,{reducer(t){return Object.assign({[t.name](...r){return t(...r)}}[t.name],{_reducerDefinitionType:"reducer"})},preparedReducer(t,r){return{_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:r}},asyncThunk:e}}function Mr({type:e,reducerName:t,createNotation:r},n,o){let i,u;if("reducer"in n){if(r&&!Dr(n))throw new Error(k(17));i=n.reducer,u=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,u?V(e,u):V(e))}function xr(e){return e._reducerDefinitionType==="asyncThunk"}function Dr(e){return e._reducerDefinitionType==="reducerWithPrepare"}function kr({type:e,reducerName:t},r,n,o){if(!o)throw new Error(k(18));const{payloadCreator:i,fulfilled:u,pending:c,rejected:s,settled:l,options:f}=r,p=o(e,i,f);n.exposeAction(t,p),u&&n.addCase(p.fulfilled,u),c&&n.addCase(p.pending,c),s&&n.addCase(p.rejected,s),l&&n.addMatcher(p.settled,l),n.exposeCaseReducer(t,{fulfilled:u||ee,pending:c||ee,rejected:s||ee,settled:l||ee})}function ee(){}function k(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}export{Pt as A,Fr as B,Ir as C,zr as D,qr as E,jr as P,rt as S,rr as a,Kr as b,V as c,Hr as d,Lr as e,k as f,Ur as g,Q as h,Ee as i,hr as j,mr as k,Br as l,Nt as m,br as n,Fe as o,Ye as p,ve as q,yr as r,D as s,Vr as t,jt as u,wr as v,Ze as w,T as x,$r as y,Wr as z};
//# sourceMappingURL=redux-Bzo8j05z.js.map
