const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-CcEm1oee.js","assets/vendor-BM8LEypB.js","assets/antd-sY6o5fYU.js","assets/redux-Bzo8j05z.js"])))=>i.map(i=>d[i]);
var gs=Object.defineProperty;var ms=(e,t,r)=>t in e?gs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Lt=(e,t,r)=>ms(e,typeof t!="symbol"?t+"":t,r);import{r as L,c as ys,g as vs,u as rn,e as nn,O as bs,N as sn,o as de,f as Ss,h as Xe,d as xs,B as on}from"./vendor-BM8LEypB.js";import{i as Ue,w as _r,n as an,f as cn,e as ws,c as Oe,a as un,p as lr,b as Pr,d as me,g as De,h as ft,j as dr,k as xe,l as Er,m as Is,S as ln,o as Nt,q as fr,r as dn,s as _s,t as fn,u as Ps,v as Rr,x as Es,y as Rs,z as As,A as Cs,B as pn,C as hn,D as Fe,E as ks,P as gn}from"./redux-Bzo8j05z.js";import{o as Ts,T as js,L as Be,M as ot,R as mn,a as Os,b as $s,c as Gt,d as qs,e as Ds,f as Ms,g as Qs,h as Ls,i as Ns,B as Jt,j as Fs,k as zs,D as Us,A as Bs,S as yn,l as Ys,r as Hs,C as vn}from"./antd-sY6o5fYU.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function r(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(s){if(s.ep)return;s.ep=!0;const o=r(s);fetch(s.href,o)}})();var bn={exports:{}},St={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ks=L,Ws=Symbol.for("react.element"),Vs=Symbol.for("react.fragment"),Gs=Object.prototype.hasOwnProperty,Js=Ks.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Zs={key:!0,ref:!0,__self:!0,__source:!0};function Sn(e,t,r){var n,s={},o=null,a=null;r!==void 0&&(o=""+r),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(a=t.ref);for(n in t)Gs.call(t,n)&&!Zs.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)s[n]===void 0&&(s[n]=t[n]);return{$$typeof:Ws,type:e,key:o,ref:a,props:s,_owner:Js.current}}St.Fragment=Vs;St.jsx=Sn;St.jsxs=Sn;bn.exports=St;var O=bn.exports,xn,Ar=ys;xn=Ar.createRoot,Ar.hydrateRoot;var xt={},wn={exports:{}};(function(e){function t(r){return r&&r.__esModule?r:{default:r}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(wn);var wt=wn.exports,It={};Object.defineProperty(It,"__esModule",{value:!0});It.default=void 0;var Xs={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};It.default=Xs;var _t={},Ge={},Pt={},Et={};Object.defineProperty(Et,"__esModule",{value:!0});Et.commonLocale=void 0;Et.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0};var ei=wt.default;Object.defineProperty(Pt,"__esModule",{value:!0});Pt.default=void 0;var Cr=ei(Ts),ti=Et,ri=(0,Cr.default)((0,Cr.default)({},ti.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});Pt.default=ri;var Je={};Object.defineProperty(Je,"__esModule",{value:!0});Je.default=void 0;const ni={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]};Je.default=ni;var In=wt.default;Object.defineProperty(Ge,"__esModule",{value:!0});Ge.default=void 0;var si=In(Pt),ii=In(Je);const _n={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},si.default),timePickerLocale:Object.assign({},ii.default)};_n.lang.ok="确定";Ge.default=_n;var oi=wt.default;Object.defineProperty(_t,"__esModule",{value:!0});_t.default=void 0;var ai=oi(Ge);_t.default=ai.default;var Rt=wt.default;Object.defineProperty(xt,"__esModule",{value:!0});xt.default=void 0;var ci=Rt(It),ui=Rt(_t),li=Rt(Ge),di=Rt(Je);const ne="${label}不是一个有效的${type}",fi={locale:"zh-cn",Pagination:ci.default,DatePicker:li.default,TimePicker:di.default,Calendar:ui.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:ne,method:ne,array:ne,object:ne,number:ne,date:ne,boolean:ne,integer:ne,float:ne,regexp:ne,email:ne,url:ne,hex:ne},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};xt.default=fi;var pi=xt;const Pn=vs(pi);var hi=class extends Error{constructor(t){super(t[0].message);Lt(this,"issues");this.name="SchemaError",this.issues=t}},En=(e=>(e.uninitialized="uninitialized",e.pending="pending",e.fulfilled="fulfilled",e.rejected="rejected",e))(En||{});function kr(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}var Tr=Ue;function Rn(e,t){if(e===t||!(Tr(e)&&Tr(t)||Array.isArray(e)&&Array.isArray(t)))return t;const r=Object.keys(t),n=Object.keys(e);let s=r.length===n.length;const o=Array.isArray(t)?[]:{};for(const a of r)o[a]=Rn(e[a],t[a]),s&&(s=e[a]===o[a]);return s?e:o}function Re(e){let t=0;for(const r in e)t++;return t}var jr=e=>[].concat(...e);function gi(e){return new RegExp("(^|:)//").test(e)}function mi(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function pt(e){return e!=null}function yi(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var vi=e=>e.replace(/\/$/,""),bi=e=>e.replace(/^\//,"");function Si(e,t){if(!e)return t;if(!t)return e;if(gi(t))return t;const r=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=vi(e),t=bi(t),`${e}${r}${t}`}function xi(e,t,r){return e.has(t)?e.get(t):e.set(t,r).get(t)}var Or=(...e)=>fetch(...e),wi=e=>e.status>=200&&e.status<=299,Ii=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function $r(e){if(!Ue(e))return e;const t={...e};for(const[r,n]of Object.entries(t))n===void 0&&delete t[r];return t}function _i({baseUrl:e,prepareHeaders:t=_=>_,fetchFn:r=Or,paramsSerializer:n,isJsonContentType:s=Ii,jsonContentType:o="application/json",jsonReplacer:a,timeout:S,responseHandler:I,validateStatus:w,...C}={}){return typeof fetch>"u"&&r===Or&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(b,f,P)=>{const{getState:A,extra:R,endpoint:x,forced:k,type:p}=f;let i,{url:u,headers:y=new Headers(C.headers),params:v=void 0,responseHandler:E=I??"json",validateStatus:m=w??wi,timeout:l=S,...c}=typeof b=="string"?{url:b}:b,d,h=f.signal;l&&(d=new AbortController,f.signal.addEventListener("abort",d.abort),h=d.signal);let g={...C,signal:h,...c};y=new Headers($r(y)),g.headers=await t(y,{getState:A,arg:b,extra:R,endpoint:x,forced:k,type:p,extraOptions:P})||y;const T=B=>typeof B=="object"&&(Ue(B)||Array.isArray(B)||typeof B.toJSON=="function");if(!g.headers.has("content-type")&&T(g.body)&&g.headers.set("content-type",o),T(g.body)&&s(g.headers)&&(g.body=JSON.stringify(g.body,a)),v){const B=~u.indexOf("?")?"&":"?",D=n?n(v):new URLSearchParams($r(v));u+=B+D}u=Si(e,u);const $=new Request(u,g);i={request:new Request(u,g)};let M,j=!1,Q=d&&setTimeout(()=>{j=!0,d.abort()},l);try{M=await r($)}catch(B){return{error:{status:j?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(B)},meta:i}}finally{Q&&clearTimeout(Q),d==null||d.signal.removeEventListener("abort",d.abort)}const N=M.clone();i.response=N;let z,V="";try{let B;if(await Promise.all([_(M,E).then(D=>z=D,D=>B=D),N.text().then(D=>V=D,()=>{})]),B)throw B}catch(B){return{error:{status:"PARSING_ERROR",originalStatus:M.status,data:V,error:String(B)},meta:i}}return m(M,z)?{data:z,meta:i}:{error:{status:M.status,data:z},meta:i}};async function _(b,f){if(typeof f=="function")return f(b);if(f==="content-type"&&(f=s(b.headers)?"json":"text"),f==="json"){const P=await b.text();return P.length?JSON.parse(P):null}return b.text()}}var qr=class{constructor(e,t=void 0){this.value=e,this.meta=t}},At=Oe("__rtkq/focused"),pr=Oe("__rtkq/unfocused"),Ct=Oe("__rtkq/online"),hr=Oe("__rtkq/offline"),Ft=!1;function Pi(e,t){function r(){const n=()=>e(At()),s=()=>e(pr()),o=()=>e(Ct()),a=()=>e(hr()),S=()=>{window.document.visibilityState==="visible"?n():s()};return Ft||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",S,!1),window.addEventListener("focus",n,!1),window.addEventListener("online",o,!1),window.addEventListener("offline",a,!1),Ft=!0),()=>{window.removeEventListener("focus",n),window.removeEventListener("visibilitychange",S),window.removeEventListener("online",o),window.removeEventListener("offline",a),Ft=!1}}return r()}function kt(e){return e.type==="query"}function Ei(e){return e.type==="mutation"}function Tt(e){return e.type==="infinitequery"}function ht(e){return kt(e)||Tt(e)}function gr(e,t,r,n,s,o){return Ri(e)?e(t,r,n,s).filter(pt).map(Zt).map(o):Array.isArray(e)?e.map(Zt).map(o):[]}function Ri(e){return typeof e=="function"}function Zt(e){return typeof e=="string"?{type:e}:e}function Ai(e,t){return e.catch(t)}var Ye=Symbol("forceQueryFn"),Xt=e=>typeof e[Ye]=="function";function Ci({serializeQueryArgs:e,queryThunk:t,infiniteQueryThunk:r,mutationThunk:n,api:s,context:o}){const a=new Map,S=new Map,{unsubscribeQueryResult:I,removeMutationResult:w,updateSubscriptionOptions:C}=s.internalActions;return{buildInitiateQuery:R,buildInitiateInfiniteQuery:x,buildInitiateMutation:k,getRunningQueryThunk:_,getRunningMutationThunk:b,getRunningQueriesThunk:f,getRunningMutationsThunk:P};function _(p,i){return u=>{var E;const y=o.endpointDefinitions[p],v=e({queryArgs:i,endpointDefinition:y,endpointName:p});return(E=a.get(u))==null?void 0:E[v]}}function b(p,i){return u=>{var y;return(y=S.get(u))==null?void 0:y[i]}}function f(){return p=>Object.values(a.get(p)||{}).filter(pt)}function P(){return p=>Object.values(S.get(p)||{}).filter(pt)}function A(p,i){const u=(y,{subscribe:v=!0,forceRefetch:E,subscriptionOptions:m,[Ye]:l,...c}={})=>(d,h)=>{var H;const g=e({queryArgs:y,endpointDefinition:i,endpointName:p});let T;const $={...c,type:"query",subscribe:v,forceRefetch:E,subscriptionOptions:m,endpointName:p,originalArgs:y,queryCacheKey:g,[Ye]:l};if(kt(i))T=t($);else{const{direction:U,initialPageParam:Z}=c;T=r({...$,direction:U,initialPageParam:Z})}const q=s.endpoints[p].select(y),M=d(T),j=q(h()),{requestId:Q,abort:N}=M,z=j.requestId!==Q,V=(H=a.get(d))==null?void 0:H[g],B=()=>q(h()),D=Object.assign(l?M.then(B):z&&!V?Promise.resolve(j):Promise.all([V,M]).then(B),{arg:y,requestId:Q,subscriptionOptions:m,queryCacheKey:g,abort:N,async unwrap(){const U=await D;if(U.isError)throw U.error;return U.data},refetch:()=>d(u(y,{subscribe:!1,forceRefetch:!0})),unsubscribe(){v&&d(I({queryCacheKey:g,requestId:Q}))},updateSubscriptionOptions(U){D.subscriptionOptions=U,d(C({endpointName:p,requestId:Q,queryCacheKey:g,options:U}))}});if(!V&&!z&&!l){const U=xi(a,d,{});U[g]=D,D.then(()=>{delete U[g],Re(U)||a.delete(d)})}return D};return u}function R(p,i){return A(p,i)}function x(p,i){return A(p,i)}function k(p){return(i,{track:u=!0,fixedCacheKey:y}={})=>(v,E)=>{const m=n({type:"mutation",endpointName:p,originalArgs:i,track:u,fixedCacheKey:y}),l=v(m),{requestId:c,abort:d,unwrap:h}=l,g=Ai(l.unwrap().then(M=>({data:M})),M=>({error:M})),T=()=>{v(w({requestId:c,fixedCacheKey:y}))},$=Object.assign(g,{arg:l.arg,requestId:c,abort:d,unwrap:h,reset:T}),q=S.get(v)||{};return S.set(v,q),q[c]=$,$.then(()=>{delete q[c],Re(q)||S.delete(v)}),y&&(q[y]=$,$.then(()=>{q[y]===$&&(delete q[y],Re(q)||S.delete(v))})),$}}}var An=class extends hi{constructor(e,t,r,n){super(e),this.value=t,this.schemaName=r,this._bqMeta=n}};async function ve(e,t,r,n){const s=await e["~standard"].validate(t);if(s.issues)throw new An(s.issues,t,r,n);return s.value}function ki(e){return e}var Me=(e={})=>({...e,[ln]:!0});function Ti({reducerPath:e,baseQuery:t,context:{endpointDefinitions:r},serializeQueryArgs:n,api:s,assertTagType:o,selectors:a,onSchemaFailure:S,catchSchemaFailure:I,skipSchemaValidation:w}){const C=(c,d,h,g)=>(T,$)=>{const q=r[c],M=n({queryArgs:d,endpointDefinition:q,endpointName:c});if(T(s.internalActions.queryResultPatched({queryCacheKey:M,patches:h})),!g)return;const j=s.endpoints[c].select(d)($()),Q=gr(q.providesTags,j.data,void 0,d,{},o);T(s.internalActions.updateProvidedBy([{queryCacheKey:M,providedTags:Q}]))};function _(c,d,h=0){const g=[d,...c];return h&&g.length>h?g.slice(0,-1):g}function b(c,d,h=0){const g=[...c,d];return h&&g.length>h?g.slice(1):g}const f=(c,d,h,g=!0)=>(T,$)=>{const M=s.endpoints[c].select(d)($()),j={patches:[],inversePatches:[],undo:()=>T(s.util.patchQueryData(c,d,j.inversePatches,g))};if(M.status==="uninitialized")return j;let Q;if("data"in M)if(_s(M.data)){const[N,z,V]=fn(M.data,h);j.patches.push(...z),j.inversePatches.push(...V),Q=N}else Q=h(M.data),j.patches.push({op:"replace",path:[],value:Q}),j.inversePatches.push({op:"replace",path:[],value:M.data});return j.patches.length===0||T(s.util.patchQueryData(c,d,j.patches,g)),j},P=(c,d,h)=>g=>g(s.endpoints[c].initiate(d,{subscribe:!1,forceRefetch:!0,[Ye]:()=>({data:h})})),A=(c,d)=>c.query&&c[d]?c[d]:ki,R=async(c,{signal:d,abort:h,rejectWithValue:g,fulfillWithValue:T,dispatch:$,getState:q,extra:M})=>{var z,V;const j=r[c.endpointName],{metaSchema:Q,skipSchemaValidation:N=w}=j;try{let B=A(j,"transformResponse");const D={signal:d,abort:h,dispatch:$,getState:q,extra:M,endpoint:c.endpointName,type:c.type,forced:c.type==="query"?x(c,q()):void 0,queryCacheKey:c.type==="query"?c.queryCacheKey:void 0},H=c.type==="query"?c[Ye]:void 0;let U;const Z=async(W,Y,J,we)=>{if(Y==null&&W.pages.length)return Promise.resolve({data:W});const ce={queryArg:c.originalArgs,pageParam:Y},Ie=await te(ce),ue=we?_:b;return{data:{pages:ue(W.pages,Ie.data,J),pageParams:ue(W.pageParams,Y,J)},meta:Ie.meta}};async function te(W){let Y;const{extraOptions:J,argSchema:we,rawResponseSchema:ce,responseSchema:Ie}=j;if(we&&!N&&(W=await ve(we,W,"argSchema",{})),H?Y=H():j.query?Y=await t(j.query(W),D,J):Y=await j.queryFn(W,D,J,qe=>t(qe,D,J)),typeof process<"u",Y.error)throw new qr(Y.error,Y.meta);let{data:ue}=Y;ce&&!N&&(ue=await ve(ce,Y.data,"rawResponseSchema",Y.meta));let he=await B(ue,Y.meta,W);return Ie&&!N&&(he=await ve(Ie,he,"responseSchema",Y.meta)),{...Y,data:he}}if(c.type==="query"&&"infiniteQueryOptions"in j){const{infiniteQueryOptions:W}=j,{maxPages:Y=1/0}=W;let J;const we={pages:[],pageParams:[]},ce=(z=a.selectQueryEntry(q(),c.queryCacheKey))==null?void 0:z.data,ue=x(c,q())&&!c.direction||!ce?we:ce;if("direction"in c&&c.direction&&ue.pages.length){const he=c.direction==="backward",Qt=(he?Cn:er)(W,ue,c.originalArgs);J=await Z(ue,Qt,Y,he)}else{const{initialPageParam:he=W.initialPageParam}=c,qe=(ce==null?void 0:ce.pageParams)??[],Qt=qe[0]??he,ps=qe.length;J=await Z(ue,Qt,Y),H&&(J={data:J.data.pages[0]});for(let Ir=1;Ir<ps;Ir++){const hs=er(W,J.data,c.originalArgs);J=await Z(J.data,hs,Y)}}U=J}else U=await te(c.originalArgs);return Q&&!N&&U.meta&&(U.meta=await ve(Q,U.meta,"metaSchema",U.meta)),T(U.data,Me({fulfilledTimeStamp:Date.now(),baseQueryMeta:U.meta}))}catch(B){let D=B;if(D instanceof qr){let H=A(j,"transformErrorResponse");const{rawErrorResponseSchema:U,errorResponseSchema:Z}=j;let{value:te,meta:W}=D;try{U&&!N&&(te=await ve(U,te,"rawErrorResponseSchema",W)),Q&&!N&&(W=await ve(Q,W,"metaSchema",W));let Y=await H(te,W,c.originalArgs);return Z&&!N&&(Y=await ve(Z,Y,"errorResponseSchema",W)),g(Y,Me({baseQueryMeta:W}))}catch(Y){D=Y}}try{if(D instanceof An){const H={endpoint:c.endpointName,arg:c.originalArgs,type:c.type,queryCacheKey:c.type==="query"?c.queryCacheKey:void 0};(V=j.onSchemaFailure)==null||V.call(j,D,H),S==null||S(D,H);const{catchSchemaFailure:U=I}=j;if(U)return g(U(D,H),Me({baseQueryMeta:D._bqMeta}))}}catch(H){D=H}throw console.error(D),D}};function x(c,d){const h=a.selectQueryEntry(d,c.queryCacheKey),g=a.selectConfig(d).refetchOnMountOrArgChange,T=h==null?void 0:h.fulfilledTimeStamp,$=c.forceRefetch??(c.subscribe&&g);return $?$===!0||(Number(new Date)-Number(T))/1e3>=$:!1}const k=()=>Pr(`${e}/executeQuery`,R,{getPendingMeta({arg:d}){const h=r[d.endpointName];return Me({startedTimeStamp:Date.now(),...Tt(h)?{direction:d.direction}:{}})},condition(d,{getState:h}){var N;const g=h(),T=a.selectQueryEntry(g,d.queryCacheKey),$=T==null?void 0:T.fulfilledTimeStamp,q=d.originalArgs,M=T==null?void 0:T.originalArgs,j=r[d.endpointName],Q=d.direction;return Xt(d)?!0:(T==null?void 0:T.status)==="pending"?!1:x(d,g)||kt(j)&&((N=j==null?void 0:j.forceRefetch)!=null&&N.call(j,{currentArg:q,previousArg:M,endpointState:T,state:g}))?!0:!($&&!Q)},dispatchConditionRejection:!0}),p=k(),i=k(),u=Pr(`${e}/executeMutation`,R,{getPendingMeta(){return Me({startedTimeStamp:Date.now()})}}),y=c=>"force"in c,v=c=>"ifOlderThan"in c,E=(c,d,h)=>(g,T)=>{const $=y(h)&&h.force,q=v(h)&&h.ifOlderThan,M=(Q=!0)=>{const N={forceRefetch:Q,isPrefetch:!0};return s.endpoints[c].initiate(d,N)},j=s.endpoints[c].select(d)(T());if($)g(M());else if(q){const Q=j==null?void 0:j.fulfilledTimeStamp;if(!Q){g(M());return}(Number(new Date)-Number(new Date(Q)))/1e3>=q&&g(M())}else g(M(!1))};function m(c){return d=>{var h,g;return((g=(h=d==null?void 0:d.meta)==null?void 0:h.arg)==null?void 0:g.endpointName)===c}}function l(c,d){return{matchPending:Nt(dn(c),m(d)),matchFulfilled:Nt(xe(c),m(d)),matchRejected:Nt(fr(c),m(d))}}return{queryThunk:p,mutationThunk:u,infiniteQueryThunk:i,prefetch:E,updateQueryData:f,upsertQueryData:P,patchQueryData:C,buildMatchThunkActions:l}}function er(e,{pages:t,pageParams:r},n){const s=t.length-1;return e.getNextPageParam(t[s],t,r[s],r,n)}function Cn(e,{pages:t,pageParams:r},n){var s;return(s=e.getPreviousPageParam)==null?void 0:s.call(e,t[0],t,r[0],r,n)}function kn(e,t,r,n){return gr(r[e.meta.arg.endpointName][t],xe(e)?e.payload:void 0,dr(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,n)}function et(e,t,r){const n=e[t];n&&r(n)}function He(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function Dr(e,t,r){const n=e[He(t)];n&&r(n)}var tt={};function ji({reducerPath:e,queryThunk:t,mutationThunk:r,serializeQueryArgs:n,context:{endpointDefinitions:s,apiUid:o,extractRehydrationInfo:a,hasRehydrationInfo:S},assertTagType:I,config:w}){const C=Oe(`${e}/resetApiState`);function _(m,l,c,d){var h;m[h=l.queryCacheKey]??(m[h]={status:"uninitialized",endpointName:l.endpointName}),et(m,l.queryCacheKey,g=>{g.status="pending",g.requestId=c&&g.requestId?g.requestId:d.requestId,l.originalArgs!==void 0&&(g.originalArgs=l.originalArgs),g.startedTimeStamp=d.startedTimeStamp;const T=s[d.arg.endpointName];Tt(T)&&"direction"in l&&(g.direction=l.direction)})}function b(m,l,c,d){et(m,l.arg.queryCacheKey,h=>{if(h.requestId!==l.requestId&&!d)return;const{merge:g}=s[l.arg.endpointName];if(h.status="fulfilled",g)if(h.data!==void 0){const{fulfilledTimeStamp:T,arg:$,baseQueryMeta:q,requestId:M}=l;let j=lr(h.data,Q=>g(Q,c,{arg:$.originalArgs,baseQueryMeta:q,fulfilledTimeStamp:T,requestId:M}));h.data=j}else h.data=c;else h.data=s[l.arg.endpointName].structuralSharing??!0?Rn(Es(h.data)?Rs(h.data):h.data,c):c;delete h.error,h.fulfilledTimeStamp=l.fulfilledTimeStamp})}const f=me({name:`${e}/queries`,initialState:tt,reducers:{removeQueryResult:{reducer(m,{payload:{queryCacheKey:l}}){delete m[l]},prepare:De()},cacheEntriesUpserted:{reducer(m,l){for(const c of l.payload){const{queryDescription:d,value:h}=c;_(m,d,!0,{arg:d,requestId:l.meta.requestId,startedTimeStamp:l.meta.timestamp}),b(m,{arg:d,requestId:l.meta.requestId,fulfilledTimeStamp:l.meta.timestamp,baseQueryMeta:{}},h,!0)}},prepare:m=>({payload:m.map(d=>{const{endpointName:h,arg:g,value:T}=d,$=s[h];return{queryDescription:{type:"query",endpointName:h,originalArgs:d.arg,queryCacheKey:n({queryArgs:g,endpointDefinition:$,endpointName:h})},value:T}}),meta:{[ln]:!0,requestId:an(),timestamp:Date.now()}})},queryResultPatched:{reducer(m,{payload:{queryCacheKey:l,patches:c}}){et(m,l,d=>{d.data=Er(d.data,c.concat())})},prepare:De()}},extraReducers(m){m.addCase(t.pending,(l,{meta:c,meta:{arg:d}})=>{const h=Xt(d);_(l,d,h,c)}).addCase(t.fulfilled,(l,{meta:c,payload:d})=>{const h=Xt(c.arg);b(l,c,d,h)}).addCase(t.rejected,(l,{meta:{condition:c,arg:d,requestId:h},error:g,payload:T})=>{et(l,d.queryCacheKey,$=>{if(!c){if($.requestId!==h)return;$.status="rejected",$.error=T??g}})}).addMatcher(S,(l,c)=>{const{queries:d}=a(c);for(const[h,g]of Object.entries(d))((g==null?void 0:g.status)==="fulfilled"||(g==null?void 0:g.status)==="rejected")&&(l[h]=g)})}}),P=me({name:`${e}/mutations`,initialState:tt,reducers:{removeMutationResult:{reducer(m,{payload:l}){const c=He(l);c in m&&delete m[c]},prepare:De()}},extraReducers(m){m.addCase(r.pending,(l,{meta:c,meta:{requestId:d,arg:h,startedTimeStamp:g}})=>{h.track&&(l[He(c)]={requestId:d,status:"pending",endpointName:h.endpointName,startedTimeStamp:g})}).addCase(r.fulfilled,(l,{payload:c,meta:d})=>{d.arg.track&&Dr(l,d,h=>{h.requestId===d.requestId&&(h.status="fulfilled",h.data=c,h.fulfilledTimeStamp=d.fulfilledTimeStamp)})}).addCase(r.rejected,(l,{payload:c,error:d,meta:h})=>{h.arg.track&&Dr(l,h,g=>{g.requestId===h.requestId&&(g.status="rejected",g.error=c??d)})}).addMatcher(S,(l,c)=>{const{mutations:d}=a(c);for(const[h,g]of Object.entries(d))((g==null?void 0:g.status)==="fulfilled"||(g==null?void 0:g.status)==="rejected")&&h!==(g==null?void 0:g.requestId)&&(l[h]=g)})}}),A={tags:{},keys:{}},R=me({name:`${e}/invalidation`,initialState:A,reducers:{updateProvidedBy:{reducer(m,l){var c,d,h;for(const{queryCacheKey:g,providedTags:T}of l.payload){x(m,g);for(const{type:$,id:q}of T){const M=(d=(c=m.tags)[$]??(c[$]={}))[h=q||"__internal_without_id"]??(d[h]=[]);M.includes(g)||M.push(g)}m.keys[g]=T}},prepare:De()}},extraReducers(m){m.addCase(f.actions.removeQueryResult,(l,{payload:{queryCacheKey:c}})=>{x(l,c)}).addMatcher(S,(l,c)=>{var h,g,T;const{provided:d}=a(c);for(const[$,q]of Object.entries(d))for(const[M,j]of Object.entries(q)){const Q=(g=(h=l.tags)[$]??(h[$]={}))[T=M||"__internal_without_id"]??(g[T]=[]);for(const N of j)Q.includes(N)||Q.push(N)}}).addMatcher(ft(xe(t),dr(t)),(l,c)=>{k(l,[c])}).addMatcher(f.actions.cacheEntriesUpserted.match,(l,c)=>{const d=c.payload.map(({queryDescription:h,value:g})=>({type:"UNKNOWN",payload:g,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:h}}));k(l,d)})}});function x(m,l){var d;const c=m.keys[l]??[];for(const h of c){const g=h.type,T=h.id??"__internal_without_id",$=(d=m.tags[g])==null?void 0:d[T];$&&(m.tags[g][T]=$.filter(q=>q!==l))}delete m.keys[l]}function k(m,l){const c=l.map(d=>{const h=kn(d,"providesTags",s,I),{queryCacheKey:g}=d.meta.arg;return{queryCacheKey:g,providedTags:h}});R.caseReducers.updateProvidedBy(m,R.actions.updateProvidedBy(c))}const p=me({name:`${e}/subscriptions`,initialState:tt,reducers:{updateSubscriptionOptions(m,l){},unsubscribeQueryResult(m,l){},internal_getRTKQSubscriptions(){}}}),i=me({name:`${e}/internalSubscriptions`,initialState:tt,reducers:{subscriptionsUpdated:{reducer(m,l){return Er(m,l.payload)},prepare:De()}}}),u=me({name:`${e}/config`,initialState:{online:yi(),focused:mi(),middlewareRegistered:!1,...w},reducers:{middlewareRegistered(m,{payload:l}){m.middlewareRegistered=m.middlewareRegistered==="conflict"||o!==l?"conflict":!0}},extraReducers:m=>{m.addCase(Ct,l=>{l.online=!0}).addCase(hr,l=>{l.online=!1}).addCase(At,l=>{l.focused=!0}).addCase(pr,l=>{l.focused=!1}).addMatcher(S,l=>({...l}))}}),y=Is({queries:f.reducer,mutations:P.reducer,provided:R.reducer,subscriptions:i.reducer,config:u.reducer}),v=(m,l)=>y(C.match(l)?void 0:m,l),E={...u.actions,...f.actions,...p.actions,...i.actions,...P.actions,...R.actions,resetApiState:C};return{reducer:v,actions:E}}var oe=Symbol.for("RTKQ/skipToken"),Tn={status:"uninitialized"},Mr=lr(Tn,()=>{}),Qr=lr(Tn,()=>{});function Oi({serializeQueryArgs:e,reducerPath:t,createSelector:r}){const n=p=>Mr,s=p=>Qr;return{buildQuerySelector:b,buildInfiniteQuerySelector:f,buildMutationSelector:P,selectInvalidatedBy:A,selectCachedArgsForQuery:R,selectApiState:a,selectQueries:S,selectMutations:w,selectQueryEntry:I,selectConfig:C};function o(p){return{...p,...kr(p.status)}}function a(p){return p[t]}function S(p){var i;return(i=a(p))==null?void 0:i.queries}function I(p,i){var u;return(u=S(p))==null?void 0:u[i]}function w(p){var i;return(i=a(p))==null?void 0:i.mutations}function C(p){var i;return(i=a(p))==null?void 0:i.config}function _(p,i,u){return y=>{if(y===oe)return r(n,u);const v=e({queryArgs:y,endpointDefinition:i,endpointName:p});return r(m=>I(m,v)??Mr,u)}}function b(p,i){return _(p,i,o)}function f(p,i){const{infiniteQueryOptions:u}=i;function y(v){const E={...v,...kr(v.status)},{isLoading:m,isError:l,direction:c}=E,d=c==="forward",h=c==="backward";return{...E,hasNextPage:x(u,E.data,E.originalArgs),hasPreviousPage:k(u,E.data,E.originalArgs),isFetchingNextPage:m&&d,isFetchingPreviousPage:m&&h,isFetchNextPageError:l&&d,isFetchPreviousPageError:l&&h}}return _(p,i,y)}function P(){return p=>{let i;return typeof p=="object"?i=He(p)??oe:i=p,r(i===oe?s:v=>{var E,m;return((m=(E=a(v))==null?void 0:E.mutations)==null?void 0:m[i])??Qr},o)}}function A(p,i){const u=p[t],y=new Set;for(const v of i.filter(pt).map(Zt)){const E=u.provided.tags[v.type];if(!E)continue;let m=(v.id!==void 0?E[v.id]:jr(Object.values(E)))??[];for(const l of m)y.add(l)}return jr(Array.from(y.values()).map(v=>{const E=u.queries[v];return E?[{queryCacheKey:v,endpointName:E.endpointName,originalArgs:E.originalArgs}]:[]}))}function R(p,i){return Object.values(S(p)).filter(u=>(u==null?void 0:u.endpointName)===i&&u.status!=="uninitialized").map(u=>u.originalArgs)}function x(p,i,u){return i?er(p,i,u)!=null:!1}function k(p,i,u){return!i||!p.getPreviousPageParam?!1:Cn(p,i,u)!=null}}var _e=WeakMap?new WeakMap:void 0,gt=({endpointName:e,queryArgs:t})=>{let r="";const n=_e==null?void 0:_e.get(t);if(typeof n=="string")r=n;else{const s=JSON.stringify(t,(o,a)=>(a=typeof a=="bigint"?{$bigint:a.toString()}:a,a=Ue(a)?Object.keys(a).sort().reduce((S,I)=>(S[I]=a[I],S),{}):a,a));Ue(t)&&(_e==null||_e.set(t,s)),r=s}return`${e}(${r})`};function jn(...e){return function(r){const n=_r(w=>{var C;return(C=r.extractRehydrationInfo)==null?void 0:C.call(r,w,{reducerPath:r.reducerPath??"api"})}),s={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...r,extractRehydrationInfo:n,serializeQueryArgs(w){let C=gt;if("serializeQueryArgs"in w.endpointDefinition){const _=w.endpointDefinition.serializeQueryArgs;C=b=>{const f=_(b);return typeof f=="string"?f:gt({...b,queryArgs:f})}}else r.serializeQueryArgs&&(C=r.serializeQueryArgs);return C(w)},tagTypes:[...r.tagTypes||[]]},o={endpointDefinitions:{},batch(w){w()},apiUid:an(),extractRehydrationInfo:n,hasRehydrationInfo:_r(w=>n(w)!=null)},a={injectEndpoints:I,enhanceEndpoints({addTagTypes:w,endpoints:C}){if(w)for(const _ of w)s.tagTypes.includes(_)||s.tagTypes.push(_);if(C)for(const[_,b]of Object.entries(C))typeof b=="function"?b(o.endpointDefinitions[_]):Object.assign(o.endpointDefinitions[_]||{},b);return a}},S=e.map(w=>w.init(a,s,o));function I(w){const C=w.endpoints({query:_=>({..._,type:"query"}),mutation:_=>({..._,type:"mutation"}),infiniteQuery:_=>({..._,type:"infinitequery"})});for(const[_,b]of Object.entries(C)){if(w.overrideExisting!==!0&&_ in o.endpointDefinitions){if(w.overrideExisting==="throw")throw new Error(cn(39));continue}o.endpointDefinitions[_]=b;for(const f of S)f.injectEndpoint(_,b)}return a}return a.injectEndpoints({endpoints:r.endpoints})}}function fe(e,...t){return Object.assign(e,...t)}var $i=({api:e,queryThunk:t,internalState:r})=>{const n=`${e.reducerPath}/subscriptions`;let s=null,o=null;const{updateSubscriptionOptions:a,unsubscribeQueryResult:S}=e.internalActions,I=(f,P)=>{var R,x,k;if(a.match(P)){const{queryCacheKey:p,requestId:i,options:u}=P.payload;return(R=f==null?void 0:f[p])!=null&&R[i]&&(f[p][i]=u),!0}if(S.match(P)){const{queryCacheKey:p,requestId:i}=P.payload;return f[p]&&delete f[p][i],!0}if(e.internalActions.removeQueryResult.match(P))return delete f[P.payload.queryCacheKey],!0;if(t.pending.match(P)){const{meta:{arg:p,requestId:i}}=P,u=f[x=p.queryCacheKey]??(f[x]={});return u[`${i}_running`]={},p.subscribe&&(u[i]=p.subscriptionOptions??u[i]??{}),!0}let A=!1;if(t.fulfilled.match(P)||t.rejected.match(P)){const p=f[P.meta.arg.queryCacheKey]||{},i=`${P.meta.requestId}_running`;A||(A=!!p[i]),delete p[i]}if(t.rejected.match(P)){const{meta:{condition:p,arg:i,requestId:u}}=P;if(p&&i.subscribe){const y=f[k=i.queryCacheKey]??(f[k]={});y[u]=i.subscriptionOptions??y[u]??{},A=!0}}return A},w=()=>r.currentSubscriptions,b={getSubscriptions:w,getSubscriptionCount:f=>{const A=w()[f]??{};return Re(A)},isRequestSubscribed:(f,P)=>{var R;const A=w();return!!((R=A==null?void 0:A[f])!=null&&R[P])}};return(f,P)=>{if(s||(s=JSON.parse(JSON.stringify(r.currentSubscriptions))),e.util.resetApiState.match(f))return s=r.currentSubscriptions={},o=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(f))return[!1,b];const A=I(r.currentSubscriptions,f);let R=!0;if(A){o||(o=setTimeout(()=>{const p=JSON.parse(JSON.stringify(r.currentSubscriptions)),[,i]=fn(s,()=>p);P.next(e.internalActions.subscriptionsUpdated(i)),s=p,o=null},500));const x=typeof f.type=="string"&&!!f.type.startsWith(n),k=t.rejected.match(f)&&f.meta.condition&&!!f.meta.arg.subscribe;R=!x&&!k}return[R,!1]}};function qi(e){for(const t in e)return!1;return!0}var Di=2147483647/1e3-1,Mi=({reducerPath:e,api:t,queryThunk:r,context:n,internalState:s,selectors:{selectQueryEntry:o,selectConfig:a}})=>{const{removeQueryResult:S,unsubscribeQueryResult:I,cacheEntriesUpserted:w}=t.internalActions,C=ft(I.match,r.fulfilled,r.rejected,w.match);function _(R){const x=s.currentSubscriptions[R];return!!x&&!qi(x)}const b={},f=(R,x,k)=>{const p=x.getState(),i=a(p);if(C(R)){let u;if(w.match(R))u=R.payload.map(y=>y.queryDescription.queryCacheKey);else{const{queryCacheKey:y}=I.match(R)?R.payload:R.meta.arg;u=[y]}P(u,x,i)}if(t.util.resetApiState.match(R))for(const[u,y]of Object.entries(b))y&&clearTimeout(y),delete b[u];if(n.hasRehydrationInfo(R)){const{queries:u}=n.extractRehydrationInfo(R);P(Object.keys(u),x,i)}};function P(R,x,k){const p=x.getState();for(const i of R){const u=o(p,i);A(i,u==null?void 0:u.endpointName,x,k)}}function A(R,x,k,p){const i=n.endpointDefinitions[x],u=(i==null?void 0:i.keepUnusedDataFor)??p.keepUnusedDataFor;if(u===1/0)return;const y=Math.max(0,Math.min(u,Di));if(!_(R)){const v=b[R];v&&clearTimeout(v),b[R]=setTimeout(()=>{_(R)||k.dispatch(S({queryCacheKey:R})),delete b[R]},y*1e3)}}return f},Lr=new Error("Promise never resolved before cacheEntryRemoved."),Qi=({api:e,reducerPath:t,context:r,queryThunk:n,mutationThunk:s,internalState:o,selectors:{selectQueryEntry:a,selectApiState:S}})=>{const I=Rr(n),w=Rr(s),C=xe(n,s),_={};function b(x,k,p){const i=_[x];i!=null&&i.valueResolved&&(i.valueResolved({data:k,meta:p}),delete i.valueResolved)}function f(x){const k=_[x];k&&(delete _[x],k.cacheEntryRemoved())}const P=(x,k,p)=>{const i=A(x);function u(y,v,E,m){const l=a(p,v),c=a(k.getState(),v);!l&&c&&R(y,m,v,k,E)}if(n.pending.match(x))u(x.meta.arg.endpointName,i,x.meta.requestId,x.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(x))for(const{queryDescription:y,value:v}of x.payload){const{endpointName:E,originalArgs:m,queryCacheKey:l}=y;u(E,l,x.meta.requestId,m),b(l,v,{})}else if(s.pending.match(x))k.getState()[t].mutations[i]&&R(x.meta.arg.endpointName,x.meta.arg.originalArgs,i,k,x.meta.requestId);else if(C(x))b(i,x.payload,x.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(x)||e.internalActions.removeMutationResult.match(x))f(i);else if(e.util.resetApiState.match(x))for(const y of Object.keys(_))f(y)};function A(x){return I(x)?x.meta.arg.queryCacheKey:w(x)?x.meta.arg.fixedCacheKey??x.meta.requestId:e.internalActions.removeQueryResult.match(x)?x.payload.queryCacheKey:e.internalActions.removeMutationResult.match(x)?He(x.payload):""}function R(x,k,p,i,u){const y=r.endpointDefinitions[x],v=y==null?void 0:y.onCacheEntryAdded;if(!v)return;const E={},m=new Promise(T=>{E.cacheEntryRemoved=T}),l=Promise.race([new Promise(T=>{E.valueResolved=T}),m.then(()=>{throw Lr})]);l.catch(()=>{}),_[p]=E;const c=e.endpoints[x].select(ht(y)?k:p),d=i.dispatch((T,$,q)=>q),h={...i,getCacheEntry:()=>c(i.getState()),requestId:u,extra:d,updateCachedData:ht(y)?T=>i.dispatch(e.util.updateQueryData(x,k,T)):void 0,cacheDataLoaded:l,cacheEntryRemoved:m},g=v(k,h);Promise.resolve(g).catch(T=>{if(T!==Lr)throw T})}return P},Li=({api:e,context:{apiUid:t},reducerPath:r})=>(n,s)=>{e.util.resetApiState.match(n)&&s.dispatch(e.internalActions.middlewareRegistered(t))},Ni=({reducerPath:e,context:t,context:{endpointDefinitions:r},mutationThunk:n,queryThunk:s,api:o,assertTagType:a,refetchQuery:S,internalState:I})=>{const{removeQueryResult:w}=o.internalActions,C=ft(xe(n),dr(n)),_=ft(xe(n,s),fr(n,s));let b=[];const f=(R,x)=>{C(R)?A(kn(R,"invalidatesTags",r,a),x):_(R)?A([],x):o.util.invalidateTags.match(R)&&A(gr(R.payload,void 0,void 0,void 0,void 0,a),x)};function P(R){var p;const{queries:x,mutations:k}=R;for(const i of[x,k])for(const u in i)if(((p=i[u])==null?void 0:p.status)==="pending")return!0;return!1}function A(R,x){const k=x.getState(),p=k[e];if(b.push(...R),p.config.invalidationBehavior==="delayed"&&P(p))return;const i=b;if(b=[],i.length===0)return;const u=o.util.selectInvalidatedBy(k,i);t.batch(()=>{const y=Array.from(u.values());for(const{queryCacheKey:v}of y){const E=p.queries[v],m=I.currentSubscriptions[v]??{};E&&(Re(m)===0?x.dispatch(w({queryCacheKey:v})):E.status!=="uninitialized"&&x.dispatch(S(E)))}})}return f},Fi=({reducerPath:e,queryThunk:t,api:r,refetchQuery:n,internalState:s})=>{const o={},a=(b,f)=>{(r.internalActions.updateSubscriptionOptions.match(b)||r.internalActions.unsubscribeQueryResult.match(b))&&I(b.payload,f),(t.pending.match(b)||t.rejected.match(b)&&b.meta.condition)&&I(b.meta.arg,f),(t.fulfilled.match(b)||t.rejected.match(b)&&!b.meta.condition)&&S(b.meta.arg,f),r.util.resetApiState.match(b)&&C()};function S({queryCacheKey:b},f){const P=f.getState()[e],A=P.queries[b],R=s.currentSubscriptions[b];if(!A||A.status==="uninitialized")return;const{lowestPollingInterval:x,skipPollingIfUnfocused:k}=_(R);if(!Number.isFinite(x))return;const p=o[b];p!=null&&p.timeout&&(clearTimeout(p.timeout),p.timeout=void 0);const i=Date.now()+x;o[b]={nextPollTimestamp:i,pollingInterval:x,timeout:setTimeout(()=>{(P.config.focused||!k)&&f.dispatch(n(A)),S({queryCacheKey:b},f)},x)}}function I({queryCacheKey:b},f){const A=f.getState()[e].queries[b],R=s.currentSubscriptions[b];if(!A||A.status==="uninitialized")return;const{lowestPollingInterval:x}=_(R);if(!Number.isFinite(x)){w(b);return}const k=o[b],p=Date.now()+x;(!k||p<k.nextPollTimestamp)&&S({queryCacheKey:b},f)}function w(b){const f=o[b];f!=null&&f.timeout&&clearTimeout(f.timeout),delete o[b]}function C(){for(const b of Object.keys(o))w(b)}function _(b={}){let f=!1,P=Number.POSITIVE_INFINITY;for(let A in b)b[A].pollingInterval&&(P=Math.min(b[A].pollingInterval,P),f=b[A].skipPollingIfUnfocused||f);return{lowestPollingInterval:P,skipPollingIfUnfocused:f}}return a},zi=({api:e,context:t,queryThunk:r,mutationThunk:n})=>{const s=dn(r,n),o=fr(r,n),a=xe(r,n),S={};return(w,C)=>{var _,b;if(s(w)){const{requestId:f,arg:{endpointName:P,originalArgs:A}}=w.meta,R=t.endpointDefinitions[P],x=R==null?void 0:R.onQueryStarted;if(x){const k={},p=new Promise((v,E)=>{k.resolve=v,k.reject=E});p.catch(()=>{}),S[f]=k;const i=e.endpoints[P].select(ht(R)?A:f),u=C.dispatch((v,E,m)=>m),y={...C,getCacheEntry:()=>i(C.getState()),requestId:f,extra:u,updateCachedData:ht(R)?v=>C.dispatch(e.util.updateQueryData(P,A,v)):void 0,queryFulfilled:p};x(A,y)}}else if(a(w)){const{requestId:f,baseQueryMeta:P}=w.meta;(_=S[f])==null||_.resolve({data:w.payload,meta:P}),delete S[f]}else if(o(w)){const{requestId:f,rejectedWithValue:P,baseQueryMeta:A}=w.meta;(b=S[f])==null||b.reject({error:w.payload??w.error,isUnhandledError:!P,meta:A}),delete S[f]}}},Ui=({reducerPath:e,context:t,api:r,refetchQuery:n,internalState:s})=>{const{removeQueryResult:o}=r.internalActions,a=(I,w)=>{At.match(I)&&S(w,"refetchOnFocus"),Ct.match(I)&&S(w,"refetchOnReconnect")};function S(I,w){const C=I.getState()[e],_=C.queries,b=s.currentSubscriptions;t.batch(()=>{for(const f of Object.keys(b)){const P=_[f],A=b[f];if(!A||!P)continue;(Object.values(A).some(x=>x[w]===!0)||Object.values(A).every(x=>x[w]===void 0)&&C.config[w])&&(Re(A)===0?I.dispatch(o({queryCacheKey:f})):P.status!=="uninitialized"&&I.dispatch(n(P)))}})}return a};function Bi(e){const{reducerPath:t,queryThunk:r,api:n,context:s}=e,{apiUid:o}=s,a={invalidateTags:Oe(`${t}/invalidateTags`)},S=_=>_.type.startsWith(`${t}/`),I=[Li,Mi,Ni,Fi,Qi,zi];return{middleware:_=>{let b=!1;const P={...e,internalState:{currentSubscriptions:{}},refetchQuery:C,isThisApiSliceAction:S},A=I.map(k=>k(P)),R=$i(P),x=Ui(P);return k=>p=>{if(!Ps(p))return k(p);b||(b=!0,_.dispatch(n.internalActions.middlewareRegistered(o)));const i={..._,next:k},u=_.getState(),[y,v]=R(p,i,u);let E;if(y?E=k(p):E=v,_.getState()[t]&&(x(p,i,u),S(p)||s.hasRehydrationInfo(p)))for(const m of A)m(p,i,u);return E}},actions:a};function C(_){return e.api.endpoints[_.endpointName].initiate(_.originalArgs,{subscribe:!1,forceRefetch:!0})}}var Nr=Symbol(),On=({createSelector:e=un}={})=>({name:Nr,init(t,{baseQuery:r,tagTypes:n,reducerPath:s,serializeQueryArgs:o,keepUnusedDataFor:a,refetchOnMountOrArgChange:S,refetchOnFocus:I,refetchOnReconnect:w,invalidationBehavior:C,onSchemaFailure:_,catchSchemaFailure:b,skipSchemaValidation:f},P){ws();const A=D=>D;Object.assign(t,{reducerPath:s,endpoints:{},internalActions:{onOnline:Ct,onOffline:hr,onFocus:At,onFocusLost:pr},util:{}});const R=Oi({serializeQueryArgs:o,reducerPath:s,createSelector:e}),{selectInvalidatedBy:x,selectCachedArgsForQuery:k,buildQuerySelector:p,buildInfiniteQuerySelector:i,buildMutationSelector:u}=R;fe(t.util,{selectInvalidatedBy:x,selectCachedArgsForQuery:k});const{queryThunk:y,infiniteQueryThunk:v,mutationThunk:E,patchQueryData:m,updateQueryData:l,upsertQueryData:c,prefetch:d,buildMatchThunkActions:h}=Ti({baseQuery:r,reducerPath:s,context:P,api:t,serializeQueryArgs:o,assertTagType:A,selectors:R,onSchemaFailure:_,catchSchemaFailure:b,skipSchemaValidation:f}),{reducer:g,actions:T}=ji({context:P,queryThunk:y,mutationThunk:E,serializeQueryArgs:o,reducerPath:s,assertTagType:A,config:{refetchOnFocus:I,refetchOnReconnect:w,refetchOnMountOrArgChange:S,keepUnusedDataFor:a,reducerPath:s,invalidationBehavior:C}});fe(t.util,{patchQueryData:m,updateQueryData:l,upsertQueryData:c,prefetch:d,resetApiState:T.resetApiState,upsertQueryEntries:T.cacheEntriesUpserted}),fe(t.internalActions,T);const{middleware:$,actions:q}=Bi({reducerPath:s,context:P,queryThunk:y,mutationThunk:E,infiniteQueryThunk:v,api:t,assertTagType:A,selectors:R});fe(t.util,q),fe(t,{reducer:g,middleware:$});const{buildInitiateQuery:M,buildInitiateInfiniteQuery:j,buildInitiateMutation:Q,getRunningMutationThunk:N,getRunningMutationsThunk:z,getRunningQueriesThunk:V,getRunningQueryThunk:B}=Ci({queryThunk:y,mutationThunk:E,infiniteQueryThunk:v,api:t,serializeQueryArgs:o,context:P});return fe(t.util,{getRunningMutationThunk:N,getRunningMutationsThunk:z,getRunningQueryThunk:B,getRunningQueriesThunk:V}),{name:Nr,injectEndpoint(D,H){var te;const Z=(te=t.endpoints)[D]??(te[D]={});kt(H)&&fe(Z,{name:D,select:p(D,H),initiate:M(D,H)},h(y,D)),Ei(H)&&fe(Z,{name:D,select:u(),initiate:Q(D)},h(E,D)),Tt(H)&&fe(Z,{name:D,select:i(D,H),initiate:j(D,H)},h(y,D))}}}});On();function rt(e){return e.replace(e[0],e[0].toUpperCase())}function Yi(e){return e.type==="query"}function Hi(e){return e.type==="mutation"}function $n(e){return e.type==="infinitequery"}function Qe(e,...t){return Object.assign(e,...t)}var zt=Symbol();function Ut(e,t,r,n){const s=L.useMemo(()=>({queryArgs:e,serialized:typeof e=="object"?t({queryArgs:e,endpointDefinition:r,endpointName:n}):e}),[e,t,r,n]),o=L.useRef(s);return L.useEffect(()=>{o.current.serialized!==s.serialized&&(o.current=s)},[s]),o.current.serialized===s.serialized?o.current.queryArgs:e}function nt(e){const t=L.useRef(e);return L.useEffect(()=>{Fe(t.current,e)||(t.current=e)},[e]),Fe(t.current,e)?t.current:e}var Ki=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Wi=Ki(),Vi=()=>typeof navigator<"u"&&navigator.product==="ReactNative",Gi=Vi(),Ji=()=>Wi||Gi?L.useLayoutEffect:L.useEffect,Zi=Ji(),Fr=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:En.pending}:e;function Bt(e,...t){const r={};return t.forEach(n=>{r[n]=e[n]}),r}var Yt=["data","status","isLoading","isSuccess","isError","error"];function Xi({api:e,moduleOptions:{batch:t,hooks:{useDispatch:r,useSelector:n,useStore:s},unstable__sideEffectsInRender:o,createSelector:a},serializeQueryArgs:S,context:I}){const w=o?i=>i():L.useEffect;return{buildQueryHooks:x,buildInfiniteQueryHooks:k,buildMutationHook:p,usePrefetch:b};function C(i,u,y){if(u!=null&&u.endpointName&&i.isUninitialized){const{endpointName:d}=u,h=I.endpointDefinitions[d];y!==oe&&S({queryArgs:u.originalArgs,endpointDefinition:h,endpointName:d})===S({queryArgs:y,endpointDefinition:h,endpointName:d})&&(u=void 0)}let v=i.isSuccess?i.data:u==null?void 0:u.data;v===void 0&&(v=i.data);const E=v!==void 0,m=i.isLoading,l=(!u||u.isLoading||u.isUninitialized)&&!E&&m,c=i.isSuccess||E&&(m&&!(u!=null&&u.isError)||i.isUninitialized);return{...i,data:v,currentData:i.data,isFetching:m,isLoading:l,isSuccess:c}}function _(i,u,y){if(u!=null&&u.endpointName&&i.isUninitialized){const{endpointName:d}=u,h=I.endpointDefinitions[d];y!==oe&&S({queryArgs:u.originalArgs,endpointDefinition:h,endpointName:d})===S({queryArgs:y,endpointDefinition:h,endpointName:d})&&(u=void 0)}let v=i.isSuccess?i.data:u==null?void 0:u.data;v===void 0&&(v=i.data);const E=v!==void 0,m=i.isLoading,l=(!u||u.isLoading||u.isUninitialized)&&!E&&m,c=i.isSuccess||m&&E;return{...i,data:v,currentData:i.data,isFetching:m,isLoading:l,isSuccess:c}}function b(i,u){const y=r(),v=nt(u);return L.useCallback((E,m)=>y(e.util.prefetch(i,E,{...v,...m})),[i,y,v])}function f(i,u,{refetchOnReconnect:y,refetchOnFocus:v,refetchOnMountOrArgChange:E,skip:m=!1,pollingInterval:l=0,skipPollingIfUnfocused:c=!1,...d}={}){const{initiate:h}=e.endpoints[i],g=r(),T=L.useRef(void 0);if(!T.current){const D=g(e.internalActions.internal_getRTKQSubscriptions());T.current=D}const $=Ut(m?oe:u,gt,I.endpointDefinitions[i],i),q=nt({refetchOnReconnect:y,refetchOnFocus:v,pollingInterval:l,skipPollingIfUnfocused:c}),M=d.initialPageParam,j=nt(M),Q=L.useRef(void 0);let{queryCacheKey:N,requestId:z}=Q.current||{},V=!1;N&&z&&(V=T.current.isRequestSubscribed(N,z));const B=!V&&Q.current!==void 0;return w(()=>{B&&(Q.current=void 0)},[B]),w(()=>{var U;const D=Q.current;if($===oe){D==null||D.unsubscribe(),Q.current=void 0;return}const H=(U=Q.current)==null?void 0:U.subscriptionOptions;if(!D||D.arg!==$){D==null||D.unsubscribe();const Z=g(h($,{subscriptionOptions:q,forceRefetch:E,...$n(I.endpointDefinitions[i])?{initialPageParam:j}:{}}));Q.current=Z}else q!==H&&D.updateSubscriptionOptions(q)},[g,h,E,$,q,B,j,i]),[Q,g,h,q]}function P(i,u){return(v,{skip:E=!1,selectFromResult:m}={})=>{const{select:l}=e.endpoints[i],c=Ut(E?oe:v,S,I.endpointDefinitions[i],i),d=L.useRef(void 0),h=L.useMemo(()=>a([l(c),(M,j)=>j,M=>c],u,{memoizeOptions:{resultEqualityCheck:Fe}}),[l,c]),g=L.useMemo(()=>m?a([h],m,{devModeChecks:{identityFunctionCheck:"never"}}):h,[h,m]),T=n(M=>g(M,d.current),Fe),$=s(),q=h($.getState(),d.current);return Zi(()=>{d.current=q},[q]),T}}function A(i){L.useEffect(()=>()=>{var u,y;(y=(u=i.current)==null?void 0:u.unsubscribe)==null||y.call(u),i.current=void 0},[i])}function R(i){if(!i.current)throw new Error(cn(38));return i.current.refetch()}function x(i){const u=(E,m={})=>{const[l]=f(i,E,m);return A(l),L.useMemo(()=>({refetch:()=>R(l)}),[l])},y=({refetchOnReconnect:E,refetchOnFocus:m,pollingInterval:l=0,skipPollingIfUnfocused:c=!1}={})=>{const{initiate:d}=e.endpoints[i],h=r(),[g,T]=L.useState(zt),$=L.useRef(void 0),q=nt({refetchOnReconnect:E,refetchOnFocus:m,pollingInterval:l,skipPollingIfUnfocused:c});w(()=>{var z,V;const N=(z=$.current)==null?void 0:z.subscriptionOptions;q!==N&&((V=$.current)==null||V.updateSubscriptionOptions(q))},[q]);const M=L.useRef(q);w(()=>{M.current=q},[q]);const j=L.useCallback(function(N,z=!1){let V;return t(()=>{var B;(B=$.current)==null||B.unsubscribe(),$.current=V=h(d(N,{subscriptionOptions:M.current,forceRefetch:!z})),T(N)}),V},[h,d]),Q=L.useCallback(()=>{var N,z;(N=$.current)!=null&&N.queryCacheKey&&h(e.internalActions.removeQueryResult({queryCacheKey:(z=$.current)==null?void 0:z.queryCacheKey}))},[h]);return L.useEffect(()=>()=>{var N;(N=$==null?void 0:$.current)==null||N.unsubscribe()},[]),L.useEffect(()=>{g!==zt&&!$.current&&j(g,!0)},[g,j]),L.useMemo(()=>[j,g,{reset:Q}],[j,g,Q])},v=P(i,C);return{useQueryState:v,useQuerySubscription:u,useLazyQuerySubscription:y,useLazyQuery(E){const[m,l,{reset:c}]=y(E),d=v(l,{...E,skip:l===zt}),h=L.useMemo(()=>({lastArg:l}),[l]);return L.useMemo(()=>[m,{...d,reset:c},h],[m,d,c,h])},useQuery(E,m){const l=u(E,m),c=v(E,{selectFromResult:E===oe||m!=null&&m.skip?void 0:Fr,...m}),d=Bt(c,...Yt);return L.useDebugValue(d),L.useMemo(()=>({...c,...l}),[c,l])}}}function k(i){const u=(v,E={})=>{const[m,l,c,d]=f(i,v,E),h=L.useRef(d);w(()=>{h.current=d},[d]);const g=L.useCallback(function(q,M){let j;return t(()=>{var Q;(Q=m.current)==null||Q.unsubscribe(),m.current=j=l(c(q,{subscriptionOptions:h.current,direction:M}))}),j},[m,l,c]);A(m);const T=Ut(E.skip?oe:v,gt,I.endpointDefinitions[i],i),$=L.useCallback(()=>R(m),[m]);return L.useMemo(()=>({trigger:g,refetch:$,fetchNextPage:()=>g(T,"forward"),fetchPreviousPage:()=>g(T,"backward")}),[$,g,T])},y=P(i,_);return{useInfiniteQueryState:y,useInfiniteQuerySubscription:u,useInfiniteQuery(v,E){const{refetch:m,fetchNextPage:l,fetchPreviousPage:c}=u(v,E),d=y(v,{selectFromResult:v===oe||E!=null&&E.skip?void 0:Fr,...E}),h=Bt(d,...Yt,"hasNextPage","hasPreviousPage");return L.useDebugValue(h),L.useMemo(()=>({...d,fetchNextPage:l,fetchPreviousPage:c,refetch:m}),[d,l,c,m])}}}function p(i){return({selectFromResult:u,fixedCacheKey:y}={})=>{const{select:v,initiate:E}=e.endpoints[i],m=r(),[l,c]=L.useState();L.useEffect(()=>()=>{l!=null&&l.arg.fixedCacheKey||l==null||l.reset()},[l]);const d=L.useCallback(function(N){const z=m(E(N,{fixedCacheKey:y}));return c(z),z},[m,E,y]),{requestId:h}=l||{},g=L.useMemo(()=>v({fixedCacheKey:y,requestId:l==null?void 0:l.requestId}),[y,l,v]),T=L.useMemo(()=>u?a([g],u):g,[u,g]),$=n(T,Fe),q=y==null?l==null?void 0:l.arg.originalArgs:void 0,M=L.useCallback(()=>{t(()=>{l&&c(void 0),y&&m(e.internalActions.removeMutationResult({requestId:h,fixedCacheKey:y}))})},[m,y,l,h]),j=Bt($,...Yt,"endpointName");L.useDebugValue(j);const Q=L.useMemo(()=>({...$,originalArgs:q,reset:M}),[$,q,M]);return L.useMemo(()=>[d,Q],[d,Q])}}}var eo=Symbol(),to=({batch:e=As,hooks:t={useDispatch:hn,useSelector:pn,useStore:Cs},createSelector:r=un,unstable__sideEffectsInRender:n=!1,...s}={})=>({name:eo,init(o,{serializeQueryArgs:a},S){const I=o,{buildQueryHooks:w,buildInfiniteQueryHooks:C,buildMutationHook:_,usePrefetch:b}=Xi({api:o,moduleOptions:{batch:e,hooks:t,unstable__sideEffectsInRender:n,createSelector:r},serializeQueryArgs:a,context:S});return Qe(I,{usePrefetch:b}),Qe(S,{batch:e}),{injectEndpoint(f,P){if(Yi(P)){const{useQuery:A,useLazyQuery:R,useLazyQuerySubscription:x,useQueryState:k,useQuerySubscription:p}=w(f);Qe(I.endpoints[f],{useQuery:A,useLazyQuery:R,useLazyQuerySubscription:x,useQueryState:k,useQuerySubscription:p}),o[`use${rt(f)}Query`]=A,o[`useLazy${rt(f)}Query`]=R}if(Hi(P)){const A=_(f);Qe(I.endpoints[f],{useMutation:A}),o[`use${rt(f)}Mutation`]=A}else if($n(P)){const{useInfiniteQuery:A,useInfiniteQuerySubscription:R,useInfiniteQueryState:x}=C(f);Qe(I.endpoints[f],{useInfiniteQuery:A,useInfiniteQuerySubscription:R,useInfiniteQueryState:x}),o[`use${rt(f)}InfiniteQuery`]=A}}}}}),ro=jn(On(),to());const no=_i({baseUrl:"/api/admin",prepareHeaders:(e,{getState:t})=>{const r=t().auth.token;return r&&e.set("authorization",`Bearer ${r}`),e.set("content-type","application/json"),e}}),so=async(e,t,r)=>{const n=await no(e,t,r);return n.error&&n.error.status===401&&t.dispatch({type:"auth/logout"}),n},Le=ro({reducerPath:"api",baseQuery:so,tagTypes:["User","Item","Exchange","Report","Statistics"],endpoints:e=>({login:e.mutation({query:t=>({url:"/auth/login",method:"POST",body:t})}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"})}),getDashboardStats:e.query({query:()=>"/dashboard/stats",providesTags:["Statistics"]}),getDashboardCharts:e.query({query:t=>({url:"/dashboard/charts",params:t}),providesTags:["Statistics"]}),deleteUser:e.mutation({query:t=>({url:`/users/${t}`,method:"DELETE"}),invalidatesTags:["User"]}),createUser:e.mutation({query:t=>({url:"/users",method:"POST",body:t}),invalidatesTags:["User"]}),updateUser:e.mutation({query:({userId:t,data:r})=>({url:`/users/${t}`,method:"PUT",body:r}),invalidatesTags:["User"]}),getUsers:e.query({query:t=>({url:"/users",params:t}),providesTags:["User"]}),getUserById:e.query({query:t=>`/users/${t}`,providesTags:(t,r,n)=>[{type:"User",id:n}]}),updateUserStatus:e.mutation({query:({userId:t,...r})=>({url:`/users/${t}/status`,method:"PUT",body:r}),invalidatesTags:["User"]}),banUser:e.mutation({query:({userId:t,...r})=>({url:`/users/${t}/ban`,method:"POST",body:r}),invalidatesTags:["User"]}),getItems:e.query({query:t=>({url:"/items",params:t}),providesTags:["Item"]}),getItemById:e.query({query:t=>`/items/${t}`,providesTags:(t,r,n)=>[{type:"Item",id:n}]}),updateItemStatus:e.mutation({query:({itemId:t,...r})=>({url:`/items/${t}/status`,method:"PUT",body:r}),invalidatesTags:["Item"]}),updateItem:e.mutation({query:({itemId:t,data:r})=>({url:`/items/${t}`,method:"PUT",body:r,formData:!0}),invalidatesTags:["Item"]}),deleteItem:e.mutation({query:t=>({url:`/items/${t}`,method:"DELETE"}),invalidatesTags:["Item"]}),createItem:e.mutation({query:t=>({url:"/items",method:"POST",body:t,formData:!0}),invalidatesTags:["Item"]}),getExchanges:e.query({query:t=>({url:"/exchanges",params:t}),providesTags:["Exchange"]}),getExchangeById:e.query({query:t=>`/exchanges/${t}`,providesTags:(t,r,n)=>[{type:"Exchange",id:n}]}),getReports:e.query({query:t=>({url:"/reports",params:t}),providesTags:["Report"]}),handleReport:e.mutation({query:({reportId:t,...r})=>({url:`/reports/${t}/handle`,method:"PUT",body:r}),invalidatesTags:["Report"]}),getStatistics:e.query({query:t=>({url:"/statistics/overview",params:t}),providesTags:["Statistics"]}),exportStatistics:e.mutation({query:t=>({url:"/statistics/export",method:"POST",body:t,responseHandler:r=>r.blob()})}),updateExchangeStatus:e.mutation({query:({id:t,...r})=>({url:`/exchanges/${t}/status`,method:"PUT",body:r}),invalidatesTags:["Exchange"]})})}),{useLogoutMutation:io,useGetDashboardStatsQuery:La}=Le,oo={token:localStorage.getItem("admin_token"),admin:null,isAuthenticated:!1,isLoading:!1,loginError:null},qn=me({name:"auth",initialState:oo,reducers:{loginStart:e=>{e.isLoading=!0,e.loginError=null},loginSuccess:(e,t)=>{e.isLoading=!1,e.token=t.payload.token,e.admin=t.payload.admin,e.isAuthenticated=!0,e.loginError=null,localStorage.setItem("admin_token",t.payload.token)},loginFailure:(e,t)=>{e.isLoading=!1,e.token=null,e.admin=null,e.isAuthenticated=!1,e.loginError=t.payload,localStorage.removeItem("admin_token")},logout:e=>{e.token=null,e.admin=null,e.isAuthenticated=!1,e.loginError=null,localStorage.removeItem("admin_token")},updateProfile:(e,t)=>{e.admin&&(e.admin={...e.admin,...t.payload})},clearError:e=>{e.loginError=null},initializeAuth:e=>{const t=localStorage.getItem("admin_token");t&&(e.token=t)}}}),{loginStart:Na,loginSuccess:Fa,loginFailure:za,logout:Ua,updateProfile:Ba,clearError:Ya,initializeAuth:Ha}=qn.actions,ao=qn.reducer,Dn=e=>e.auth.isAuthenticated,co={sidebarCollapsed:localStorage.getItem("sidebar_collapsed")==="true",theme:localStorage.getItem("theme")||"light",loading:{global:!1},notifications:[],breadcrumb:[],pageTitle:"管理后台",tableSettings:{}},Mn=me({name:"ui",initialState:co,reducers:{toggleSidebar:e=>{e.sidebarCollapsed=!e.sidebarCollapsed,localStorage.setItem("sidebar_collapsed",e.sidebarCollapsed.toString())},setSidebarCollapsed:(e,t)=>{e.sidebarCollapsed=t.payload,localStorage.setItem("sidebar_collapsed",t.payload.toString())},setTheme:(e,t)=>{e.theme=t.payload,localStorage.setItem("theme",t.payload)},setLoading:(e,t)=>{e.loading[t.payload.key]=t.payload.loading},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},addNotification:(e,t)=>{const r={...t.payload,id:Date.now().toString()+Math.random().toString(36).substr(2,9),timestamp:Date.now()};e.notifications.unshift(r),e.notifications.length>20&&(e.notifications=e.notifications.slice(0,20))},removeNotification:(e,t)=>{e.notifications=e.notifications.filter(r=>r.id!==t.payload)},clearNotifications:e=>{e.notifications=[]},setBreadcrumb:(e,t)=>{e.breadcrumb=t.payload},setPageTitle:(e,t)=>{e.pageTitle=t.payload,document.title=`${t.payload} - 社区物品置换管理后台`},setTableSettings:(e,t)=>{const{tableKey:r,settings:n}=t.payload;e.tableSettings[r]={...e.tableSettings[r],...n}},resetTableSettings:(e,t)=>{delete e.tableSettings[t.payload]}}}),{toggleSidebar:Qn,setSidebarCollapsed:Ka,setTheme:Wa,setLoading:Va,setGlobalLoading:Ga,addNotification:Ja,removeNotification:Za,clearNotifications:Xa,setBreadcrumb:ec,setPageTitle:tc,setTableSettings:rc,resetTableSettings:nc}=Mn.actions,uo=Mn.reducer,Ln=e=>e.ui.sidebarCollapsed,sc=e=>e.ui.theme,lo=e=>e.ui.loading,mr=ks({reducer:{auth:ao,ui:uo,[Le.reducerPath]:Le.reducer},middleware:e=>e({serializableCheck:{ignoredActions:[Le.util.resetApiState.type]}}).concat(Le.middleware),devTools:!1});Pi(mr.dispatch);const Nn=()=>hn(),Ke=pn,fo="modulepreload",po=function(e){return"/"+e},zr={},ho=function(t,r,n){let s=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),S=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));s=Promise.allSettled(r.map(I=>{if(I=po(I),I in zr)return;zr[I]=!0;const w=I.endsWith(".css"),C=w?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${I}"]${C}`))return;const _=document.createElement("link");if(_.rel=w?"stylesheet":fo,w||(_.as="script"),_.crossOrigin="",_.href=I,S&&_.setAttribute("nonce",S),document.head.appendChild(_),w)return new Promise((b,f)=>{_.addEventListener("load",b),_.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${I}`)))})}))}function o(a){const S=new Event("vite:preloadError",{cancelable:!0});if(S.payload=a,window.dispatchEvent(S),!S.defaultPrevented)throw a}return s.then(a=>{for(const S of a||[])S.status==="rejected"&&o(S.reason);return t().catch(o)})},{Title:go}=js,mo=({collapsed:e})=>O.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[O.jsx("div",{style:{width:32,height:32,borderRadius:8,background:"#1890ff",display:"flex",alignItems:"center",justifyContent:"center",marginRight:e?0:10},children:O.jsx("span",{style:{color:"white",fontWeight:"bold"},children:"XBZ"})}),!e&&O.jsx(go,{level:5,style:{margin:0,color:"#1890ff"},children:"闲不猪交换平台"})]}),{Sider:yo}=Be,vo=[{key:"/dashboard",icon:O.jsx($s,{}),label:"仪表板"},{key:"/users",icon:O.jsx(Gt,{}),label:"用户管理"},{key:"/items",icon:O.jsx(qs,{}),label:"物品管理"},{key:"/exchanges",icon:O.jsx(Ds,{}),label:"交换管理"},{key:"/reports",icon:O.jsx(Ms,{}),label:"举报管理"},{key:"/statistics",icon:O.jsx(Qs,{}),label:"数据统计"},{key:"/settings",icon:O.jsx(Ls,{}),label:"系统设置"}],bo=()=>{const e=rn(),t=nn(),r=Nn(),n=Ke(Ln),s=({key:a})=>{e(a)},o=()=>{r(Qn())};return O.jsxs(yo,{trigger:null,collapsible:!0,collapsed:n,width:200,style:{position:"fixed",left:0,top:0,bottom:0,zIndex:1e3,boxShadow:"2px 0 8px rgba(0, 0, 0, 0.15)"},children:[O.jsx("div",{style:{height:"64px",display:"flex",alignItems:"center",justifyContent:"center",borderBottom:"1px solid #f0f0f0",background:"#fff"},children:O.jsx(mo,{collapsed:n})}),O.jsx(ot,{theme:"dark",mode:"inline",selectedKeys:[t.pathname],items:vo,onClick:s,style:{height:"calc(100vh - 128px)",borderRight:0}}),O.jsx("div",{style:{height:"64px",display:"flex",alignItems:"center",justifyContent:"center",borderTop:"1px solid #f0f0f0",background:"#001529",cursor:"pointer",color:"#fff",transition:"all 0.2s"},onClick:o,onMouseEnter:a=>{a.currentTarget.style.background="#1890ff"},onMouseLeave:a=>{a.currentTarget.style.background="#001529"},children:n?O.jsx(mn,{}):O.jsx(Os,{})})]})},{Header:So}=Be,xo=()=>{const e=Nn(),t=rn(),[r]=io(),n=Ke(Dn),s=()=>{e(Qn())},o=async()=>{try{await r().unwrap(),t("/login")}catch(S){console.error("退出登录失败:",S)}},a=O.jsxs(ot,{children:[O.jsx(ot.Item,{icon:O.jsx(Gt,{}),children:"个人资料"},"profile"),O.jsx(ot.Item,{icon:O.jsx(Ns,{}),onClick:o,children:"退出登录"},"logout")]});return O.jsxs(So,{style:{padding:"0 16px",background:"#fff",display:"flex",alignItems:"center",justifyContent:"space-between",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.08)",position:"sticky",top:0,zIndex:100},children:[O.jsx("div",{style:{display:"flex",alignItems:"center"},children:O.jsx(Jt,{type:"text",icon:O.jsx(mn,{}),onClick:s,style:{marginRight:16}})}),O.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[O.jsx(Fs,{count:5,style:{marginRight:16},children:O.jsx(Jt,{type:"text",icon:O.jsx(zs,{})})}),O.jsx(Us,{overlay:a,placement:"bottomRight",children:O.jsxs("div",{style:{display:"flex",alignItems:"center",cursor:"pointer"},children:[O.jsx(Bs,{icon:O.jsx(Gt,{}),style:{marginRight:8}}),O.jsx("span",{style:{fontWeight:500},children:n?"管理员":"未登录"})]})})]})]})},wo=({tip:e="加载中..."})=>O.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",background:"rgba(255, 255, 255, 0.8)",zIndex:1e3},children:O.jsx(yn,{size:"large",tip:e})}),{Content:Io}=Be,_o=()=>{const e=nn(),t=Ke(Ln),r=Ke(lo);return L.useEffect(()=>{window.scrollTo(0,0)},[e.pathname]),O.jsxs(Be,{style:{minHeight:"100vh"},children:[O.jsx(bo,{}),O.jsxs(Be,{style:{marginLeft:t?80:200,transition:"margin-left 0.2s"},children:[O.jsx(xo,{}),O.jsxs(Io,{style:{margin:"16px",padding:"24px",background:"#fff",borderRadius:"8px",minHeight:"calc(100vh - 112px)",position:"relative",overflow:"auto"},children:[r.global&&O.jsx(wo,{}),O.jsx(bs,{})]})]})]})},Po=({children:e})=>Ke(Dn)?e:O.jsx(sn,{to:"/login",replace:!0}),Eo=de.lazy(()=>ho(()=>import("./index-CcEm1oee.js"),__vite__mapDeps([0,1,2,3]))),Ro=()=>O.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"200px"},children:O.jsx(yn,{size:"large"})}),Ao=()=>O.jsxs(Ss,{children:[O.jsxs(Xe,{path:"/",element:O.jsx(Po,{children:O.jsx(_o,{})}),children:[O.jsx(Xe,{index:!0,element:O.jsx(sn,{to:"/dashboard",replace:!0})}),O.jsx(Xe,{path:"dashboard",element:O.jsx(L.Suspense,{fallback:O.jsx(Ro,{}),children:O.jsx(Eo,{})})})]}),O.jsx(Xe,{path:"*",element:O.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[O.jsx("h1",{children:"404 - 页面不存在"}),O.jsx("p",{children:"您访问的页面不存在，请检查URL是否正确。"})]})})]});class Co extends L.Component{constructor(r){super(r);Lt(this,"handleReload",()=>{this.setState({hasError:!1,error:null,errorInfo:null}),window.location.reload()});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(r){return{hasError:!0,error:r,errorInfo:null}}componentDidCatch(r,n){this.setState({hasError:!0,error:r,errorInfo:n}),console.error("组件错误:",r,n)}render(){var r;return this.state.hasError?O.jsx(Ys,{status:"error",title:"页面出错了",subTitle:((r=this.state.error)==null?void 0:r.message)||"未知错误",extra:O.jsx(Jt,{type:"primary",onClick:this.handleReload,children:"重新加载"})}):this.props.children}}var re=function(){return re=Object.assign||function(t){for(var r,n=1,s=arguments.length;n<s;n++){r=arguments[n];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t},re.apply(this,arguments)};function We(e,t,r){if(r||arguments.length===2)for(var n=0,s=t.length,o;n<s;n++)(o||!(n in t))&&(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))}var G="-ms-",ze="-moz-",K="-webkit-",Fn="comm",jt="rule",yr="decl",ko="@import",zn="@keyframes",To="@layer",Un=Math.abs,vr=String.fromCharCode,tr=Object.assign;function jo(e,t){return ee(e,0)^45?(((t<<2^ee(e,0))<<2^ee(e,1))<<2^ee(e,2))<<2^ee(e,3):0}function Bn(e){return e.trim()}function pe(e,t){return(e=t.exec(e))?e[0]:e}function F(e,t,r){return e.replace(t,r)}function at(e,t,r){return e.indexOf(t,r)}function ee(e,t){return e.charCodeAt(t)|0}function Ae(e,t,r){return e.slice(t,r)}function le(e){return e.length}function Yn(e){return e.length}function Ne(e,t){return t.push(e),e}function Oo(e,t){return e.map(t).join("")}function Ur(e,t){return e.filter(function(r){return!pe(r,t)})}var Ot=1,Ce=1,Hn=0,ie=0,X=0,$e="";function $t(e,t,r,n,s,o,a,S){return{value:e,root:t,parent:r,type:n,props:s,children:o,line:Ot,column:Ce,length:a,return:"",siblings:S}}function ge(e,t){return tr($t("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function Pe(e){for(;e.root;)e=ge(e.root,{children:[e]});Ne(e,e.siblings)}function $o(){return X}function qo(){return X=ie>0?ee($e,--ie):0,Ce--,X===10&&(Ce=1,Ot--),X}function ae(){return X=ie<Hn?ee($e,ie++):0,Ce++,X===10&&(Ce=1,Ot++),X}function Se(){return ee($e,ie)}function ct(){return ie}function qt(e,t){return Ae($e,e,t)}function rr(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Do(e){return Ot=Ce=1,Hn=le($e=e),ie=0,[]}function Mo(e){return $e="",e}function Ht(e){return Bn(qt(ie-1,nr(e===91?e+2:e===40?e+1:e)))}function Qo(e){for(;(X=Se())&&X<33;)ae();return rr(e)>2||rr(X)>3?"":" "}function Lo(e,t){for(;--t&&ae()&&!(X<48||X>102||X>57&&X<65||X>70&&X<97););return qt(e,ct()+(t<6&&Se()==32&&ae()==32))}function nr(e){for(;ae();)switch(X){case e:return ie;case 34:case 39:e!==34&&e!==39&&nr(X);break;case 40:e===41&&nr(e);break;case 92:ae();break}return ie}function No(e,t){for(;ae()&&e+X!==57;)if(e+X===84&&Se()===47)break;return"/*"+qt(t,ie-1)+"*"+vr(e===47?e:ae())}function Fo(e){for(;!rr(Se());)ae();return qt(e,ie)}function zo(e){return Mo(ut("",null,null,null,[""],e=Do(e),0,[0],e))}function ut(e,t,r,n,s,o,a,S,I){for(var w=0,C=0,_=a,b=0,f=0,P=0,A=1,R=1,x=1,k=0,p="",i=s,u=o,y=n,v=p;R;)switch(P=k,k=ae()){case 40:if(P!=108&&ee(v,_-1)==58){at(v+=F(Ht(k),"&","&\f"),"&\f",Un(w?S[w-1]:0))!=-1&&(x=-1);break}case 34:case 39:case 91:v+=Ht(k);break;case 9:case 10:case 13:case 32:v+=Qo(P);break;case 92:v+=Lo(ct()-1,7);continue;case 47:switch(Se()){case 42:case 47:Ne(Uo(No(ae(),ct()),t,r,I),I);break;default:v+="/"}break;case 123*A:S[w++]=le(v)*x;case 125*A:case 59:case 0:switch(k){case 0:case 125:R=0;case 59+C:x==-1&&(v=F(v,/\f/g,"")),f>0&&le(v)-_&&Ne(f>32?Yr(v+";",n,r,_-1,I):Yr(F(v," ","")+";",n,r,_-2,I),I);break;case 59:v+=";";default:if(Ne(y=Br(v,t,r,w,C,s,S,p,i=[],u=[],_,o),o),k===123)if(C===0)ut(v,t,y,y,i,o,_,S,u);else switch(b===99&&ee(v,3)===110?100:b){case 100:case 108:case 109:case 115:ut(e,y,y,n&&Ne(Br(e,y,y,0,0,s,S,p,s,i=[],_,u),u),s,u,_,S,n?i:u);break;default:ut(v,y,y,y,[""],u,0,S,u)}}w=C=f=0,A=x=1,p=v="",_=a;break;case 58:_=1+le(v),f=P;default:if(A<1){if(k==123)--A;else if(k==125&&A++==0&&qo()==125)continue}switch(v+=vr(k),k*A){case 38:x=C>0?1:(v+="\f",-1);break;case 44:S[w++]=(le(v)-1)*x,x=1;break;case 64:Se()===45&&(v+=Ht(ae())),b=Se(),C=_=le(p=v+=Fo(ct())),k++;break;case 45:P===45&&le(v)==2&&(A=0)}}return o}function Br(e,t,r,n,s,o,a,S,I,w,C,_){for(var b=s-1,f=s===0?o:[""],P=Yn(f),A=0,R=0,x=0;A<n;++A)for(var k=0,p=Ae(e,b+1,b=Un(R=a[A])),i=e;k<P;++k)(i=Bn(R>0?f[k]+" "+p:F(p,/&\f/g,f[k])))&&(I[x++]=i);return $t(e,t,r,s===0?jt:S,I,w,C,_)}function Uo(e,t,r,n){return $t(e,t,r,Fn,vr($o()),Ae(e,2,-2),0,n)}function Yr(e,t,r,n,s){return $t(e,t,r,yr,Ae(e,0,n),Ae(e,n+1,-1),n,s)}function Kn(e,t,r){switch(jo(e,t)){case 5103:return K+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return K+e+e;case 4789:return ze+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return K+e+ze+e+G+e+e;case 5936:switch(ee(e,t+11)){case 114:return K+e+G+F(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return K+e+G+F(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return K+e+G+F(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return K+e+G+e+e;case 6165:return K+e+G+"flex-"+e+e;case 5187:return K+e+F(e,/(\w+).+(:[^]+)/,K+"box-$1$2"+G+"flex-$1$2")+e;case 5443:return K+e+G+"flex-item-"+F(e,/flex-|-self/g,"")+(pe(e,/flex-|baseline/)?"":G+"grid-row-"+F(e,/flex-|-self/g,""))+e;case 4675:return K+e+G+"flex-line-pack"+F(e,/align-content|flex-|-self/g,"")+e;case 5548:return K+e+G+F(e,"shrink","negative")+e;case 5292:return K+e+G+F(e,"basis","preferred-size")+e;case 6060:return K+"box-"+F(e,"-grow","")+K+e+G+F(e,"grow","positive")+e;case 4554:return K+F(e,/([^-])(transform)/g,"$1"+K+"$2")+e;case 6187:return F(F(F(e,/(zoom-|grab)/,K+"$1"),/(image-set)/,K+"$1"),e,"")+e;case 5495:case 3959:return F(e,/(image-set\([^]*)/,K+"$1$`$1");case 4968:return F(F(e,/(.+:)(flex-)?(.*)/,K+"box-pack:$3"+G+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+K+e+e;case 4200:if(!pe(e,/flex-|baseline/))return G+"grid-column-align"+Ae(e,t)+e;break;case 2592:case 3360:return G+F(e,"template-","")+e;case 4384:case 3616:return r&&r.some(function(n,s){return t=s,pe(n.props,/grid-\w+-end/)})?~at(e+(r=r[t].value),"span",0)?e:G+F(e,"-start","")+e+G+"grid-row-span:"+(~at(r,"span",0)?pe(r,/\d+/):+pe(r,/\d+/)-+pe(e,/\d+/))+";":G+F(e,"-start","")+e;case 4896:case 4128:return r&&r.some(function(n){return pe(n.props,/grid-\w+-start/)})?e:G+F(F(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return F(e,/(.+)-inline(.+)/,K+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(le(e)-1-t>6)switch(ee(e,t+1)){case 109:if(ee(e,t+4)!==45)break;case 102:return F(e,/(.+:)(.+)-([^]+)/,"$1"+K+"$2-$3$1"+ze+(ee(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~at(e,"stretch",0)?Kn(F(e,"stretch","fill-available"),t,r)+e:e}break;case 5152:case 5920:return F(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(n,s,o,a,S,I,w){return G+s+":"+o+w+(a?G+s+"-span:"+(S?I:+I-+o)+w:"")+e});case 4949:if(ee(e,t+6)===121)return F(e,":",":"+K)+e;break;case 6444:switch(ee(e,ee(e,14)===45?18:11)){case 120:return F(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+K+(ee(e,14)===45?"inline-":"")+"box$3$1"+K+"$2$3$1"+G+"$2box$3")+e;case 100:return F(e,":",":"+G)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return F(e,"scroll-","scroll-snap-")+e}return e}function mt(e,t){for(var r="",n=0;n<e.length;n++)r+=t(e[n],n,e,t)||"";return r}function Bo(e,t,r,n){switch(e.type){case To:if(e.children.length)break;case ko:case yr:return e.return=e.return||e.value;case Fn:return"";case zn:return e.return=e.value+"{"+mt(e.children,n)+"}";case jt:if(!le(e.value=e.props.join(",")))return""}return le(r=mt(e.children,n))?e.return=e.value+"{"+r+"}":""}function Yo(e){var t=Yn(e);return function(r,n,s,o){for(var a="",S=0;S<t;S++)a+=e[S](r,n,s,o)||"";return a}}function Ho(e){return function(t){t.root||(t=t.return)&&e(t)}}function Ko(e,t,r,n){if(e.length>-1&&!e.return)switch(e.type){case yr:e.return=Kn(e.value,e.length,r);return;case zn:return mt([ge(e,{value:F(e.value,"@","@"+K)})],n);case jt:if(e.length)return Oo(r=e.props,function(s){switch(pe(s,n=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":Pe(ge(e,{props:[F(s,/:(read-\w+)/,":"+ze+"$1")]})),Pe(ge(e,{props:[s]})),tr(e,{props:Ur(r,n)});break;case"::placeholder":Pe(ge(e,{props:[F(s,/:(plac\w+)/,":"+K+"input-$1")]})),Pe(ge(e,{props:[F(s,/:(plac\w+)/,":"+ze+"$1")]})),Pe(ge(e,{props:[F(s,/:(plac\w+)/,G+"input-$1")]})),Pe(ge(e,{props:[s]})),tr(e,{props:Ur(r,n)});break}return""})}}var Wo={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},se={},ke=typeof process<"u"&&se!==void 0&&(se.REACT_APP_SC_ATTR||se.SC_ATTR)||"data-styled",Wn="active",Vn="data-styled-version",Dt="6.1.19",br=`/*!sc*/
`,yt=typeof window<"u"&&typeof document<"u",Vo=!!(typeof SC_DISABLE_SPEEDY=="boolean"?SC_DISABLE_SPEEDY:typeof process<"u"&&se!==void 0&&se.REACT_APP_SC_DISABLE_SPEEDY!==void 0&&se.REACT_APP_SC_DISABLE_SPEEDY!==""?se.REACT_APP_SC_DISABLE_SPEEDY!=="false"&&se.REACT_APP_SC_DISABLE_SPEEDY:typeof process<"u"&&se!==void 0&&se.SC_DISABLE_SPEEDY!==void 0&&se.SC_DISABLE_SPEEDY!==""&&se.SC_DISABLE_SPEEDY!=="false"&&se.SC_DISABLE_SPEEDY),Go={},Mt=Object.freeze([]),Te=Object.freeze({});function Gn(e,t,r){return r===void 0&&(r=Te),e.theme!==r.theme&&e.theme||t||r.theme}var Jn=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),Jo=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,Zo=/(^-|-$)/g;function Hr(e){return e.replace(Jo,"-").replace(Zo,"")}var Xo=/(a)(d)/gi,st=52,Kr=function(e){return String.fromCharCode(e+(e>25?39:97))};function sr(e){var t,r="";for(t=Math.abs(e);t>st;t=t/st|0)r=Kr(t%st)+r;return(Kr(t%st)+r).replace(Xo,"$1-$2")}var Kt,Zn=5381,Ee=function(e,t){for(var r=t.length;r;)e=33*e^t.charCodeAt(--r);return e},Xn=function(e){return Ee(Zn,e)};function es(e){return sr(Xn(e)>>>0)}function ea(e){return e.displayName||e.name||"Component"}function Wt(e){return typeof e=="string"&&!0}var ts=typeof Symbol=="function"&&Symbol.for,rs=ts?Symbol.for("react.memo"):60115,ta=ts?Symbol.for("react.forward_ref"):60112,ra={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},na={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},ns={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},sa=((Kt={})[ta]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Kt[rs]=ns,Kt);function Wr(e){return("type"in(t=e)&&t.type.$$typeof)===rs?ns:"$$typeof"in e?sa[e.$$typeof]:ra;var t}var ia=Object.defineProperty,oa=Object.getOwnPropertyNames,Vr=Object.getOwnPropertySymbols,aa=Object.getOwnPropertyDescriptor,ca=Object.getPrototypeOf,Gr=Object.prototype;function ss(e,t,r){if(typeof t!="string"){if(Gr){var n=ca(t);n&&n!==Gr&&ss(e,n,r)}var s=oa(t);Vr&&(s=s.concat(Vr(t)));for(var o=Wr(e),a=Wr(t),S=0;S<s.length;++S){var I=s[S];if(!(I in na||r&&r[I]||a&&I in a||o&&I in o)){var w=aa(t,I);try{ia(e,I,w)}catch{}}}}return e}function je(e){return typeof e=="function"}function Sr(e){return typeof e=="object"&&"styledComponentId"in e}function be(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function ir(e,t){if(e.length===0)return"";for(var r=e[0],n=1;n<e.length;n++)r+=e[n];return r}function Ve(e){return e!==null&&typeof e=="object"&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function or(e,t,r){if(r===void 0&&(r=!1),!r&&!Ve(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var n=0;n<t.length;n++)e[n]=or(e[n],t[n]);else if(Ve(t))for(var n in t)e[n]=or(e[n],t[n]);return e}function xr(e,t){Object.defineProperty(e,"toString",{value:t})}function Ze(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var ua=function(){function e(t){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=t}return e.prototype.indexOfGroup=function(t){for(var r=0,n=0;n<t;n++)r+=this.groupSizes[n];return r},e.prototype.insertRules=function(t,r){if(t>=this.groupSizes.length){for(var n=this.groupSizes,s=n.length,o=s;t>=o;)if((o<<=1)<0)throw Ze(16,"".concat(t));this.groupSizes=new Uint32Array(o),this.groupSizes.set(n),this.length=o;for(var a=s;a<o;a++)this.groupSizes[a]=0}for(var S=this.indexOfGroup(t+1),I=(a=0,r.length);a<I;a++)this.tag.insertRule(S,r[a])&&(this.groupSizes[t]++,S++)},e.prototype.clearGroup=function(t){if(t<this.length){var r=this.groupSizes[t],n=this.indexOfGroup(t),s=n+r;this.groupSizes[t]=0;for(var o=n;o<s;o++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(t){var r="";if(t>=this.length||this.groupSizes[t]===0)return r;for(var n=this.groupSizes[t],s=this.indexOfGroup(t),o=s+n,a=s;a<o;a++)r+="".concat(this.tag.getRule(a)).concat(br);return r},e}(),lt=new Map,vt=new Map,dt=1,it=function(e){if(lt.has(e))return lt.get(e);for(;vt.has(dt);)dt++;var t=dt++;return lt.set(e,t),vt.set(t,e),t},la=function(e,t){dt=t+1,lt.set(e,t),vt.set(t,e)},da="style[".concat(ke,"][").concat(Vn,'="').concat(Dt,'"]'),fa=new RegExp("^".concat(ke,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),pa=function(e,t,r){for(var n,s=r.split(","),o=0,a=s.length;o<a;o++)(n=s[o])&&e.registerName(t,n)},ha=function(e,t){for(var r,n=((r=t.textContent)!==null&&r!==void 0?r:"").split(br),s=[],o=0,a=n.length;o<a;o++){var S=n[o].trim();if(S){var I=S.match(fa);if(I){var w=0|parseInt(I[1],10),C=I[2];w!==0&&(la(C,w),pa(e,C,I[3]),e.getTag().insertRules(w,s)),s.length=0}else s.push(S)}}},Jr=function(e){for(var t=document.querySelectorAll(da),r=0,n=t.length;r<n;r++){var s=t[r];s&&s.getAttribute(ke)!==Wn&&(ha(e,s),s.parentNode&&s.parentNode.removeChild(s))}};function ga(){return typeof __webpack_nonce__<"u"?__webpack_nonce__:null}var is=function(e){var t=document.head,r=e||t,n=document.createElement("style"),s=function(S){var I=Array.from(S.querySelectorAll("style[".concat(ke,"]")));return I[I.length-1]}(r),o=s!==void 0?s.nextSibling:null;n.setAttribute(ke,Wn),n.setAttribute(Vn,Dt);var a=ga();return a&&n.setAttribute("nonce",a),r.insertBefore(n,o),n},ma=function(){function e(t){this.element=is(t),this.element.appendChild(document.createTextNode("")),this.sheet=function(r){if(r.sheet)return r.sheet;for(var n=document.styleSheets,s=0,o=n.length;s<o;s++){var a=n[s];if(a.ownerNode===r)return a}throw Ze(17)}(this.element),this.length=0}return e.prototype.insertRule=function(t,r){try{return this.sheet.insertRule(r,t),this.length++,!0}catch{return!1}},e.prototype.deleteRule=function(t){this.sheet.deleteRule(t),this.length--},e.prototype.getRule=function(t){var r=this.sheet.cssRules[t];return r&&r.cssText?r.cssText:""},e}(),ya=function(){function e(t){this.element=is(t),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(t,r){if(t<=this.length&&t>=0){var n=document.createTextNode(r);return this.element.insertBefore(n,this.nodes[t]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(t){this.element.removeChild(this.nodes[t]),this.length--},e.prototype.getRule=function(t){return t<this.length?this.nodes[t].textContent:""},e}(),va=function(){function e(t){this.rules=[],this.length=0}return e.prototype.insertRule=function(t,r){return t<=this.length&&(this.rules.splice(t,0,r),this.length++,!0)},e.prototype.deleteRule=function(t){this.rules.splice(t,1),this.length--},e.prototype.getRule=function(t){return t<this.length?this.rules[t]:""},e}(),Zr=yt,ba={isServer:!yt,useCSSOMInjection:!Vo},bt=function(){function e(t,r,n){t===void 0&&(t=Te),r===void 0&&(r={});var s=this;this.options=re(re({},ba),t),this.gs=r,this.names=new Map(n),this.server=!!t.isServer,!this.server&&yt&&Zr&&(Zr=!1,Jr(this)),xr(this,function(){return function(o){for(var a=o.getTag(),S=a.length,I="",w=function(_){var b=function(x){return vt.get(x)}(_);if(b===void 0)return"continue";var f=o.names.get(b),P=a.getGroup(_);if(f===void 0||!f.size||P.length===0)return"continue";var A="".concat(ke,".g").concat(_,'[id="').concat(b,'"]'),R="";f!==void 0&&f.forEach(function(x){x.length>0&&(R+="".concat(x,","))}),I+="".concat(P).concat(A,'{content:"').concat(R,'"}').concat(br)},C=0;C<S;C++)w(C);return I}(s)})}return e.registerId=function(t){return it(t)},e.prototype.rehydrate=function(){!this.server&&yt&&Jr(this)},e.prototype.reconstructWithOptions=function(t,r){return r===void 0&&(r=!0),new e(re(re({},this.options),t),this.gs,r&&this.names||void 0)},e.prototype.allocateGSInstance=function(t){return this.gs[t]=(this.gs[t]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(t=function(r){var n=r.useCSSOMInjection,s=r.target;return r.isServer?new va(s):n?new ma(s):new ya(s)}(this.options),new ua(t)));var t},e.prototype.hasNameForId=function(t,r){return this.names.has(t)&&this.names.get(t).has(r)},e.prototype.registerName=function(t,r){if(it(t),this.names.has(t))this.names.get(t).add(r);else{var n=new Set;n.add(r),this.names.set(t,n)}},e.prototype.insertRules=function(t,r,n){this.registerName(t,r),this.getTag().insertRules(it(t),n)},e.prototype.clearNames=function(t){this.names.has(t)&&this.names.get(t).clear()},e.prototype.clearRules=function(t){this.getTag().clearGroup(it(t)),this.clearNames(t)},e.prototype.clearTag=function(){this.tag=void 0},e}(),Sa=/&/g,xa=/^\s*\/\/.*$/gm;function os(e,t){return e.map(function(r){return r.type==="rule"&&(r.value="".concat(t," ").concat(r.value),r.value=r.value.replaceAll(",",",".concat(t," ")),r.props=r.props.map(function(n){return"".concat(t," ").concat(n)})),Array.isArray(r.children)&&r.type!=="@keyframes"&&(r.children=os(r.children,t)),r})}function wa(e){var t,r,n,s=Te,o=s.options,a=o===void 0?Te:o,S=s.plugins,I=S===void 0?Mt:S,w=function(b,f,P){return P.startsWith(r)&&P.endsWith(r)&&P.replaceAll(r,"").length>0?".".concat(t):b},C=I.slice();C.push(function(b){b.type===jt&&b.value.includes("&")&&(b.props[0]=b.props[0].replace(Sa,r).replace(n,w))}),a.prefix&&C.push(Ko),C.push(Bo);var _=function(b,f,P,A){f===void 0&&(f=""),P===void 0&&(P=""),A===void 0&&(A="&"),t=A,r=f,n=new RegExp("\\".concat(r,"\\b"),"g");var R=b.replace(xa,""),x=zo(P||f?"".concat(P," ").concat(f," { ").concat(R," }"):R);a.namespace&&(x=os(x,a.namespace));var k=[];return mt(x,Yo(C.concat(Ho(function(p){return k.push(p)})))),k};return _.hash=I.length?I.reduce(function(b,f){return f.name||Ze(15),Ee(b,f.name)},Zn).toString():"",_}var Ia=new bt,ar=wa(),as=de.createContext({shouldForwardProp:void 0,styleSheet:Ia,stylis:ar});as.Consumer;de.createContext(void 0);function cr(){return L.useContext(as)}var _a=function(){function e(t,r){var n=this;this.inject=function(s,o){o===void 0&&(o=ar);var a=n.name+o.hash;s.hasNameForId(n.id,a)||s.insertRules(n.id,a,o(n.rules,a,"@keyframes"))},this.name=t,this.id="sc-keyframes-".concat(t),this.rules=r,xr(this,function(){throw Ze(12,String(n.name))})}return e.prototype.getName=function(t){return t===void 0&&(t=ar),this.name+t.hash},e}(),Pa=function(e){return e>="A"&&e<="Z"};function Xr(e){for(var t="",r=0;r<e.length;r++){var n=e[r];if(r===1&&n==="-"&&e[0]==="-")return e;Pa(n)?t+="-"+n.toLowerCase():t+=n}return t.startsWith("ms-")?"-"+t:t}var cs=function(e){return e==null||e===!1||e===""},us=function(e){var t,r,n=[];for(var s in e){var o=e[s];e.hasOwnProperty(s)&&!cs(o)&&(Array.isArray(o)&&o.isCss||je(o)?n.push("".concat(Xr(s),":"),o,";"):Ve(o)?n.push.apply(n,We(We(["".concat(s," {")],us(o),!1),["}"],!1)):n.push("".concat(Xr(s),": ").concat((t=s,(r=o)==null||typeof r=="boolean"||r===""?"":typeof r!="number"||r===0||t in Wo||t.startsWith("--")?String(r).trim():"".concat(r,"px")),";")))}return n};function ye(e,t,r,n){if(cs(e))return[];if(Sr(e))return[".".concat(e.styledComponentId)];if(je(e)){if(!je(o=e)||o.prototype&&o.prototype.isReactComponent||!t)return[e];var s=e(t);return ye(s,t,r,n)}var o;return e instanceof _a?r?(e.inject(r,n),[e.getName(n)]):[e]:Ve(e)?us(e):Array.isArray(e)?Array.prototype.concat.apply(Mt,e.map(function(a){return ye(a,t,r,n)})):[e.toString()]}function ls(e){for(var t=0;t<e.length;t+=1){var r=e[t];if(je(r)&&!Sr(r))return!1}return!0}var Ea=Xn(Dt),Ra=function(){function e(t,r,n){this.rules=t,this.staticRulesId="",this.isStatic=(n===void 0||n.isStatic)&&ls(t),this.componentId=r,this.baseHash=Ee(Ea,r),this.baseStyle=n,bt.registerId(r)}return e.prototype.generateAndInjectStyles=function(t,r,n){var s=this.baseStyle?this.baseStyle.generateAndInjectStyles(t,r,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&r.hasNameForId(this.componentId,this.staticRulesId))s=be(s,this.staticRulesId);else{var o=ir(ye(this.rules,t,r,n)),a=sr(Ee(this.baseHash,o)>>>0);if(!r.hasNameForId(this.componentId,a)){var S=n(o,".".concat(a),void 0,this.componentId);r.insertRules(this.componentId,a,S)}s=be(s,a),this.staticRulesId=a}else{for(var I=Ee(this.baseHash,n.hash),w="",C=0;C<this.rules.length;C++){var _=this.rules[C];if(typeof _=="string")w+=_;else if(_){var b=ir(ye(_,t,r,n));I=Ee(I,b+C),w+=b}}if(w){var f=sr(I>>>0);r.hasNameForId(this.componentId,f)||r.insertRules(this.componentId,f,n(w,".".concat(f),void 0,this.componentId)),s=be(s,f)}}return s},e}(),wr=de.createContext(void 0);wr.Consumer;var Vt={};function Aa(e,t,r){var n=Sr(e),s=e,o=!Wt(e),a=t.attrs,S=a===void 0?Mt:a,I=t.componentId,w=I===void 0?function(i,u){var y=typeof i!="string"?"sc":Hr(i);Vt[y]=(Vt[y]||0)+1;var v="".concat(y,"-").concat(es(Dt+y+Vt[y]));return u?"".concat(u,"-").concat(v):v}(t.displayName,t.parentComponentId):I,C=t.displayName,_=C===void 0?function(i){return Wt(i)?"styled.".concat(i):"Styled(".concat(ea(i),")")}(e):C,b=t.displayName&&t.componentId?"".concat(Hr(t.displayName),"-").concat(t.componentId):t.componentId||w,f=n&&s.attrs?s.attrs.concat(S).filter(Boolean):S,P=t.shouldForwardProp;if(n&&s.shouldForwardProp){var A=s.shouldForwardProp;if(t.shouldForwardProp){var R=t.shouldForwardProp;P=function(i,u){return A(i,u)&&R(i,u)}}else P=A}var x=new Ra(r,b,n?s.componentStyle:void 0);function k(i,u){return function(y,v,E){var m=y.attrs,l=y.componentStyle,c=y.defaultProps,d=y.foldedComponentIds,h=y.styledComponentId,g=y.target,T=de.useContext(wr),$=cr(),q=y.shouldForwardProp||$.shouldForwardProp,M=Gn(v,T,c)||Te,j=function(D,H,U){for(var Z,te=re(re({},H),{className:void 0,theme:U}),W=0;W<D.length;W+=1){var Y=je(Z=D[W])?Z(te):Z;for(var J in Y)te[J]=J==="className"?be(te[J],Y[J]):J==="style"?re(re({},te[J]),Y[J]):Y[J]}return H.className&&(te.className=be(te.className,H.className)),te}(m,v,M),Q=j.as||g,N={};for(var z in j)j[z]===void 0||z[0]==="$"||z==="as"||z==="theme"&&j.theme===M||(z==="forwardedAs"?N.as=j.forwardedAs:q&&!q(z,Q)||(N[z]=j[z]));var V=function(D,H){var U=cr(),Z=D.generateAndInjectStyles(H,U.styleSheet,U.stylis);return Z}(l,j),B=be(d,h);return V&&(B+=" "+V),j.className&&(B+=" "+j.className),N[Wt(Q)&&!Jn.has(Q)?"class":"className"]=B,E&&(N.ref=E),L.createElement(Q,N)}(p,i,u)}k.displayName=_;var p=de.forwardRef(k);return p.attrs=f,p.componentStyle=x,p.displayName=_,p.shouldForwardProp=P,p.foldedComponentIds=n?be(s.foldedComponentIds,s.styledComponentId):"",p.styledComponentId=b,p.target=n?s.target:e,Object.defineProperty(p,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(i){this._foldedDefaultProps=n?function(u){for(var y=[],v=1;v<arguments.length;v++)y[v-1]=arguments[v];for(var E=0,m=y;E<m.length;E++)or(u,m[E],!0);return u}({},s.defaultProps,i):i}}),xr(p,function(){return".".concat(p.styledComponentId)}),o&&ss(p,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),p}function en(e,t){for(var r=[e[0]],n=0,s=t.length;n<s;n+=1)r.push(t[n],e[n+1]);return r}var tn=function(e){return Object.assign(e,{isCss:!0})};function ds(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(je(e)||Ve(e))return tn(ye(en(Mt,We([e],t,!0))));var n=e;return t.length===0&&n.length===1&&typeof n[0]=="string"?ye(n):tn(ye(en(n,t)))}function ur(e,t,r){if(r===void 0&&(r=Te),!t)throw Ze(1,t);var n=function(s){for(var o=[],a=1;a<arguments.length;a++)o[a-1]=arguments[a];return e(t,r,ds.apply(void 0,We([s],o,!1)))};return n.attrs=function(s){return ur(e,t,re(re({},r),{attrs:Array.prototype.concat(r.attrs,s).filter(Boolean)}))},n.withConfig=function(s){return ur(e,t,re(re({},r),s))},n}var fs=function(e){return ur(Aa,e)},Ca=fs;Jn.forEach(function(e){Ca[e]=fs(e)});var ka=function(){function e(t,r){this.rules=t,this.componentId=r,this.isStatic=ls(t),bt.registerId(this.componentId+1)}return e.prototype.createStyles=function(t,r,n,s){var o=s(ir(ye(this.rules,r,n,s)),""),a=this.componentId+t;n.insertRules(a,a,o)},e.prototype.removeStyles=function(t,r){r.clearRules(this.componentId+t)},e.prototype.renderStyles=function(t,r,n,s){t>2&&bt.registerId(this.componentId+t),this.removeStyles(t,n),this.createStyles(t,r,n,s)},e}();function Ta(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=ds.apply(void 0,We([e],t,!1)),s="sc-global-".concat(es(JSON.stringify(n))),o=new ka(n,s),a=function(I){var w=cr(),C=de.useContext(wr),_=de.useRef(w.styleSheet.allocateGSInstance(s)).current;return w.styleSheet.server&&S(_,I,w.styleSheet,C,w.stylis),de.useLayoutEffect(function(){if(!w.styleSheet.server)return S(_,I,w.styleSheet,C,w.stylis),function(){return o.removeStyles(_,w.styleSheet)}},[_,I,w.styleSheet,C,w.stylis]),null};function S(I,w,C,_,b){if(o.isStatic)o.renderStyles(I,Go,C,b);else{var f=re(re({},w),{theme:Gn(w,_,a.defaultProps)});o.renderStyles(I,f,C,b)}}return de.memo(a)}const ja=Ta`
  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f5f5f5;
  }

  * {
    box-sizing: border-box;
  }

  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: 600;
  }

  p {
    margin: 0;
  }

  a {
    color: #1890ff;
    text-decoration: none;
    transition: color 0.2s;
  }

  a:hover {
    color: #40a9ff;
    text-decoration: underline;
  }

  .site-form-item-icon {
    color: rgba(0, 0, 0, 0.25);
  }

  /* Ant Design 覆盖样式 */
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }

  .ant-btn-primary {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-btn-primary:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }

  .ant-table-thead > tr > th {
    background-color: #f7f7f7;
    font-weight: 600;
  }

  .ant-menu-item-selected {
    background-color: #e6f7ff !important;
    color: #1890ff !important;
  }

  .ant-menu-item-selected .ant-menu-item-icon {
    color: #1890ff !important;
  }
`;var Oa={exports:{}};(function(e,t){(function(r,n){e.exports=n(Hs())})(xs,function(r){function n(a){return a&&typeof a=="object"&&"default"in a?a:{default:a}}var s=n(r),o={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(a,S){return S==="W"?a+"周":a+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(a,S){var I=100*a+S;return I<600?"凌晨":I<900?"早上":I<1100?"上午":I<1300?"中午":I<1800?"下午":"晚上"}};return s.default.locale(o,null,!0),o})})(Oa);function $a(){return O.jsx(Co,{children:O.jsx(gn,{store:mr,children:O.jsx(vn,{locale:Pn,theme:{token:{colorPrimary:"#1890ff",borderRadius:6,colorBgContainer:"#ffffff"},components:{Layout:{siderBg:"#001529",triggerBg:"#002140"},Menu:{darkItemBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedBg:"#1890ff"}}},children:O.jsxs(on,{children:[O.jsx(ja,{}),O.jsx(Ao,{})]})})})})}xn(document.getElementById("root")).render(O.jsx(L.StrictMode,{children:O.jsx(gn,{store:mr,children:O.jsx(on,{children:O.jsx(vn,{locale:Pn,children:O.jsx($a,{})})})})}));export{Ke as a,La as b,ec as c,tc as d,O as j,sc as s,Nn as u};
//# sourceMappingURL=index-CSkGGLAO.js.map
