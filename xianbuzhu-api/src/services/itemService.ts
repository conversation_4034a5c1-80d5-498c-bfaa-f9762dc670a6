/**
 * 物品服务
 */

import { prisma } from '../lib/prisma'
import { logger } from '../utils/logger'

export interface CreateItemData {
  title: string
  description: string
  categoryId: string
  condition: string
  estimatedValue: number
  publisherId: string
  communityId: string
  latitude?: number
  longitude?: number
  address?: string
  images?: string[]
  desiredItems?: string[]
  tags?: string[]
}

export interface UpdateItemData {
  title?: string
  description?: string
  categoryId?: string
  condition?: string
  estimatedValue?: number
  latitude?: number
  longitude?: number
  address?: string
  images?: string[]
  desiredItems?: string[]
  tags?: string[]
  status?: string
  isActive?: boolean
}

export class ItemService {
  /**
   * 根据ID查找物品
   */
  async findById(id: string) {
    try {
      const item = await prisma.item.findUnique({
        where: { id },
        include: {
          images: true,
          publisher: {
            select: {
              id: true,
              name: true,
              avatar: true,
              isVerified: true,
            },
          },
          category: true,
          community: true,
          exchanges: {
            where: { status: { in: ['pending', 'accepted'] } },
            include: {
              requester: {
                select: {
                  id: true,
                  name: true,
                  avatar: true,
                },
              },
            },
          },
        },
      })

      return item
    } catch (error) {
      logger.error('查找物品失败:', error)
      throw error
    }
  }

  /**
   * 创建物品
   */
  async create(data: CreateItemData) {
    try {
      const result = await prisma.$transaction(async (tx: any) => {
        // 创建物品
        const item = await tx.item.create({
          data: {
            title: data.title,
            description: data.description,
            categoryId: data.categoryId,
            condition: data.condition,
            estimatedValue: data.estimatedValue,
            publisherId: data.publisherId,
            communityId: data.communityId,
            latitude: data.latitude,
            longitude: data.longitude,
            address: data.address,
            status: 'available',
          },
          include: {
            images: true,
            publisher: {
              select: {
                id: true,
                name: true,
                avatar: true,
                isVerified: true,
              },
            },
            category: true,
            community: true,
          },
        })

        // 批量创建图片
        if (data.images && data.images.length > 0) {
          await tx.itemImage.createMany({
            data: data.images.map((url, index) => ({
              itemId: item.id,
              url,
              order: index,
            })),
          })
        }

        return item
      })

      logger.info('创建物品成功:', result.id)
      return result
    } catch (error) {
      logger.error('创建物品失败:', error)
      throw error
    }
  }

  /**
   * 更新物品
   */
  async update(id: string, data: UpdateItemData) {
    try {
      const result = await prisma.$transaction(async (tx: any) => {
        // 更新物品
        const updateData: any = {}
        if (data.title) updateData.title = data.title
        if (data.description) updateData.description = data.description
        if (data.condition) updateData.condition = data.condition
        if (data.estimatedValue) updateData.estimatedValue = data.estimatedValue
        if (data.latitude) updateData.latitude = data.latitude
        if (data.longitude) updateData.longitude = data.longitude
        if (data.address) updateData.address = data.address
        if (data.status) updateData.status = data.status

        const item = await tx.item.update({
          where: { id },
          data: updateData,
          include: {
            images: true,
            publisher: {
              select: {
                id: true,
                name: true,
                avatar: true,
                isVerified: true,
              },
            },
            category: true,
            community: true,
          },
        })

        // 更新图片
        if (data.images) {
          // 删除旧图片
          await tx.itemImage.deleteMany({
            where: { itemId: id },
          })

          // 创建新图片
          if (data.images.length > 0) {
            await tx.itemImage.createMany({
              data: data.images.map((url, index) => ({
                itemId: id,
                url,
                order: index,
              })),
            })
          }
        }

        return item
      })

      logger.info('更新物品成功:', id)
      return result
    } catch (error) {
      logger.error('更新物品失败:', error)
      throw error
    }
  }

  /**
   * 删除物品（软删除）
   */
  async delete(id: string) {
    try {
      // 软删除 - 更新状态而不是真正删除
      await prisma.item.update({
        where: { id },
        data: { status: 'deleted' },
      })

      logger.info('删除物品成功:', id)
      return true
    } catch (error) {
      logger.error('删除物品失败:', error)
      return false
    }
  }

  /**
   * 获取物品列表
   */
  async list(params: {
    page?: number
    limit?: number
    search?: string
    categoryId?: string
    communityId?: string
    publisherId?: string
    condition?: string
    status?: string
  } = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        categoryId,
        communityId,
        publisherId,
        condition,
        status = 'available',
      } = params

      const skip = (page - 1) * limit

      const where: any = {
        status,
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ]
      }

      if (categoryId) where.categoryId = categoryId
      if (communityId) where.communityId = communityId
      if (publisherId) where.publisherId = publisherId
      if (condition) where.condition = condition

      const [items, total] = await Promise.all([
        prisma.item.findMany({
          where,
          skip,
          take: limit,
          include: {
            images: { take: 1 },
            publisher: {
              select: {
                id: true,
                name: true,
                avatar: true,
                isVerified: true,
              },
            },
            category: true,
            community: true,
          },
          orderBy: { createdAt: 'desc' },
        }),
        prisma.item.count({ where }),
      ])

      return {
        items,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      }
    } catch (error) {
      logger.error('获取物品列表失败:', error)
      throw error
    }
  }
}

export const itemService = new ItemService()
