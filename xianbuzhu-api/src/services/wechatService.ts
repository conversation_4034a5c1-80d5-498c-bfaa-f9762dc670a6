/**
 * 微信服务
 */

import axios from 'axios'
import { config } from '../config'
import { logger } from '../utils/logger'

export interface WechatSessionResult {
  openid: string
  session_key: string
  unionid?: string
  errcode?: number
  errmsg?: string
}

export class WechatService {
  /**
   * 获取微信用户session信息
   */
  async getSessionInfo(code: string): Promise<WechatSessionResult> {
    try {
      const url = 'https://api.weixin.qq.com/sns/jscode2session'
      const params = {
        appid: config.WECHAT_APP_ID,
        secret: config.WECHAT_APP_SECRET,
        js_code: code,
        grant_type: 'authorization_code',
      }

      const response = await axios.get(url, { params })
      const result = response.data

      if (result.errcode) {
        throw new Error(`微信API错误: ${result.errmsg}`)
      }

      logger.info('获取微信session成功', { openid: result.openid })
      return result
    } catch (error) {
      logger.error('获取微信session失败:', error)
      throw error
    }
  }

  /**
   * 获取微信访问令牌
   */
  async getAccessToken(): Promise<string> {
    try {
      const url = 'https://api.weixin.qq.com/cgi-bin/token'
      const params = {
        grant_type: 'client_credential',
        appid: config.WECHAT_APP_ID,
        secret: config.WECHAT_APP_SECRET,
      }

      const response = await axios.get(url, { params })
      const result = response.data

      if (result.errcode) {
        throw new Error(`获取访问令牌失败: ${result.errmsg}`)
      }

      return result.access_token
    } catch (error) {
      logger.error('获取微信访问令牌失败:', error)
      throw error
    }
  }

  /**
   * 发送模板消息
   */
  async sendTemplateMessage(data: {
    touser: string
    template_id: string
    data: Record<string, { value: string; color?: string }>
    miniprogram?: {
      appid: string
      pagepath: string
    }
  }) {
    try {
      const accessToken = await this.getAccessToken()
      const url = `https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=${accessToken}`

      const response = await axios.post(url, data)
      const result = response.data

      if (result.errcode !== 0) {
        throw new Error(`发送模板消息失败: ${result.errmsg}`)
      }

      logger.info('发送模板消息成功', { msgid: result.msgid })
      return result
    } catch (error) {
      logger.error('发送模板消息失败:', error)
      throw error
    }
  }
}

export const wechatService = new WechatService()
