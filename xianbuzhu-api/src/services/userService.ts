/**
 * 用户服务
 */

import { prisma } from '../lib/prisma'
import { logger } from '../utils/logger'

export interface CreateUserData {
  name: string
  phone?: string
  avatar?: string
  openid?: string
  unionid?: string
  sessionKey?: string
  communityId?: string
}

export interface UpdateUserData {
  name?: string
  phone?: string
  avatar?: string
  communityId?: string
  isVerified?: boolean
  isActive?: boolean
  sessionKey?: string
  lastLoginAt?: Date
}

export class UserService {
  /**
   * 根据ID查找用户
   */
  async findById(id: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          community: true,
        },
      })

      return user
    } catch (error) {
      logger.error('查找用户失败:', error)
      throw error
    }
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phone: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { phone },
      })

      return user
    } catch (error) {
      logger.error('根据手机号查找用户失败:', error)
      throw error
    }
  }

  /**
   * 根据openid查找用户
   */
  async findByOpenid(openid: string) {
    try {
      // 注意：这里需要根据实际的数据库模型调整
      // 如果User模型中没有openid字段，需要通过其他方式查找
      const user = await prisma.user.findFirst({
        where: {
          // 假设通过某个字段存储openid，需要根据实际情况调整
          id: openid // 临时解决方案
        },
      })

      return user
    } catch (error) {
      logger.error('根据openid查找用户失败:', error)
      throw error
    }
  }

  /**
   * 创建用户
   */
  async create(data: CreateUserData) {
    try {
      const user = await prisma.user.create({
        data,
        include: {
          community: true,
        },
      })

      logger.info('创建用户成功:', user.id)
      return user
    } catch (error) {
      logger.error('创建用户失败:', error)
      throw error
    }
  }

  /**
   * 更新用户
   */
  async update(id: string, data: UpdateUserData) {
    try {
      const user = await prisma.user.update({
        where: { id },
        data,
        include: {
          community: true,
        },
      })

      logger.info('更新用户成功:', id)
      return user
    } catch (error) {
      logger.error('更新用户失败:', error)
      throw error
    }
  }

  /**
   * 删除用户
   */
  async delete(id: string) {
    try {
      await prisma.user.delete({
        where: { id },
      })

      logger.info('删除用户成功:', id)
      return true
    } catch (error) {
      logger.error('删除用户失败:', error)
      return false
    }
  }

  /**
   * 获取用户列表
   */
  async list(params: {
    page?: number
    limit?: number
    search?: string
    communityId?: string
    isVerified?: boolean
  } = {}) {
    try {
      const { page = 1, limit = 20, search, communityId, isVerified } = params
      const skip = (page - 1) * limit

      const where: any = {}

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } },
        ]
      }

      if (communityId) {
        where.communityId = communityId
      }

      if (isVerified !== undefined) {
        where.isVerified = isVerified
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take: limit,
          include: {
            community: true,
          },
          orderBy: { createdAt: 'desc' },
        }),
        prisma.user.count({ where }),
      ])

      return {
        users,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      }
    } catch (error) {
      logger.error('获取用户列表失败:', error)
      throw error
    }
  }
}

export const userService = new UserService()
