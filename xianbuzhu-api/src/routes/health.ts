/**
 * 健康检查路由
 */

import { Router } from 'express'
import { prisma } from '../lib/prisma'
import { logger } from '../utils/logger'

const router = Router()

// 基础健康检查
router.get('/', async (req, res) => {
  try {
    // 检查数据库连接
    await prisma.$queryRaw`SELECT 1`
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0',
    })
  } catch (error) {
    logger.error('健康检查失败', { error })
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// 详细健康检查
router.get('/detailed', async (req, res) => {
  const checks = {
    database: false,
    memory: false,
    uptime: false,
  }

  try {
    // 数据库检查
    await prisma.$queryRaw`SELECT 1`
    checks.database = true
  } catch (error) {
    logger.error('数据库健康检查失败', { error })
  }

  // 内存检查
  const memoryUsage = process.memoryUsage()
  checks.memory = memoryUsage.heapUsed < memoryUsage.heapTotal * 0.9

  // 运行时间检查
  checks.uptime = process.uptime() > 0

  const isHealthy = Object.values(checks).every(check => check)

  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    checks,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: memoryUsage,
  })
})

export default router
