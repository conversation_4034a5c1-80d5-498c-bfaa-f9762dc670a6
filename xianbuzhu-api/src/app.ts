/**
 * API网关服务主入口文件
 */

import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'

import { config } from './config'
import { logger } from './utils/logger'
import { errorHandler } from './middleware/errorHandler'
import { requestLogger } from './middleware/requestLogger'
import { metricsMiddleware } from './middleware/metrics'

// 路由导入
import miniprogramRoutes from './routes/miniprogram'
import healthRoutes from './routes/health'
import adminRoutes from './routes/admin'

const app = express()

// 基础中间件
app.use(helmet())
app.use(cors({
  origin: config.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001'],
  credentials: true
}))

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 限制每个IP 15分钟内最多1000个请求
  message: '请求过于频繁，请稍后再试',
  standardHeaders: true,
  legacyHeaders: false,
})
app.use(limiter)

// 请求解析
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 请求日志
app.use(requestLogger)

// 性能监控
app.use(metricsMiddleware)

// 路由注册
app.use('/api/miniprogram', miniprogramRoutes)
app.use('/api/admin', adminRoutes)
app.use('/api/health', healthRoutes)

// 根路径
app.get('/', (req, res) => {
  res.json({
    name: 'Xianbuzhu API Gateway',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString()
  })
})

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  })
})

// 错误处理
app.use(errorHandler)

// 启动服务器
const PORT = config.PORT || 3000
const server = app.listen(PORT, () => {
  logger.info(`🚀 API网关服务启动成功`)
  logger.info(`📡 服务地址: http://localhost:${PORT}`)
  logger.info(`🌍 环境: ${config.NODE_ENV}`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...')
  server.close(() => {
    logger.info('服务器已关闭')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...')
  server.close(() => {
    logger.info('服务器已关闭')
    process.exit(0)
  })
})

export default app
