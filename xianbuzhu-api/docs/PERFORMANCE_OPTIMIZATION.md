# API性能优化指南

## 概述

本文档描述了API网关的性能优化策略和实现，包括缓存、数据库优化、限流等多个方面。

## 🚀 性能优化组件

### 1. 缓存服务 (CacheService)

#### 功能特性
- Redis缓存支持
- 自动序列化/反序列化
- 缓存装饰器
- 批量操作
- 缓存统计

#### 使用示例
```typescript
import { cacheService, CACHE_KEYS, CACHE_TTL } from '../services/cacheService';

// 基本使用
await cacheService.set('key', data, CACHE_TTL.MEDIUM);
const data = await cacheService.get('key');

// 使用装饰器
class MyService {
  @cacheService.cache('user:profile', CACHE_TTL.LONG)
  async getUserProfile(userId: string) {
    // 方法会自动缓存结果
    return await this.fetchUserFromDB(userId);
  }
}
```

#### 缓存策略
- **短期缓存 (5分钟)**: 频繁变化的数据，如物品列表
- **中期缓存 (30分钟)**: 相对稳定的数据，如物品详情
- **长期缓存 (1小时)**: 稳定数据，如用户资料
- **超长期缓存 (24小时)**: 很少变化的数据，如分类信息

### 2. 数据库优化

#### 索引优化
```sql
-- 复合索引用于常用查询
CREATE INDEX idx_items_status_community ON "Item" ("status", "communityId");
CREATE INDEX idx_items_category_status ON "Item" ("categoryId", "status");

-- 全文搜索索引
CREATE INDEX idx_items_title_search ON "Item" USING gin(to_tsvector('english', "title"));
```

#### 查询优化
- 使用 `select` 字段限制返回数据
- 并行执行独立查询
- 避免 N+1 查询问题
- 使用分页限制结果集大小

```typescript
// 优化前
const items = await prisma.item.findMany({
  include: {
    publisher: true,
    category: true,
    images: true,
  }
});

// 优化后
const items = await prisma.item.findMany({
  select: {
    id: true,
    title: true,
    estimatedValue: true,
    publisher: {
      select: {
        id: true,
        name: true,
        avatar: true,
      }
    },
    images: {
      select: { url: true },
      take: 1,
    }
  }
});
```

### 3. 性能监控

#### 响应时间监控
- 自动记录所有API响应时间
- 慢查询检测和告警
- 内存使用监控
- 缓存命中率统计

#### 监控指标
```typescript
interface PerformanceMetrics {
  responseTime: number;      // 响应时间
  memoryUsage: NodeJS.MemoryUsage; // 内存使用
  dbQueryCount: number;      // 数据库查询次数
  cacheHits: number;         // 缓存命中次数
  cacheMisses: number;       // 缓存未命中次数
}
```

### 4. API限流

#### 限流策略
- **严格限流**: 敏感操作，15分钟5次
- **标准限流**: 一般API，15分钟100次
- **宽松限流**: 查询操作，15分钟1000次
- **认证限流**: 登录操作，15分钟10次

#### 使用示例
```typescript
import { createRateLimit, authRateLimit } from '../middleware/rateLimiter';

// 应用到路由
router.post('/login', authRateLimit, authController.login);
router.get('/items', createRateLimit('lenient'), itemController.getItems);
```

## 📊 性能基准

### 响应时间目标
- **查询操作**: < 200ms
- **创建操作**: < 500ms
- **复杂查询**: < 1000ms
- **文件上传**: < 5000ms

### 缓存命中率目标
- **物品详情**: > 80%
- **用户资料**: > 90%
- **分类信息**: > 95%
- **热门物品**: > 95%

## 🔧 配置说明

### Redis配置
```typescript
// config/redis.ts
export const redisConfig = {
  url: process.env.REDIS_URL,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  lazyConnect: true,
  keepAlive: 30000,
};
```

### 数据库连接池配置
```typescript
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
  previewFeatures = ["metrics"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

### 环境变量
```bash
# 数据库
DATABASE_URL="postgresql://user:password@localhost:5432/db"

# Redis
REDIS_URL="redis://localhost:6379"

# 性能配置
SLOW_QUERY_THRESHOLD=1000
CACHE_DEFAULT_TTL=3600
RATE_LIMIT_WINDOW=900000
```

## 🚀 部署优化

### 生产环境配置
1. **启用连接池**: 设置合适的连接池大小
2. **配置缓存**: 使用Redis集群提高可用性
3. **监控告警**: 设置性能指标告警
4. **负载均衡**: 使用多实例部署

### Docker配置
```dockerfile
# 多阶段构建优化镜像大小
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 📈 性能测试

### 压力测试
```bash
# 使用Apache Bench测试
ab -n 1000 -c 10 http://localhost:3000/api/items

# 使用Artillery测试
artillery run performance-test.yml
```

### 测试配置
```yaml
# performance-test.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Get Items"
    requests:
      - get:
          url: "/api/items"
```

## 🔍 故障排查

### 常见性能问题
1. **慢查询**: 检查数据库索引和查询优化
2. **内存泄漏**: 监控内存使用趋势
3. **缓存穿透**: 检查缓存键设计和TTL设置
4. **连接池耗尽**: 调整连接池配置

### 监控工具
- **应用监控**: 使用性能监控中间件
- **数据库监控**: Prisma查询日志
- **缓存监控**: Redis监控命令
- **系统监控**: CPU、内存、网络使用率

## 📝 最佳实践

### 代码层面
1. **避免同步操作**: 使用异步/await
2. **批量操作**: 合并数据库操作
3. **分页查询**: 限制单次查询数据量
4. **缓存策略**: 合理设置缓存TTL

### 架构层面
1. **读写分离**: 分离读写数据库
2. **CDN加速**: 静态资源使用CDN
3. **微服务**: 按业务拆分服务
4. **异步处理**: 耗时操作异步化

## 🎯 性能优化检查清单

- [ ] 数据库索引已优化
- [ ] 缓存策略已实施
- [ ] API限流已配置
- [ ] 性能监控已启用
- [ ] 慢查询检测已开启
- [ ] 连接池已优化
- [ ] 响应时间符合目标
- [ ] 缓存命中率达标
- [ ] 错误率在可接受范围
- [ ] 负载测试已通过

## 📞 支持

如有性能相关问题，请联系开发团队或查看监控面板获取详细信息。
