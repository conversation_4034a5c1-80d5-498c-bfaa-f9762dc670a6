/**
 * 数据库种子文件
 */

import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('开始初始化数据库...')

  // 创建管理员用户
  const adminPassword = await hash('admin123', 10)
  const admin = await prisma.adminUser.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      password: adminPassword,
      name: '超级管理员',
      role: 'superadmin',
      email: '<EMAIL>'
    }
  })
  console.log('创建管理员用户:', admin.username)

  // 创建社区
  const community = await prisma.community.upsert({
    where: { id: 'community-1' },
    update: {},
    create: {
      id: 'community-1',
      name: '示例社区',
      description: '这是一个示例社区',
      location: '北京市朝阳区',
      latitude: 39.9042,
      longitude: 116.4074,
      radius: 5000
    }
  })
  console.log('创建社区:', community.name)

  // 创建物品分类
  const categories = [
    { name: '电子产品', description: '手机、电脑、数码设备等', icon: '📱' },
    { name: '家居用品', description: '家具、装饰品、生活用品等', icon: '🏠' },
    { name: '服装配饰', description: '衣服、鞋子、包包等', icon: '👕' },
    { name: '图书文具', description: '书籍、文具、学习用品等', icon: '📚' },
    { name: '运动健身', description: '运动器材、健身用品等', icon: '⚽' },
    { name: '母婴用品', description: '婴儿用品、玩具等', icon: '🍼' },
    { name: '美妆护肤', description: '化妆品、护肤品等', icon: '💄' },
    { name: '其他', description: '其他物品', icon: '📦' }
  ]

  for (const categoryData of categories) {
    const existingCategory = await prisma.category.findFirst({
      where: { name: categoryData.name }
    })

    if (!existingCategory) {
      const category = await prisma.category.create({
        data: categoryData
      })
      console.log('创建分类:', category.name)
    } else {
      console.log('分类已存在:', existingCategory.name)
    }
  }

  // 创建示例用户
  const user = await prisma.user.upsert({
    where: { phone: 'demo_user_openid' },
    update: {},
    create: {
      phone: 'demo_user_openid',
      name: '示例用户',
      avatar: 'https://via.placeholder.com/100',
      communityId: community.id,
      trustLevel: 3,
      trustScore: 75,
      exchangeCount: 5,
      successRate: 0.9,
      averageRating: 4.5,
      isVerified: true,
      isActive: true
    }
  })
  console.log('创建示例用户:', user.name)

  // 创建示例物品
  const electronicsCategory = await prisma.category.findFirst({
    where: { name: '电子产品' }
  })

  if (electronicsCategory) {
    const item = await prisma.item.upsert({
      where: { id: 'demo-item-1' },
      update: {},
      create: {
        id: 'demo-item-1',
        title: 'iPhone 12 Pro',
        description: '九成新iPhone 12 Pro，256GB，深空灰色，无磕碰，功能正常。希望换一台安卓手机或者其他电子产品。',
        categoryId: electronicsCategory.id,
        condition: 'excellent',
        estimatedValue: 6000,
        status: 'available',
        publisherId: user.id,
        communityId: community.id,
        latitude: 39.9042,
        longitude: 116.4074,
        address: '北京市朝阳区某小区',
        viewCount: 25,
        favoriteCount: 3
      }
    })
    console.log('创建示例物品:', item.title)

    // 添加物品图片
    await prisma.itemImage.createMany({
      data: [
        {
          itemId: item.id,
          url: 'https://via.placeholder.com/400x300?text=iPhone+12+Pro+1',
          isPrimary: true
        },
        {
          itemId: item.id,
          url: 'https://via.placeholder.com/400x300?text=iPhone+12+Pro+2',
          isPrimary: false
        }
      ]
    })

    // 添加期望物品
    await prisma.desiredItem.createMany({
      data: [
        {
          itemId: item.id,
          description: '安卓旗舰手机（华为、小米、OPPO等）'
        },
        {
          itemId: item.id,
          description: '平板电脑或笔记本电脑'
        }
      ]
    })

    // 创建标签
    const tags = ['手机', '苹果', 'iPhone', '电子产品']
    for (const tagName of tags) {
      const tag = await prisma.tag.upsert({
        where: { name: tagName },
        update: {},
        create: { name: tagName }
      })

      const existingItemTag = await prisma.itemTag.findFirst({
        where: {
          itemId: item.id,
          tagId: tag.id
        }
      })

      if (!existingItemTag) {
        await prisma.itemTag.create({
          data: {
            itemId: item.id,
            tagId: tag.id
          }
        })
      }
    }
  }

  console.log('数据库初始化完成！')
}

main()
  .catch((e) => {
    console.error('数据库初始化失败:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
