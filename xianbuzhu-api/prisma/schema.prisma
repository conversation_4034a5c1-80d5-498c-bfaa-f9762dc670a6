// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 社区模型
model Community {
  id          String   @id @default(uuid())
  name        String
  description String?
  location    String
  latitude    Float
  longitude   Float
  radius      Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关系
  users       User[]
  items       Item[]
}

// 管理员用户模型
model AdminUser {
  id        String   @id @default(uuid())
  username  String   @unique
  password  String
  name      String
  role      String
  email     String?  @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 普通用户模型
model User {
  id            String    @id @default(uuid())
  phone         String?   @unique
  name          String
  avatar        String?
  communityId   String?
  trustLevel    Int       @default(1)
  trustScore    Float     @default(0)
  exchangeCount Int       @default(0)
  successRate   Float     @default(0)
  averageRating Float     @default(0)
  isVerified    Boolean   @default(false)
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 关系
  community     Community? @relation(fields: [communityId], references: [id])
  items         Item[]
  requestedExchanges Exchange[] @relation(name: "ExchangeRequester")
  providedExchanges Exchange[] @relation(name: "ExchangeProvider")
  sentMessages  Message[] @relation(name: "MessageSender")
  receivedMessages Message[] @relation(name: "MessageReceiver")
  favorites     Favorite[]
}

// 物品分类模型
model Category {
  id          String   @id @default(uuid())
  name        String
  description String?
  icon        String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关系
  parent      Category? @relation(name: "CategoryRelation", fields: [parentId], references: [id])
  children    Category[] @relation(name: "CategoryRelation")
  items       Item[]
}

// 物品模型
model Item {
  id              String    @id @default(uuid())
  title           String
  description     String
  categoryId      String
  condition       String
  estimatedValue  Float
  status          String
  publisherId     String
  communityId     String
  viewCount       Int       @default(0)
  favoriteCount   Int       @default(0)
  latitude        Float?
  longitude       Float?
  address         String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // 关系
  category        Category  @relation(fields: [categoryId], references: [id])
  publisher       User      @relation(fields: [publisherId], references: [id])
  community       Community @relation(fields: [communityId], references: [id])
  images          ItemImage[]
  desiredItems    DesiredItem[]
  exchanges       Exchange[]
  favorites       Favorite[]
  tags            ItemTag[]
}

// 物品图片模型
model ItemImage {
  id        String  @id @default(uuid())
  itemId    String
  url       String
  isPrimary Boolean @default(false)
  createdAt DateTime @default(now())

  // 关系
  item      Item @relation(fields: [itemId], references: [id], onDelete: Cascade)
}

// 期望物品模型
model DesiredItem {
  id          String  @id @default(uuid())
  itemId      String
  description String
  createdAt   DateTime @default(now())

  // 关系
  item        Item @relation(fields: [itemId], references: [id], onDelete: Cascade)
}

// 收藏模型
model Favorite {
  id        String  @id @default(uuid())
  userId    String
  itemId    String
  createdAt DateTime @default(now())

  // 关系
  user      User @relation(fields: [userId], references: [id], onDelete: Cascade)
  item      Item @relation(fields: [itemId], references: [id], onDelete: Cascade)

  // 复合唯一索引
  @@unique([userId, itemId])
}

// 交换记录模型
model Exchange {
  id            String    @id @default(uuid())
  requesterId   String
  providerId    String
  itemId        String
  status        String
  message       String?
  exchangeTime  DateTime?
  exchangePlace String?
  rating        Int?
  comment       String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 关系
  requester     User @relation(name: "ExchangeRequester", fields: [requesterId], references: [id])
  provider      User @relation(name: "ExchangeProvider", fields: [providerId], references: [id])
  item          Item @relation(fields: [itemId], references: [id])
}

// 消息模型
model Message {
  id          String    @id @default(uuid())
  senderId    String
  receiverId  String
  content     String
  type        String
  isRead      Boolean   @default(false)
  createdAt   DateTime  @default(now())

  // 关系
  sender      User @relation(name: "MessageSender", fields: [senderId], references: [id])
  receiver    User @relation(name: "MessageReceiver", fields: [receiverId], references: [id])
}

// 标签模型
model Tag {
  id        String    @id @default(uuid())
  name      String    @unique
  createdAt DateTime  @default(now())

  // 关系
  itemTags  ItemTag[]
}

// 物品标签关联表
model ItemTag {
  id        String  @id @default(uuid())
  itemId    String
  tagId     String

  // 关系
  item      Item @relation(fields: [itemId], references: [id], onDelete: Cascade)
  tag       Tag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  // 复合唯一索引
  @@unique([itemId, tagId])
}
