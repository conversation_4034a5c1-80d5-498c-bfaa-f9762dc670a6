-- 性能优化索引
-- 为常用查询字段添加索引以提升查询性能

-- 物品表索引优化
CREATE INDEX IF NOT EXISTS idx_items_status_community ON "Item" ("status", "communityId");
CREATE INDEX IF NOT EXISTS idx_items_category_status ON "Item" ("categoryId", "status");
CREATE INDEX IF NOT EXISTS idx_items_publisher_status ON "Item" ("publisherId", "status");
CREATE INDEX IF NOT EXISTS idx_items_created_at ON "Item" ("createdAt" DESC);
CREATE INDEX IF NOT EXISTS idx_items_estimated_value ON "Item" ("estimatedValue");
CREATE INDEX IF NOT EXISTS idx_items_condition ON "Item" ("condition");

-- 全文搜索索引（PostgreSQL）
CREATE INDEX IF NOT EXISTS idx_items_title_search ON "Item" USING gin(to_tsvector('english', "title"));
CREATE INDEX IF NOT EXISTS idx_items_description_search ON "Item" USING gin(to_tsvector('english', "description"));

-- 用户表索引优化
CREATE INDEX IF NOT EXISTS idx_users_community ON "User" ("communityId");
CREATE INDEX IF NOT EXISTS idx_users_phone ON "User" ("phone");
CREATE INDEX IF NOT EXISTS idx_users_openid ON "User" ("openid");
CREATE INDEX IF NOT EXISTS idx_users_active_verified ON "User" ("isActive", "isVerified");

-- 交换表索引优化
CREATE INDEX IF NOT EXISTS idx_exchanges_item_status ON "Exchange" ("itemId", "status");
CREATE INDEX IF NOT EXISTS idx_exchanges_requester_status ON "Exchange" ("requesterId", "status");
CREATE INDEX IF NOT EXISTS idx_exchanges_created_at ON "Exchange" ("createdAt" DESC);

-- 收藏表索引优化
CREATE INDEX IF NOT EXISTS idx_favorites_user_item ON "Favorite" ("userId", "itemId");
CREATE INDEX IF NOT EXISTS idx_favorites_item ON "Favorite" ("itemId");

-- 图片表索引优化
CREATE INDEX IF NOT EXISTS idx_item_images_item_order ON "ItemImage" ("itemId", "order");

-- 社区表索引优化
CREATE INDEX IF NOT EXISTS idx_communities_active ON "Community" ("isActive");

-- 分类表索引优化
CREATE INDEX IF NOT EXISTS idx_categories_active_order ON "Category" ("isActive", "order");

-- 复合索引用于复杂查询
CREATE INDEX IF NOT EXISTS idx_items_complex_search ON "Item" ("status", "communityId", "categoryId", "createdAt" DESC);
CREATE INDEX IF NOT EXISTS idx_items_price_range ON "Item" ("estimatedValue", "status", "communityId");
