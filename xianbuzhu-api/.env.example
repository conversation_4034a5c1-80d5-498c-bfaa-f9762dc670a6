# 环境配置
NODE_ENV=development
PORT=3000
API_VERSION=v1

# 数据库配置
DATABASE_URL=postgresql://postgres:postgres_password@localhost:5432/xianbuzhu_exchange

# Redis配置
REDIS_URL=redis://default:redis_password@localhost:6379

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d
ADMIN_JWT_SECRET=your-admin-jwt-secret
ADMIN_JWT_EXPIRES_IN=24h

# 微信小程序配置
WECHAT_APP_ID=wx1234567890abcdef
WECHAT_APP_SECRET=abcdef1234567890abcdef1234567890

# 腾讯云COS配置
COS_SECRET_ID=your-cos-secret-id
COS_SECRET_KEY=your-cos-secret-key
COS_BUCKET=your-bucket-name
COS_REGION=ap-beijing

# 腾讯地图配置
TENCENT_MAP_KEY=your-tencent-map-key

# 内容审核配置
CONTENT_AUDIT_SECRET_ID=your-content-audit-secret-id
CONTENT_AUDIT_SECRET_KEY=your-content-audit-secret-key

# 聊天服务配置
CHAT_SERVICE_URL=http://localhost:3002
CHAT_SERVICE_SECRET=your-chat-service-secret

# 其他配置
ALLOWED_ORIGINS=http://localhost:3001,http://localhost:3000
LOG_LEVEL=info
METRICS_PORT=9090
