/**
 * 错误码和错误消息定义
 */

export const ERROR_CODES = {
  // 通用错误 (1000-1999)
  UNKNOWN_ERROR: 'E1000',
  VALIDATION_ERROR: 'E1001',
  AUTHENTICATION_ERROR: 'E1002',
  AUTHORIZATION_ERROR: 'E1003',
  NOT_FOUND_ERROR: 'E1004',
  RATE_LIMIT_ERROR: 'E1005',
  NETWORK_ERROR: 'E1006',
  SERVER_ERROR: 'E1007',
  
  // 用户相关错误 (2000-2999)
  USER_NOT_FOUND: 'E2001',
  USER_ALREADY_EXISTS: 'E2002',
  INVALID_PHONE: 'E2003',
  PHONE_ALREADY_BOUND: 'E2004',
  INSUFFICIENT_TRUST: 'E2005',
  USER_BANNED: 'E2006',
  USER_INACTIVE: 'E2007',
  INVALID_USER_DATA: 'E2008',
  
  // 物品相关错误 (3000-3999)
  ITEM_NOT_FOUND: 'E3001',
  ITEM_NOT_AVAILABLE: 'E3002',
  ITEM_ALREADY_EXCHANGED: 'E3003',
  INVALID_ITEM_DATA: 'E3004',
  ITEM_OWNER_MISMATCH: 'E3005',
  ITEM_CATEGORY_INVALID: 'E3006',
  ITEM_IMAGES_LIMIT_EXCEEDED: 'E3007',
  ITEM_TITLE_TOO_LONG: 'E3008',
  ITEM_DESCRIPTION_TOO_LONG: 'E3009',
  
  // 交换相关错误 (4000-4999)
  EXCHANGE_NOT_FOUND: 'E4001',
  EXCHANGE_ALREADY_EXISTS: 'E4002',
  INVALID_EXCHANGE_STATUS: 'E4003',
  EXCHANGE_PERMISSION_DENIED: 'E4004',
  EXCHANGE_ALREADY_COMPLETED: 'E4005',
  EXCHANGE_CANCELLED: 'E4006',
  INVALID_EXCHANGE_RATING: 'E4007',
  EXCHANGE_DISPUTE_EXISTS: 'E4008',
  
  // 聊天相关错误 (5000-5999)
  CHAT_ROOM_NOT_FOUND: 'E5001',
  CHAT_PERMISSION_DENIED: 'E5002',
  MESSAGE_TOO_LONG: 'E5003',
  INVALID_MESSAGE_TYPE: 'E5004',
  CHAT_ROOM_FULL: 'E5005',
  MESSAGE_NOT_FOUND: 'E5006',
  VOICE_TOO_LONG: 'E5007',
  
  // 社区相关错误 (6000-6999)
  COMMUNITY_NOT_FOUND: 'E6001',
  COMMUNITY_ACCESS_DENIED: 'E6002',
  INVALID_LOCATION: 'E6003',
  LOCATION_OUT_OF_RANGE: 'E6004',
  COMMUNITY_INACTIVE: 'E6005',
  
  // 文件上传相关错误 (7000-7999)
  FILE_TOO_LARGE: 'E7001',
  INVALID_FILE_TYPE: 'E7002',
  UPLOAD_FAILED: 'E7003',
  FILE_NOT_FOUND: 'E7004',
  STORAGE_QUOTA_EXCEEDED: 'E7005',
  
  // 第三方服务错误 (8000-8999)
  WECHAT_API_ERROR: 'E8001',
  MAP_SERVICE_ERROR: 'E8002',
  SMS_SERVICE_ERROR: 'E8003',
  PAYMENT_SERVICE_ERROR: 'E8004',
  AUDIT_SERVICE_ERROR: 'E8005',
  
  // 业务逻辑错误 (9000-9999)
  BUSINESS_RULE_VIOLATION: 'E9001',
  OPERATION_NOT_ALLOWED: 'E9002',
  RESOURCE_CONFLICT: 'E9003',
  QUOTA_EXCEEDED: 'E9004',
  TIME_WINDOW_EXPIRED: 'E9005',
} as const

export const ERROR_MESSAGES = {
  // 通用错误消息
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误，请稍后重试',
  [ERROR_CODES.VALIDATION_ERROR]: '数据验证失败',
  [ERROR_CODES.AUTHENTICATION_ERROR]: '身份认证失败',
  [ERROR_CODES.AUTHORIZATION_ERROR]: '权限不足',
  [ERROR_CODES.NOT_FOUND_ERROR]: '请求的资源不存在',
  [ERROR_CODES.RATE_LIMIT_ERROR]: '请求过于频繁，请稍后重试',
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败',
  [ERROR_CODES.SERVER_ERROR]: '服务器内部错误',
  
  // 用户相关错误消息
  [ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
  [ERROR_CODES.USER_ALREADY_EXISTS]: '用户已存在',
  [ERROR_CODES.INVALID_PHONE]: '手机号格式不正确',
  [ERROR_CODES.PHONE_ALREADY_BOUND]: '手机号已被绑定',
  [ERROR_CODES.INSUFFICIENT_TRUST]: '信任度不足，无法执行此操作',
  [ERROR_CODES.USER_BANNED]: '用户已被封禁',
  [ERROR_CODES.USER_INACTIVE]: '用户账户未激活',
  [ERROR_CODES.INVALID_USER_DATA]: '用户数据格式不正确',
  
  // 物品相关错误消息
  [ERROR_CODES.ITEM_NOT_FOUND]: '物品不存在',
  [ERROR_CODES.ITEM_NOT_AVAILABLE]: '物品不可用',
  [ERROR_CODES.ITEM_ALREADY_EXCHANGED]: '物品已被交换',
  [ERROR_CODES.INVALID_ITEM_DATA]: '物品信息格式不正确',
  [ERROR_CODES.ITEM_OWNER_MISMATCH]: '您不是该物品的拥有者',
  [ERROR_CODES.ITEM_CATEGORY_INVALID]: '物品分类无效',
  [ERROR_CODES.ITEM_IMAGES_LIMIT_EXCEEDED]: '物品图片数量超出限制',
  [ERROR_CODES.ITEM_TITLE_TOO_LONG]: '物品标题过长',
  [ERROR_CODES.ITEM_DESCRIPTION_TOO_LONG]: '物品描述过长',
  
  // 交换相关错误消息
  [ERROR_CODES.EXCHANGE_NOT_FOUND]: '交换记录不存在',
  [ERROR_CODES.EXCHANGE_ALREADY_EXISTS]: '交换请求已存在',
  [ERROR_CODES.INVALID_EXCHANGE_STATUS]: '无效的交换状态',
  [ERROR_CODES.EXCHANGE_PERMISSION_DENIED]: '无权限操作此交换',
  [ERROR_CODES.EXCHANGE_ALREADY_COMPLETED]: '交换已完成',
  [ERROR_CODES.EXCHANGE_CANCELLED]: '交换已取消',
  [ERROR_CODES.INVALID_EXCHANGE_RATING]: '交换评分无效',
  [ERROR_CODES.EXCHANGE_DISPUTE_EXISTS]: '交换争议已存在',
  
  // 聊天相关错误消息
  [ERROR_CODES.CHAT_ROOM_NOT_FOUND]: '聊天室不存在',
  [ERROR_CODES.CHAT_PERMISSION_DENIED]: '无权限访问此聊天室',
  [ERROR_CODES.MESSAGE_TOO_LONG]: '消息内容过长',
  [ERROR_CODES.INVALID_MESSAGE_TYPE]: '无效的消息类型',
  [ERROR_CODES.CHAT_ROOM_FULL]: '聊天室人数已满',
  [ERROR_CODES.MESSAGE_NOT_FOUND]: '消息不存在',
  [ERROR_CODES.VOICE_TOO_LONG]: '语音消息过长',
  
  // 社区相关错误消息
  [ERROR_CODES.COMMUNITY_NOT_FOUND]: '社区不存在',
  [ERROR_CODES.COMMUNITY_ACCESS_DENIED]: '无权限访问此社区',
  [ERROR_CODES.INVALID_LOCATION]: '位置信息无效',
  [ERROR_CODES.LOCATION_OUT_OF_RANGE]: '位置超出服务范围',
  [ERROR_CODES.COMMUNITY_INACTIVE]: '社区已停用',
  
  // 文件上传相关错误消息
  [ERROR_CODES.FILE_TOO_LARGE]: '文件大小超出限制',
  [ERROR_CODES.INVALID_FILE_TYPE]: '不支持的文件类型',
  [ERROR_CODES.UPLOAD_FAILED]: '文件上传失败',
  [ERROR_CODES.FILE_NOT_FOUND]: '文件不存在',
  [ERROR_CODES.STORAGE_QUOTA_EXCEEDED]: '存储空间不足',
  
  // 第三方服务错误消息
  [ERROR_CODES.WECHAT_API_ERROR]: '微信服务异常',
  [ERROR_CODES.MAP_SERVICE_ERROR]: '地图服务异常',
  [ERROR_CODES.SMS_SERVICE_ERROR]: '短信服务异常',
  [ERROR_CODES.PAYMENT_SERVICE_ERROR]: '支付服务异常',
  [ERROR_CODES.AUDIT_SERVICE_ERROR]: '内容审核服务异常',
  
  // 业务逻辑错误消息
  [ERROR_CODES.BUSINESS_RULE_VIOLATION]: '违反业务规则',
  [ERROR_CODES.OPERATION_NOT_ALLOWED]: '操作不被允许',
  [ERROR_CODES.RESOURCE_CONFLICT]: '资源冲突',
  [ERROR_CODES.QUOTA_EXCEEDED]: '配额已用完',
  [ERROR_CODES.TIME_WINDOW_EXPIRED]: '操作时间窗口已过期',
} as const

// 错误类型分类
export const ERROR_CATEGORIES = {
  CLIENT_ERROR: [
    ERROR_CODES.VALIDATION_ERROR,
    ERROR_CODES.AUTHENTICATION_ERROR,
    ERROR_CODES.AUTHORIZATION_ERROR,
    ERROR_CODES.NOT_FOUND_ERROR,
    ERROR_CODES.RATE_LIMIT_ERROR,
  ],
  SERVER_ERROR: [
    ERROR_CODES.SERVER_ERROR,
    ERROR_CODES.UNKNOWN_ERROR,
    ERROR_CODES.NETWORK_ERROR,
  ],
  BUSINESS_ERROR: [
    ERROR_CODES.USER_NOT_FOUND,
    ERROR_CODES.ITEM_NOT_AVAILABLE,
    ERROR_CODES.EXCHANGE_ALREADY_EXISTS,
    ERROR_CODES.INSUFFICIENT_TRUST,
  ],
  EXTERNAL_ERROR: [
    ERROR_CODES.WECHAT_API_ERROR,
    ERROR_CODES.MAP_SERVICE_ERROR,
    ERROR_CODES.SMS_SERVICE_ERROR,
    ERROR_CODES.PAYMENT_SERVICE_ERROR,
    ERROR_CODES.AUDIT_SERVICE_ERROR,
  ],
} as const

// 错误严重程度
export const ERROR_SEVERITY = {
  LOW: ['VALIDATION_ERROR', 'NOT_FOUND_ERROR'],
  MEDIUM: ['AUTHENTICATION_ERROR', 'AUTHORIZATION_ERROR', 'RATE_LIMIT_ERROR'],
  HIGH: ['SERVER_ERROR', 'NETWORK_ERROR'],
  CRITICAL: ['UNKNOWN_ERROR'],
} as const