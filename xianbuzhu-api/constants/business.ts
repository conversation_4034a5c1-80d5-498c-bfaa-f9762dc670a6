/**
 * 业务常量定义
 */

export const BUSINESS_CONSTANTS = {
  // 物品相关
  ITEM: {
    MAX_IMAGES: 9,
    MAX_IMAGE_SIZE: 2 * 1024 * 1024, // 2MB
    MAX_TITLE_LENGTH: 100,
    MAX_DESCRIPTION_LENGTH: 1000,
    MIN_TITLE_LENGTH: 2,
    MIN_DESCRIPTION_LENGTH: 10,
    MAX_DESIRED_ITEMS: 10,
    MAX_ESTIMATED_VALUE: 999999,
  },
  
  // 用户相关
  USER: {
    MIN_TRUST_SCORE: 0,
    MAX_TRUST_SCORE: 1000,
    DEFAULT_TRUST_SCORE: 100,
    MAX_USERNAME_LENGTH: 20,
    MIN_USERNAME_LENGTH: 2,
    MAX_BIO_LENGTH: 200,
    TRUST_LEVELS: {
      1: { min: 0, max: 199, name: '新手' },
      2: { min: 200, max: 399, name: '熟练' },
      3: { min: 400, max: 599, name: '专家' },
      4: { min: 600, max: 799, name: '大师' },
      5: { min: 800, max: 1000, name: '传奇' },
    },
  },
  
  // 聊天相关
  CHAT: {
    MAX_MESSAGE_LENGTH: 500,
    MAX_VOICE_DURATION: 60, // 秒
    MAX_PARTICIPANTS: 10,
    MESSAGE_HISTORY_LIMIT: 100,
    TYPING_TIMEOUT: 3000, // 3秒
  },
  
  // 地理位置相关
  LOCATION: {
    DEFAULT_SEARCH_RADIUS: 5000, // 5公里
    MAX_SEARCH_RADIUS: 50000, // 50公里
    MIN_SEARCH_RADIUS: 100, // 100米
    COORDINATE_PRECISION: 6, // 经纬度精度
  },
  
  // 交换相关
  EXCHANGE: {
    MAX_MESSAGE_LENGTH: 200,
    MAX_COMMENT_LENGTH: 200,
    RATING_MIN: 1,
    RATING_MAX: 5,
    AUTO_COMPLETE_DAYS: 7, // 7天后自动完成
  },
  
  // 文件上传相关
  UPLOAD: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    ALLOWED_VOICE_TYPES: ['mp3', 'wav', 'aac', 'm4a'],
    MAX_UPLOAD_COUNT: 10,
  },
  
  // 分页相关
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    MIN_PAGE_SIZE: 1,
  },
  
  // 缓存相关
  CACHE: {
    USER_PROFILE_TTL: 3600, // 1小时
    ITEM_LIST_TTL: 300, // 5分钟
    SEARCH_RESULTS_TTL: 600, // 10分钟
    MAP_DATA_TTL: 7200, // 2小时
    COMMUNITY_LIST_TTL: 1800, // 30分钟
  },
  
  // 限流相关
  RATE_LIMIT: {
    LOGIN_ATTEMPTS: 5, // 5次/小时
    MESSAGE_SEND: 60, // 60条/分钟
    ITEM_PUBLISH: 10, // 10个/小时
    SEARCH_REQUESTS: 100, // 100次/分钟
  },
} as const

// 物品分类
export const ITEM_CATEGORIES = [
  { id: 'electronics', name: '数码电子', icon: '📱' },
  { id: 'books', name: '图书文具', icon: '📚' },
  { id: 'clothing', name: '服装配饰', icon: '👕' },
  { id: 'home', name: '家居用品', icon: '🏠' },
  { id: 'sports', name: '运动户外', icon: '⚽' },
  { id: 'toys', name: '玩具游戏', icon: '🧸' },
  { id: 'beauty', name: '美妆护肤', icon: '💄' },
  { id: 'food', name: '食品饮料', icon: '🍎' },
  { id: 'other', name: '其他物品', icon: '📦' },
] as const

// 物品状况选项
export const ITEM_CONDITIONS = [
  { value: 'new', label: '全新', description: '未使用过，包装完好' },
  { value: 'like_new', label: '几乎全新', description: '使用次数很少，无明显磨损' },
  { value: 'good', label: '良好', description: '正常使用，功能完好' },
  { value: 'fair', label: '一般', description: '有使用痕迹，但不影响使用' },
  { value: 'poor', label: '较差', description: '有明显磨损或小问题' },
] as const

// 交换状态
export const EXCHANGE_STATUSES = [
  { value: 'pending', label: '待回复', color: '#faad14' },
  { value: 'accepted', label: '已接受', color: '#52c41a' },
  { value: 'rejected', label: '已拒绝', color: '#f5222d' },
  { value: 'completed', label: '已完成', color: '#1890ff' },
  { value: 'disputed', label: '有争议', color: '#fa541c' },
  { value: 'cancelled', label: '已取消', color: '#8c8c8c' },
] as const

// 消息类型
export const MESSAGE_TYPES = [
  { value: 'text', label: '文本消息', icon: '💬' },
  { value: 'image', label: '图片消息', icon: '🖼️' },
  { value: 'voice', label: '语音消息', icon: '🎤' },
  { value: 'location', label: '位置消息', icon: '📍' },
  { value: 'item_share', label: '物品分享', icon: '📦' },
  { value: 'exchange_request', label: '交换请求', icon: '🔄' },
  { value: 'system', label: '系统消息', icon: '🤖' },
] as const