/**
 * 用户相关类型定义
 */

export interface User {
  id: string
  phone?: string
  name: string
  avatar?: string
  communityId?: string
  trustLevel: TrustLevel
  points: number
  isVerified: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface TrustLevel {
  level: number
  score: number
  exchangeCount: number
  successRate: number
  averageRating: number
}

export interface UserProfile extends Omit<User, 'id' | 'createdAt' | 'updatedAt'> {
  bio?: string
  preferences: UserPreferences
}

export interface UserPreferences {
  notifications: NotificationSettings
  privacy: PrivacySettings
  location: LocationSettings
}

export interface NotificationSettings {
  exchangeRequest: boolean
  chatMessage: boolean
  systemNotice: boolean
  emailNotification: boolean
}

export interface PrivacySettings {
  showPhone: boolean
  showLocation: boolean
  allowSearch: boolean
}

export interface LocationSettings {
  autoLocation: boolean
  defaultCommunityId?: string
}

export interface LoginRequest {
  code: string
}

export interface AuthResult {
  token: string
  user: User
  isNewUser: boolean
}

export interface BindPhoneRequest {
  encryptedData: string
  iv: string
}

export interface UpdateProfileRequest {
  name?: string
  avatar?: string
  bio?: string
  preferences?: Partial<UserPreferences>
}