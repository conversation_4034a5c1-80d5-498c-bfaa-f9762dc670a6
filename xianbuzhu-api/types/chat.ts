/**
 * 聊天相关类型定义
 */

export interface ChatRoom {
  id: string
  participants: ChatParticipant[]
  relatedItemId?: string
  exchangeId?: string
  lastMessage?: Message
  unreadCount?: Record<string, number>
  createdAt: Date
  updatedAt: Date
}

export interface ChatParticipant {
  id: string
  name: string
  avatar?: string
}

export interface Message {
  id: string
  roomId: string
  senderId: string
  content: string
  type: MessageType
  attachments?: Attachment[]
  sender?: ChatParticipant
  createdAt: Date
}

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  LOCATION = 'location',
  ITEM_SHARE = 'item_share',
  EXCHANGE_REQUEST = 'exchange_request',
  SYSTEM = 'system'
}

export interface Attachment {
  id: string
  type: 'image' | 'voice' | 'file'
  url: string
  size: number
  name?: string
  duration?: number // 语音时长（秒）
}

export interface SendMessageRequest {
  content: string
  type: MessageType
  attachments?: Attachment[]
}

export interface CreateChatRoomRequest {
  participants: string[]
  relatedItemId?: string
  exchangeId?: string
}

export interface MessageFilters {
  page: number
  pageSize: number
  roomId: string
  messageType?: MessageType
  dateFrom?: Date
  dateTo?: Date
}

// WebSocket事件类型
export interface SocketMessage {
  type: 'message' | 'notification' | 'exchange_update' | 'typing' | 'read'
  data: any
  timestamp: number
}

export interface TypingEvent {
  roomId: string
  userId: string
  isTyping: boolean
}

export interface ReadEvent {
  roomId: string
  userId: string
  messageId: string
  readAt: Date
}