/**
 * API相关类型定义
 */

import { ApiResponse } from './common'

// API端点常量
export const API_ENDPOINTS = {
  // 小程序端API
  MINIPROGRAM: {
    // 用户认证
    AUTH: {
      WECHAT_LOGIN: '/api/miniprogram/auth/wechat-login',
      BIND_PHONE: '/api/miniprogram/auth/bind-phone',
      VERIFY_IDENTITY: '/api/miniprogram/auth/verify-identity',
    },
    
    // 用户管理
    USER: {
      PROFILE: '/api/miniprogram/users/profile',
      UPDATE_PROFILE: '/api/miniprogram/users/profile',
      TRUST_LEVEL: (userId: string) => `/api/miniprogram/users/${userId}/trust`,
    },
    
    // 物品管理
    ITEM: {
      LIST: '/api/miniprogram/items',
      CREATE: '/api/miniprogram/items',
      DETAIL: (id: string) => `/api/miniprogram/items/${id}`,
      UPDATE: (id: string) => `/api/miniprogram/items/${id}`,
      DELETE: (id: string) => `/api/miniprogram/items/${id}`,
      SEARCH: '/api/miniprogram/items/search',
      FAVORITE: (id: string) => `/api/miniprogram/items/${id}/favorite`,
    },
    
    // 交换管理
    EXCHANGE: {
      REQUEST: '/api/miniprogram/exchanges/request',
      RESPOND: (id: string) => `/api/miniprogram/exchanges/${id}/respond`,
      CONFIRM: (id: string) => `/api/miniprogram/exchanges/${id}/confirm`,
      COMPLETE: (id: string) => `/api/miniprogram/exchanges/${id}/complete`,
      RATE: (id: string) => `/api/miniprogram/exchanges/${id}/rate`,
      DISPUTE: (id: string) => `/api/miniprogram/exchanges/${id}/dispute`,
    },
    
    // 社区管理
    COMMUNITY: {
      LIST: '/api/miniprogram/communities',
      DETAIL: (id: string) => `/api/miniprogram/communities/${id}`,
      ITEMS: (id: string) => `/api/miniprogram/communities/${id}/items`,
      MAP: '/api/miniprogram/communities/map',
    },
    
    // 文件上传
    UPLOAD: {
      IMAGE: '/api/miniprogram/upload/image',
      VOICE: '/api/miniprogram/upload/voice',
    },
  },
  
  // 管理后台API
  ADMIN: {
    // 认证
    AUTH: {
      LOGIN: '/api/admin/auth/login',
      LOGOUT: '/api/admin/auth/logout',
      PROFILE: '/api/admin/auth/profile',
    },
    
    // 仪表板
    DASHBOARD: {
      STATS: '/api/admin/dashboard/stats',
      CHARTS: '/api/admin/dashboard/charts',
    },
    
    // 用户管理
    USER: {
      LIST: '/api/admin/users',
      DETAIL: (id: string) => `/api/admin/users/${id}`,
      UPDATE_STATUS: (id: string) => `/api/admin/users/${id}/status`,
      WARN: (id: string) => `/api/admin/users/${id}/warn`,
      BAN: (id: string) => `/api/admin/users/${id}/ban`,
    },
    
    // 物品管理
    ITEM: {
      LIST: '/api/admin/items',
      DETAIL: (id: string) => `/api/admin/items/${id}`,
      UPDATE_STATUS: (id: string) => `/api/admin/items/${id}/status`,
      DELETE: (id: string) => `/api/admin/items/${id}`,
    },
    
    // 举报管理
    REPORT: {
      LIST: '/api/admin/reports',
      DETAIL: (id: string) => `/api/admin/reports/${id}`,
      HANDLE: (id: string) => `/api/admin/reports/${id}/handle`,
    },
    
    // 数据统计
    STATISTICS: {
      OVERVIEW: '/api/admin/statistics/overview',
      USERS: '/api/admin/statistics/users',
      ITEMS: '/api/admin/statistics/items',
      EXCHANGES: '/api/admin/statistics/exchanges',
      EXPORT: '/api/admin/statistics/export',
    },
    
    // 系统管理
    SYSTEM: {
      LOGS: '/api/admin/system/logs',
      STATUS: '/api/admin/system/status',
      CONFIG: '/api/admin/system/config',
    },
  },
  
  // 聊天服务API
  CHAT: {
    ROOMS: '/api/chat/rooms',
    CREATE_ROOM: '/api/chat/rooms',
    ROOM_DETAIL: (id: string) => `/api/chat/rooms/${id}`,
    MESSAGES: (roomId: string) => `/api/chat/rooms/${roomId}/messages`,
    SEND_MESSAGE: (roomId: string) => `/api/chat/rooms/${roomId}/messages`,
    MARK_READ: (roomId: string) => `/api/chat/rooms/${roomId}/read`,
  },
  
  // 健康检查
  HEALTH: {
    API_GATEWAY: '/api/health',
    CHAT_SERVICE: '/api/chat/health',
  },
} as const

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置
export interface RequestConfig {
  method?: HttpMethod
  headers?: Record<string, string>
  params?: Record<string, any>
  data?: any
  timeout?: number
}

// 响应拦截器类型
export interface ResponseInterceptor<T = any> {
  onFulfilled?: (response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>
  onRejected?: (error: any) => any
}

// 请求拦截器类型
export interface RequestInterceptor {
  onFulfilled?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
  onRejected?: (error: any) => any
}