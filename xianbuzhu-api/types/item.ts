/**
 * 物品相关类型定义
 */

export interface Item {
  id: string
  title: string
  description: string
  category: ItemCategory
  images: string[]
  condition: ItemCondition
  estimatedValue: number
  desiredItems: string[]
  status: ItemStatus
  publisherId: string
  communityId?: string
  location?: Location
  viewCount: number
  favoriteCount: number
  createdAt: Date
  updatedAt: Date
}

export enum ItemStatus {
  AVAILABLE = 'available',
  EXCHANGING = 'exchanging',
  EXCHANGED = 'exchanged',
  REMOVED = 'removed'
}

export enum ItemCondition {
  NEW = 'new',
  LIKE_NEW = 'like_new',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

export interface ItemCategory {
  id: string
  name: string
  parentId?: string
  icon?: string
  description?: string
}

export interface Location {
  latitude: number
  longitude: number
  address?: string
}

export interface CreateItemRequest {
  title: string
  description: string
  category: string
  images: string[]
  condition: ItemCondition
  estimatedValue: number
  desiredItems: string[]
  location?: Location
  tags?: string[]
}

export interface UpdateItemRequest extends Partial<CreateItemRequest> {
  status?: ItemStatus
}

export interface ItemFilters {
  page: number
  pageSize: number
  communityId?: string
  category?: string
  condition?: ItemCondition
  search?: string
  minValue?: number
  maxValue?: number
  location?: {
    latitude: number
    longitude: number
    radius: number
  }
}

export interface ItemSearchQuery {
  keyword: string
  filters?: Omit<ItemFilters, 'search'>
}

/**
 * 搜索物品请求
 */
export interface SearchItemRequest {
  keyword?: string
  category?: string
  minEstimatedValue?: number
  maxEstimatedValue?: number
  location?: {
    latitude: number
    longitude: number
    radius: number
  }
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}