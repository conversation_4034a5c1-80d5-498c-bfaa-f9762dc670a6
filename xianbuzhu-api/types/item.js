"use strict";
/**
 * 物品相关类型定义
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemCondition = exports.ItemStatus = void 0;
var ItemStatus;
(function (ItemStatus) {
    ItemStatus["AVAILABLE"] = "available";
    ItemStatus["EXCHANGING"] = "exchanging";
    ItemStatus["EXCHANGED"] = "exchanged";
    ItemStatus["REMOVED"] = "removed";
})(ItemStatus = exports.ItemStatus || (exports.ItemStatus = {}));
var ItemCondition;
(function (ItemCondition) {
    ItemCondition["NEW"] = "new";
    ItemCondition["LIKE_NEW"] = "like_new";
    ItemCondition["GOOD"] = "good";
    ItemCondition["FAIR"] = "fair";
    ItemCondition["POOR"] = "poor";
})(ItemCondition = exports.ItemCondition || (exports.ItemCondition = {}));
//# sourceMappingURL=item.js.map