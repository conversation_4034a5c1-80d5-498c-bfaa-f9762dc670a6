/**
 * 通用类型定义
 */

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: ApiError
  timestamp: string
  requestId?: string
}

export interface ApiError {
  code: string
  message: string
  details?: any
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

export interface PaginationParams {
  page: number
  pageSize: number
}

export interface SortParams {
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

export interface DateRange {
  startDate: Date
  endDate: Date
}

export interface FileUpload {
  file: File | string
  type: 'image' | 'voice' | 'document'
  category?: string
}

export interface UploadResult {
  url: string
  filename: string
  size: number
  type: string
}

// 统计相关
export interface StatisticsData {
  label: string
  value: number
  change?: number
  changeType?: 'increase' | 'decrease' | 'stable'
}

export interface ChartData {
  date: string
  value: number
  category?: string
}

export interface DashboardStats {
  totalUsers: number
  totalItems: number
  totalExchanges: number
  activeUsers: number
  successRate: number
  growthRate: number
}

// 地理位置相关
export interface GeoLocation {
  latitude: number
  longitude: number
  accuracy?: number
  address?: string
}

export interface DistanceInfo {
  distance: number
  unit: 'km' | 'm'
  duration?: number
}

// 通知相关
export interface Notification {
  id: string
  userId: string
  type: NotificationType
  title: string
  content: string
  data?: any
  isRead: boolean
  createdAt: Date
}

export enum NotificationType {
  EXCHANGE_REQUEST = 'exchange_request',
  EXCHANGE_ACCEPTED = 'exchange_accepted',
  EXCHANGE_REJECTED = 'exchange_rejected',
  EXCHANGE_COMPLETED = 'exchange_completed',
  CHAT_MESSAGE = 'chat_message',
  SYSTEM_NOTICE = 'system_notice',
  ITEM_LIKED = 'item_liked',
  ITEM_COMMENTED = 'item_commented'
}