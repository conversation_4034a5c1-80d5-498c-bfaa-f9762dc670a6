/**
 * 交换相关类型定义
 */

export interface Exchange {
  id: string
  requesterId: string
  targetItemId: string
  offeredItemId: string
  status: ExchangeStatus
  message: string
  responseMessage?: string
  confirmedAt?: Date
  completedAt?: Date
  createdAt: Date
  updatedAt: Date
}

export enum ExchangeStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  COMPLETED = 'completed',
  DISPUTED = 'disputed',
  CANCELLED = 'cancelled'
}

export interface ExchangeRequest {
  itemId: string
  targetItemId: string
  message: string
  meetLocation?: string
  meetTime?: string
}

export interface ExchangeResponse {
  exchangeId: string
  status: 'accept' | 'reject'
  message?: string
  meetLocation?: string
  meetTime?: string
}

export interface ExchangeConfirmation {
  confirmed: boolean
  rating?: number
  comment?: string
}

export interface ExchangeRating {
  rating: number
  comment?: string
}

/**
 * 确认交换请求
 */
export interface ConfirmExchangeRequest {
  confirmed: boolean
  rating?: number
  comment?: string
}

/**
 * 完成交换请求
 */
export interface CompleteExchangeRequest {
  completed: boolean
  feedback?: string
}

export interface ExchangeDispute {
  reason: string
  description: string
  evidence?: string[]
}

export interface ExchangeFilters {
  page: number
  pageSize: number
  status?: ExchangeStatus
  userId?: string
  dateFrom?: Date
  dateTo?: Date
}

/**
 * 提交争议请求
 */
export interface DisputeExchangeRequest {
  exchangeId: string
  dispute: ExchangeDispute
}

/**
 * 评价交换请求
 */
export interface RateExchangeRequest {
  rating: number
  comment?: string
}