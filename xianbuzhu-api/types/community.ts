/**
 * 社区相关类型定义
 */

export interface Community {
  id: string
  name: string
  address: string
  latitude?: number
  longitude?: number
  boundary?: GeoPolygon
  description?: string
  memberCount: number
  itemCount: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface GeoPolygon {
  type: 'Polygon'
  coordinates: number[][][]
}

export interface CommunityStats {
  totalMembers: number
  activeMembers: number
  totalItems: number
  availableItems: number
  totalExchanges: number
  successfulExchanges: number
}

export interface CommunityFilters {
  page: number
  pageSize: number
  search?: string
  location?: {
    latitude: number
    longitude: number
    radius: number
  }
  isActive?: boolean
}

export interface CreateCommunityRequest {
  name: string
  address: string
  latitude?: number
  longitude?: number
  boundary?: GeoPolygon
  description?: string
}

export interface UpdateCommunityRequest extends Partial<CreateCommunityRequest> {
  isActive?: boolean
}

/**
 * 加入社区请求
 */
export interface JoinCommunityRequest {
  communityId: string
  reason: string
}