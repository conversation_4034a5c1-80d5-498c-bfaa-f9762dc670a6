{"name": "xianbuzhu-api", "version": "1.0.0", "description": "社区闲置物品置换平台API网关服务", "main": "dist/app.js", "scripts": {"dev": "nodemon --exec ts-node -P tsconfig.json src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "docker:build": "docker build -t xianbuzhu-api .", "docker:run": "docker run -p 3000:3000 xianbuzhu-api"}, "keywords": ["nodejs", "express", "typescript", "api-gateway", "postgresql", "redis", "prisma", "wechat"], "author": "Community Exchange Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^6.1.5", "express-rate-limit": "^6.7.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "winston": "^3.8.2", "redis": "^4.6.7", "@prisma/client": "^4.15.0", "prisma": "^4.15.0", "axios": "^1.4.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "node-cron": "^3.0.2", "prom-client": "^14.2.0", "dotenv": "^16.1.4", "cos-nodejs-sdk-v5": "^2.11.22", "tencentcloud-sdk-nodejs": "^4.0.3"}, "devDependencies": {"@types/node": "^20.2.5", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/joi": "^17.2.3", "@types/multer": "^1.4.7", "@types/node-cron": "^3.0.7", "@types/jest": "^29.5.2", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "eslint": "^8.41.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "nodemon": "^2.0.22", "supertest": "^6.3.3", "typescript": "^5.1.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}