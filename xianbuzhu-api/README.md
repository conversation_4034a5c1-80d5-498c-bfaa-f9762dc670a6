# API网关服务 (API Gateway)

## 项目概述

这是社区闲置物品置换平台的API网关服务，负责处理所有HTTP API请求，包括用户认证、物品管理、交换流程、社区管理等核心业务逻辑。

## 功能职责

### 🎯 **核心职责**
- 用户认证与授权管理
- 物品发布、编辑、搜索功能
- 交换请求与流程管理
- 社区管理与地理位置服务
- 内容审核与安全防护
- 数据统计与分析
- 文件上传与存储管理

### 🔌 **API服务**
- 微信小程序API (`/api/miniprogram/*`)
- 管理后台API (`/api/admin/*`)
- 文件上传API (`/api/upload/*`)
- 健康检查API (`/api/health`)

## 技术栈

- **运行环境**: Node.js 18+
- **框架**: Express.js + TypeScript
- **数据库**: PostgreSQL 14 (主数据库)
- **缓存**: Redis 6 (缓存和会话)
- **ORM**: Prisma
- **认证**: JWT + 微信小程序认证
- **文件存储**: 腾讯云COS/阿里云OSS
- **地图服务**: 腾讯位置服务API
- **内容审核**: 腾讯云内容安全API

## 项目结构

```
api-gateway/
├── src/
│   ├── controllers/           # 控制器层
│   │   ├── miniprogram/      # 小程序API控制器
│   │   │   ├── authController.ts
│   │   │   ├── userController.ts
│   │   │   ├── itemController.ts
│   │   │   ├── exchangeController.ts
│   │   │   └── communityController.ts
│   │   └── admin/            # 管理后台API控制器
│   │       ├── authController.ts
│   │       ├── userController.ts
│   │       ├── itemController.ts
│   │       ├── statisticsController.ts
│   │       └── systemController.ts
│   ├── services/             # 业务逻辑层
│   │   ├── userService.ts
│   │   ├── itemService.ts
│   │   ├── exchangeService.ts
│   │   ├── communityService.ts
│   │   ├── fileService.ts
│   │   ├── auditService.ts
│   │   └── statisticsService.ts
│   ├── repositories/         # 数据访问层
│   │   ├── userRepository.ts
│   │   ├── itemRepository.ts
│   │   ├── exchangeRepository.ts
│   │   └── communityRepository.ts
│   ├── middleware/           # 中间件
│   │   ├── auth.ts          # 认证中间件
│   │   ├── validation.ts    # 数据验证
│   │   ├── rateLimit.ts     # 频率限制
│   │   ├── cors.ts          # 跨域处理
│   │   └── errorHandler.ts  # 错误处理
│   ├── routes/              # 路由定义
│   │   ├── miniprogram.ts   # 小程序路由
│   │   ├── admin.ts         # 管理后台路由
│   │   └── upload.ts        # 文件上传路由
│   ├── utils/               # 工具函数
│   │   ├── wechat.ts        # 微信API工具
│   │   ├── crypto.ts        # 加密工具
│   │   ├── location.ts      # 地理位置工具
│   │   └── logger.ts        # 日志工具
│   ├── config/              # 配置文件
│   │   ├── database.ts      # 数据库配置
│   │   ├── redis.ts         # Redis配置
│   │   ├── storage.ts       # 存储配置
│   │   └── wechat.ts        # 微信配置
│   └── types/               # 类型定义
├── prisma/                  # 数据库Schema
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── tests/                   # 测试文件
├── docs/                    # API文档
└── docker/                  # Docker配置
```

## 环境配置

### 环境变量
```bash
# .env
# 服务配置
NODE_ENV=development
PORT=3000
API_VERSION=v1

# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/community_exchange"
REDIS_URL="redis://localhost:6379"

# JWT配置
JWT_SECRET="your-jwt-secret-key"
JWT_EXPIRES_IN="7d"
ADMIN_JWT_SECRET="your-admin-jwt-secret"
ADMIN_JWT_EXPIRES_IN="24h"

# 微信小程序配置
WECHAT_APP_ID="your-wechat-app-id"
WECHAT_APP_SECRET="your-wechat-app-secret"

# 文件存储配置 (腾讯云COS)
COS_SECRET_ID="your-cos-secret-id"
COS_SECRET_KEY="your-cos-secret-key"
COS_BUCKET="your-bucket-name"
COS_REGION="your-region"

# 地图服务配置
TENCENT_MAP_KEY="your-tencent-map-key"

# 内容审核配置
CONTENT_AUDIT_SECRET_ID="your-audit-secret-id"
CONTENT_AUDIT_SECRET_KEY="your-audit-secret-key"

# 聊天服务配置
CHAT_SERVICE_URL="http://localhost:3001"
CHAT_SERVICE_SECRET="your-chat-service-secret"
```

## 核心功能实现

### 1. 用户认证服务

```typescript
// src/services/userService.ts
import { PrismaClient } from '@prisma/client'
import { WeChatService } from '../utils/wechat'
import { JWTService } from '../utils/jwt'
import { User, LoginRequest, AuthResult } from '../types'

export class UserService {
  constructor(
    private prisma: PrismaClient,
    private wechatService: WeChatService,
    private jwtService: JWTService
  ) {}

  async wechatLogin(loginData: LoginRequest): Promise<AuthResult> {
    // 1. 通过微信API获取用户信息
    const wechatUser = await this.wechatService.getUser(loginData.code)
    
    // 2. 查找或创建用户
    let user = await this.prisma.user.findUnique({
      where: { wechatOpenId: wechatUser.openid }
    })
    
    let isNewUser = false
    if (!user) {
      user = await this.prisma.user.create({
        data: {
          wechatOpenId: wechatUser.openid,
          name: wechatUser.nickname,
          avatar: wechatUser.avatarUrl,
          trustScore: 100,
          points: 0,
        }
      })
      isNewUser = true
    }

    // 3. 生成JWT token
    const token = this.jwtService.sign({
      userId: user.id,
      type: 'user'
    })

    return {
      token,
      user: this.formatUser(user),
      isNewUser
    }
  }

  async bindPhone(userId: string, encryptedData: string, iv: string): Promise<User> {
    // 解密手机号
    const phoneData = await this.wechatService.decryptData(encryptedData, iv)
    
    // 更新用户手机号
    const user = await this.prisma.user.update({
      where: { id: userId },
      data: { phone: phoneData.phoneNumber }
    })

    return this.formatUser(user)
  }

  private formatUser(user: any): User {
    return {
      id: user.id,
      phone: user.phone,
      name: user.name,
      avatar: user.avatar,
      communityId: user.communityId,
      trustLevel: {
        level: Math.floor(user.trustScore / 200) + 1,
        score: user.trustScore,
        exchangeCount: user.exchangeCount || 0,
        successRate: user.successRate || 0,
        averageRating: user.averageRating || 0,
      },
      points: user.points,
      isVerified: user.isVerified,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  }
}
```

### 2. 物品管理服务

```typescript
// src/services/itemService.ts
import { PrismaClient } from '@prisma/client'
import { Item, CreateItemRequest, ItemFilters } from '../types'
import { AuditService } from './auditService'
import { FileService } from './fileService'

export class ItemService {
  constructor(
    private prisma: PrismaClient,
    private auditService: AuditService,
    private fileService: FileService
  ) {}

  async createItem(userId: string, itemData: CreateItemRequest): Promise<Item> {
    // 1. 内容审核
    const auditResult = await this.auditService.auditContent({
      text: `${itemData.title} ${itemData.description}`,
      images: itemData.images
    })

    if (!auditResult.pass) {
      throw new Error('内容审核不通过')
    }

    // 2. 处理图片
    const processedImages = await Promise.all(
      itemData.images.map(image => this.fileService.processImage(image))
    )

    // 3. 创建物品记录
    const item = await this.prisma.item.create({
      data: {
        title: itemData.title,
        description: itemData.description,
        categoryId: itemData.category,
        condition: itemData.condition,
        estimatedValue: itemData.estimatedValue,
        desiredItems: itemData.desiredItems,
        publisherId: userId,
        status: 'available',
        images: {
          create: processedImages.map((url, index) => ({
            imageUrl: url,
            sortOrder: index
          }))
        }
      },
      include: {
        images: true,
        publisher: true,
        category: true
      }
    })

    return this.formatItem(item)
  }

  async getItems(filters: ItemFilters): Promise<{ items: Item[], total: number }> {
    const where: any = {
      status: 'available'
    }

    if (filters.communityId) {
      where.communityId = filters.communityId
    }

    if (filters.category) {
      where.categoryId = filters.category
    }

    if (filters.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } }
      ]
    }

    const [items, total] = await Promise.all([
      this.prisma.item.findMany({
        where,
        include: {
          images: true,
          publisher: true,
          category: true
        },
        orderBy: { createdAt: 'desc' },
        skip: (filters.page - 1) * filters.pageSize,
        take: filters.pageSize
      }),
      this.prisma.item.count({ where })
    ])

    return {
      items: items.map(item => this.formatItem(item)),
      total
    }
  }

  private formatItem(item: any): Item {
    return {
      id: item.id,
      title: item.title,
      description: item.description,
      category: item.category,
      images: item.images.map((img: any) => img.imageUrl),
      condition: item.condition,
      estimatedValue: item.estimatedValue,
      desiredItems: item.desiredItems,
      status: item.status,
      publisherId: item.publisherId,
      communityId: item.communityId,
      location: {
        latitude: item.latitude,
        longitude: item.longitude
      },
      viewCount: item.viewCount,
      favoriteCount: item.favoriteCount,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt
    }
  }
}
```

### 3. 交换管理服务

```typescript
// src/services/exchangeService.ts
import { PrismaClient } from '@prisma/client'
import { Exchange, ExchangeRequest, ExchangeResponse } from '../types'
import { ChatServiceClient } from '../utils/chatServiceClient'

export class ExchangeService {
  constructor(
    private prisma: PrismaClient,
    private chatServiceClient: ChatServiceClient
  ) {}

  async createExchangeRequest(userId: string, requestData: ExchangeRequest): Promise<Exchange> {
    // 1. 验证物品状态
    const [targetItem, offeredItem] = await Promise.all([
      this.prisma.item.findUnique({ where: { id: requestData.targetItemId } }),
      this.prisma.item.findUnique({ where: { id: requestData.offeredItemId } })
    ])

    if (!targetItem || targetItem.status !== 'available') {
      throw new Error('目标物品不可用')
    }

    if (!offeredItem || offeredItem.publisherId !== userId) {
      throw new Error('提供的物品不属于当前用户')
    }

    // 2. 检查是否已存在交换请求
    const existingExchange = await this.prisma.exchange.findFirst({
      where: {
        requesterId: userId,
        targetItemId: requestData.targetItemId,
        status: { in: ['pending', 'accepted'] }
      }
    })

    if (existingExchange) {
      throw new Error('交换请求已存在')
    }

    // 3. 创建交换记录
    const exchange = await this.prisma.exchange.create({
      data: {
        requesterId: userId,
        targetItemId: requestData.targetItemId,
        offeredItemId: requestData.offeredItemId,
        message: requestData.message,
        status: 'pending'
      },
      include: {
        requester: true,
        targetItem: true,
        offeredItem: true
      }
    })

    // 4. 创建聊天室
    await this.chatServiceClient.createChatRoom({
      participants: [userId, targetItem.publisherId],
      relatedItemId: requestData.targetItemId,
      exchangeId: exchange.id
    })

    // 5. 发送通知
    await this.sendExchangeNotification(exchange, 'request_created')

    return this.formatExchange(exchange)
  }

  async respondToExchange(exchangeId: string, userId: string, response: ExchangeResponse): Promise<Exchange> {
    const exchange = await this.prisma.exchange.findUnique({
      where: { id: exchangeId },
      include: {
        targetItem: true,
        offeredItem: true,
        requester: true
      }
    })

    if (!exchange) {
      throw new Error('交换请求不存在')
    }

    if (exchange.targetItem.publisherId !== userId) {
      throw new Error('无权限操作此交换请求')
    }

    // 更新交换状态
    const updatedExchange = await this.prisma.exchange.update({
      where: { id: exchangeId },
      data: {
        status: response.action === 'accept' ? 'accepted' : 'rejected',
        responseMessage: response.message
      },
      include: {
        requester: true,
        targetItem: true,
        offeredItem: true
      }
    })

    // 发送通知
    await this.sendExchangeNotification(updatedExchange, `request_${response.action}ed`)

    return this.formatExchange(updatedExchange)
  }

  private async sendExchangeNotification(exchange: any, type: string) {
    // 发送微信模板消息通知
    // 实现略...
  }

  private formatExchange(exchange: any): Exchange {
    return {
      id: exchange.id,
      requesterId: exchange.requesterId,
      targetItemId: exchange.targetItemId,
      offeredItemId: exchange.offeredItemId,
      status: exchange.status,
      message: exchange.message,
      confirmedAt: exchange.confirmedAt,
      completedAt: exchange.completedAt,
      createdAt: exchange.createdAt,
      updatedAt: exchange.updatedAt
    }
  }
}
```

## API路由定义

### 小程序API路由
```typescript
// src/routes/miniprogram.ts
import { Router } from 'express'
import { authMiddleware } from '../middleware/auth'
import { validateBody } from '../middleware/validation'
import { AuthController } from '../controllers/miniprogram/authController'
import { ItemController } from '../controllers/miniprogram/itemController'
import { ExchangeController } from '../controllers/miniprogram/exchangeController'

const router = Router()
const authController = new AuthController()
const itemController = new ItemController()
const exchangeController = new ExchangeController()

// 认证相关
router.post('/auth/wechat-login', validateBody(loginSchema), authController.wechatLogin)
router.post('/auth/bind-phone', authMiddleware, validateBody(bindPhoneSchema), authController.bindPhone)

// 物品相关
router.get('/items', itemController.getItems)
router.post('/items', authMiddleware, validateBody(createItemSchema), itemController.createItem)
router.get('/items/:id', itemController.getItemById)
router.put('/items/:id', authMiddleware, validateBody(updateItemSchema), itemController.updateItem)
router.delete('/items/:id', authMiddleware, itemController.deleteItem)

// 交换相关
router.post('/exchanges/request', authMiddleware, validateBody(exchangeRequestSchema), exchangeController.createRequest)
router.put('/exchanges/:id/respond', authMiddleware, validateBody(exchangeResponseSchema), exchangeController.respondToRequest)
router.put('/exchanges/:id/confirm', authMiddleware, exchangeController.confirmExchange)

export default router
```

### 管理后台API路由
```typescript
// src/routes/admin.ts
import { Router } from 'express'
import { adminAuthMiddleware } from '../middleware/auth'
import { AdminAuthController } from '../controllers/admin/authController'
import { AdminUserController } from '../controllers/admin/userController'
import { AdminStatisticsController } from '../controllers/admin/statisticsController'

const router = Router()
const authController = new AdminAuthController()
const userController = new AdminUserController()
const statisticsController = new AdminStatisticsController()

// 管理员认证
router.post('/auth/login', authController.login)
router.post('/auth/logout', adminAuthMiddleware, authController.logout)

// 用户管理
router.get('/users', adminAuthMiddleware, userController.getUsers)
router.get('/users/:id', adminAuthMiddleware, userController.getUserById)
router.put('/users/:id/status', adminAuthMiddleware, userController.updateUserStatus)

// 数据统计
router.get('/statistics/overview', adminAuthMiddleware, statisticsController.getOverview)
router.get('/statistics/charts', adminAuthMiddleware, statisticsController.getCharts)

export default router
```

## 中间件

### 认证中间件
```typescript
// src/middleware/auth.ts
import { Request, Response, NextFunction } from 'express'
import { JWTService } from '../utils/jwt'
import { ERROR_CODES } from '../types'

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '')
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: ERROR_CODES.AUTHENTICATION_ERROR,
          message: '缺少认证token'
        }
      })
    }

    const payload = JWTService.verify(token)
    req.user = payload
    next()
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        code: ERROR_CODES.AUTHENTICATION_ERROR,
        message: '无效的认证token'
      }
    })
  }
}

export const adminAuthMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '')
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: ERROR_CODES.AUTHENTICATION_ERROR,
          message: '缺少管理员认证token'
        }
      })
    }

    const payload = JWTService.verifyAdmin(token)
    if (payload.type !== 'admin') {
      throw new Error('非管理员用户')
    }

    req.admin = payload
    next()
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        code: ERROR_CODES.AUTHORIZATION_ERROR,
        message: '管理员权限验证失败'
      }
    })
  }
}
```

### 数据验证中间件
```typescript
// src/middleware/validation.ts
import { Request, Response, NextFunction } from 'express'
import Joi from 'joi'
import { ERROR_CODES } from '../types'

export const validateBody = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body)
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: ERROR_CODES.VALIDATION_ERROR,
          message: error.details[0].message,
          details: error.details
        }
      })
    }
    
    next()
  }
}

// 验证规则
export const loginSchema = Joi.object({
  code: Joi.string().required()
})

export const createItemSchema = Joi.object({
  title: Joi.string().required().max(100),
  description: Joi.string().required().max(1000),
  category: Joi.string().required(),
  images: Joi.array().items(Joi.string().uri()).max(9).required(),
  condition: Joi.string().valid('new', 'like_new', 'good', 'fair', 'poor').required(),
  estimatedValue: Joi.number().min(0).required(),
  desiredItems: Joi.array().items(Joi.string()).max(10)
})
```

## 数据库Schema

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String   @id @default(cuid())
  wechatOpenId  String   @unique
  phone         String?  @unique
  name          String
  avatar        String?
  communityId   String?
  trustScore    Int      @default(100)
  points        Int      @default(0)
  isVerified    Boolean  @default(false)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  publishedItems Item[]     @relation("PublishedItems")
  exchanges      Exchange[] @relation("ExchangeRequester")
  community      Community? @relation(fields: [communityId], references: [id])

  @@map("users")
}

model Community {
  id        String   @id @default(cuid())
  name      String
  address   String
  latitude  Float?
  longitude Float?
  boundary  Json?
  createdAt DateTime @default(now())

  // 关联关系
  users User[]
  items Item[]

  @@map("communities")
}

model ItemCategory {
  id          String  @id @default(cuid())
  name        String
  parentId    String?
  icon        String?
  description String?

  // 关联关系
  items    Item[]
  parent   ItemCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children ItemCategory[] @relation("CategoryHierarchy")

  @@map("item_categories")
}

model Item {
  id             String      @id @default(cuid())
  title          String
  description    String
  categoryId     String
  condition      String
  estimatedValue Int
  desiredItems   String[]
  status         String      @default("available")
  publisherId    String
  communityId    String?
  latitude       Float?
  longitude      Float?
  viewCount      Int         @default(0)
  favoriteCount  Int         @default(0)
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // 关联关系
  publisher    User           @relation("PublishedItems", fields: [publisherId], references: [id])
  community    Community?     @relation(fields: [communityId], references: [id])
  category     ItemCategory   @relation(fields: [categoryId], references: [id])
  images       ItemImage[]
  exchanges    Exchange[]     @relation("TargetItem")
  offeredIn    Exchange[]     @relation("OfferedItem")

  @@map("items")
}

model ItemImage {
  id        String   @id @default(cuid())
  itemId    String
  imageUrl  String
  sortOrder Int      @default(0)
  createdAt DateTime @default(now())

  // 关联关系
  item Item @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@map("item_images")
}

model Exchange {
  id              String    @id @default(cuid())
  requesterId     String
  targetItemId    String
  offeredItemId   String
  status          String    @default("pending")
  message         String
  responseMessage String?
  confirmedAt     DateTime?
  completedAt     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // 关联关系
  requester    User @relation("ExchangeRequester", fields: [requesterId], references: [id])
  targetItem   Item @relation("TargetItem", fields: [targetItemId], references: [id])
  offeredItem  Item @relation("OfferedItem", fields: [offeredItemId], references: [id])

  @@map("exchanges")
}
```

## 部署配置

### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY prisma ./prisma/

# 安装依赖
RUN npm ci --only=production

# 生成Prisma客户端
RUN npx prisma generate

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 更改文件所有权
RUN chown -R nodejs:nodejs /app
USER nodejs

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
```

## 开发和测试

### 启动开发服务器
```bash
# 安装依赖
npm install

# 配置环境变量
cp .env.example .env

# 运行数据库迁移
npx prisma migrate dev

# 启动开发服务器
npm run dev
```

### 测试
```bash
# 单元测试
npm run test

# 集成测试
npm run test:integration

# 测试覆盖率
npm run test:coverage
```

## 监控和日志

### 健康检查
```typescript
// src/routes/health.ts
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
    uptime: process.uptime()
  })
})
```

### 日志配置
```typescript
// src/utils/logger.ts
import winston from 'winston'

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
})
```

## 相关项目

- [聊天服务](../chat-service/README.md)
- [微信小程序](../miniprogram/README.md)
- [管理后台](../admin-dashboard/README.md)

## 许可证

本项目采用 MIT 许可证。