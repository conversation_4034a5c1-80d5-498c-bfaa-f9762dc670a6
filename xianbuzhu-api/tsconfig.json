{"compilerOptions": {"target": "ES2016", "module": "CommonJS", "outDir": "./dist", "rootDir": ".", "strict": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "typeRoots": ["/Users/<USER>/code/trae/xianbuzhu-exchange/node_modules/@types"], "types": ["node", "jest"], "skipDefaultLibCheck": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "declaration": false, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist"]}