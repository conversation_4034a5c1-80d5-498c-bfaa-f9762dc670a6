/**
 * 数据验证工具函数
 */

/**
 * 验证手机号
 */
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证物品标题
 */
export const validateItemTitle = (title: string): string | null => {
  if (!title || title.trim().length === 0) {
    return '物品标题不能为空'
  }
  if (title.length > 100) {
    return '物品标题不能超过100个字符'
  }
  if (title.length < 2) {
    return '物品标题至少需要2个字符'
  }
  return null
}

/**
 * 验证物品描述
 */
export const validateItemDescription = (description: string): string | null => {
  if (!description || description.trim().length === 0) {
    return '物品描述不能为空'
  }
  if (description.length > 1000) {
    return '物品描述不能超过1000个字符'
  }
  if (description.length < 10) {
    return '物品描述至少需要10个字符'
  }
  return null
}

/**
 * 验证价格
 */
export const validatePrice = (price: number): string | null => {
  if (price < 0) {
    return '价格不能为负数'
  }
  if (price > 999999) {
    return '价格不能超过999999元'
  }
  return null
}

/**
 * 验证用户名
 */
export const validateUsername = (name: string): string | null => {
  if (!name || name.trim().length === 0) {
    return '用户名不能为空'
  }
  if (name.length > 20) {
    return '用户名不能超过20个字符'
  }
  if (name.length < 2) {
    return '用户名至少需要2个字符'
  }
  // 检查是否包含特殊字符
  const specialChars = /[<>\"'&]/
  if (specialChars.test(name)) {
    return '用户名不能包含特殊字符'
  }
  return null
}

/**
 * 验证密码强度
 */
export const validatePassword = (password: string): string | null => {
  if (!password) {
    return '密码不能为空'
  }
  if (password.length < 6) {
    return '密码至少需要6个字符'
  }
  if (password.length > 20) {
    return '密码不能超过20个字符'
  }
  return null
}

/**
 * 验证图片URL
 */
export const isValidImageUrl = (url: string): boolean => {
  const imageRegex = /\.(jpg|jpeg|png|gif|webp)$/i
  return imageRegex.test(url)
}

/**
 * 验证文件大小
 */
export const validateFileSize = (size: number, maxSize: number = 2 * 1024 * 1024): string | null => {
  if (size > maxSize) {
    return `文件大小不能超过${Math.round(maxSize / 1024 / 1024)}MB`
  }
  return null
}

/**
 * 验证经纬度
 */
export const isValidCoordinate = (lat: number, lng: number): boolean => {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180
}

/**
 * 验证消息内容
 */
export const validateMessageContent = (content: string, type: string): string | null => {
  if (type === 'text') {
    if (!content || content.trim().length === 0) {
      return '消息内容不能为空'
    }
    if (content.length > 500) {
      return '消息内容不能超过500个字符'
    }
  }
  return null
}

/**
 * 验证评分
 */
export const validateRating = (rating: number): string | null => {
  if (rating < 1 || rating > 5) {
    return '评分必须在1-5之间'
  }
  if (!Number.isInteger(rating)) {
    return '评分必须是整数'
  }
  return null
}

/**
 * 通用非空验证
 */
export const isNotEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return false
  if (typeof value === 'string') return value.trim().length > 0
  if (Array.isArray(value)) return value.length > 0
  if (typeof value === 'object') return Object.keys(value).length > 0
  return true
}

/**
 * 验证数组长度
 */
export const validateArrayLength = (
  arr: any[], 
  minLength: number = 0, 
  maxLength: number = Infinity,
  fieldName: string = '数组'
): string | null => {
  if (arr.length < minLength) {
    return `${fieldName}至少需要${minLength}个元素`
  }
  if (arr.length > maxLength) {
    return `${fieldName}最多只能有${maxLength}个元素`
  }
  return null
}