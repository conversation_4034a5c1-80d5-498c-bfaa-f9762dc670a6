/**
 * 格式化工具函数
 */

/**
 * 格式化价格
 */
export const formatPrice = (price: number): string => {
  if (price === 0) return '免费'
  return `¥${price.toFixed(2)}`
}

/**
 * 格式化距离
 */
export const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${Math.round(distance)}m`
  }
  return `${(distance / 1000).toFixed(1)}km`
}

/**
 * 格式化时间
 */
export const formatTime = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`
  
  return date.toLocaleDateString('zh-CN')
}

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 格式化手机号
 */
export const formatPhone = (phone: string): string => {
  if (!phone) return ''
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
}

/**
 * 格式化用户名
 */
export const formatUsername = (name: string, maxLength: number = 10): string => {
  if (!name) return '匿名用户'
  if (name.length <= maxLength) return name
  return `${name.substring(0, maxLength)}...`
}

/**
 * 格式化数字
 */
export const formatNumber = (num: number): string => {
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  if (num < 100000000) return `${(num / 10000).toFixed(1)}万`
  return `${(num / 100000000).toFixed(1)}亿`
}

/**
 * 格式化百分比
 */
export const formatPercentage = (value: number, total: number): string => {
  if (total === 0) return '0%'
  return `${((value / total) * 100).toFixed(1)}%`
}

/**
 * 格式化物品状态
 */
export const formatItemStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    available: '可交换',
    exchanging: '交换中',
    exchanged: '已交换',
    removed: '已下架'
  }
  return statusMap[status] || status
}

/**
 * 格式化物品状况
 */
export const formatItemCondition = (condition: string): string => {
  const conditionMap: Record<string, string> = {
    new: '全新',
    like_new: '几乎全新',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return conditionMap[condition] || condition
}