"use strict";
/**
 * Prisma数据库客户端
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
exports.testDatabaseConnection = testDatabaseConnection;
exports.disconnectDatabase = disconnectDatabase;
const client_1 = require("@prisma/client");
const logger_1 = require("../utils/logger");
// 创建全局Prisma客户端实例
const globalForPrisma = globalThis;
exports.prisma = (_a = globalForPrisma.prisma) !== null && _a !== void 0 ? _a : new client_1.PrismaClient({
    log: ['query', 'info', 'warn', 'error'],
});
if (process.env.NODE_ENV !== 'production') {
    globalForPrisma.prisma = exports.prisma;
}
// 数据库连接测试
function testDatabaseConnection() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            yield exports.prisma.$connect();
            logger_1.logger.info('数据库连接成功');
            return true;
        }
        catch (error) {
            logger_1.logger.error('数据库连接失败:', error);
            return false;
        }
    });
}
// 优雅关闭数据库连接
function disconnectDatabase() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            yield exports.prisma.$disconnect();
            logger_1.logger.info('数据库连接已关闭');
        }
        catch (error) {
            logger_1.logger.error('关闭数据库连接时出错:', error);
        }
    });
}
// 进程退出时自动关闭连接
process.on('beforeExit', () => __awaiter(void 0, void 0, void 0, function* () {
    yield disconnectDatabase();
}));
process.on('SIGINT', () => __awaiter(void 0, void 0, void 0, function* () {
    yield disconnectDatabase();
    process.exit(0);
}));
process.on('SIGTERM', () => __awaiter(void 0, void 0, void 0, function* () {
    yield disconnectDatabase();
    process.exit(0);
}));
//# sourceMappingURL=prisma.js.map