"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.submitFeedbackSchema = exports.updateProfileSchema = void 0;
const joi_1 = __importDefault(require("joi"));
/**
 * 用户验证器
 */
exports.updateProfileSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(50).messages({
        'string.min': '用户名长度不能少于2个字符',
        'string.max': '用户名长度不能超过50个字符'
    }),
    avatar: joi_1.default.string().allow(''),
    bio: joi_1.default.string().max(200).allow('').messages({
        'string.max': '个人简介不能超过200个字符'
    }),
    preferences: joi_1.default.object({
        notifications: joi_1.default.object({
            exchangeRequest: joi_1.default.boolean(),
            chatMessage: joi_1.default.boolean(),
            systemNotice: joi_1.default.boolean(),
            emailNotification: joi_1.default.boolean()
        }),
        privacy: joi_1.default.object({
            showPhone: joi_1.default.boolean(),
            showLocation: joi_1.default.boolean(),
            allowSearch: joi_1.default.boolean()
        }),
        location: joi_1.default.object({
            autoLocation: joi_1.default.boolean(),
            defaultCommunityId: joi_1.default.string().allow('')
        })
    }).allow(null).allow('')
});
exports.submitFeedbackSchema = joi_1.default.object({
    type: joi_1.default.string().valid('suggestion', 'bug', 'complaint', 'other').required().messages({
        'any.required': '反馈类型是必填项',
        'string.valid': '反馈类型只能是suggestion、bug、complaint或other'
    }),
    content: joi_1.default.string().min(10).max(1000).required().messages({
        'string.min': '反馈内容不能少于10个字符',
        'string.max': '反馈内容不能超过1000个字符',
        'any.required': '反馈内容是必填项'
    }),
    contactInfo: joi_1.default.string().max(100).allow('').messages({
        'string.max': '联系方式不能超过100个字符'
    }),
    screenshots: joi_1.default.array().items(joi_1.default.string()).max(5).allow(null).allow('').messages({
        'array.max': '最多只能上传5张截图'
    })
});
//# sourceMappingURL=userValidators.js.map