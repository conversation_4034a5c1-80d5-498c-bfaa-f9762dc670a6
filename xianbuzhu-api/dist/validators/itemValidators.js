"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchItemSchema = exports.updateItemSchema = exports.createItemSchema = void 0;
const joi_1 = __importDefault(require("joi"));
/**
 * 物品验证器
 */
exports.createItemSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).required().messages({
        'string.empty': '物品名称不能为空',
        'string.min': '物品名称长度不能少于2个字符',
        'string.max': '物品名称长度不能超过100个字符',
        'any.required': '物品名称是必填项'
    }),
    description: joi_1.default.string().max(1000).required().messages({
        'string.empty': '物品描述不能为空',
        'string.max': '物品描述不能超过1000个字符',
        'any.required': '物品描述是必填项'
    }),
    categoryId: joi_1.default.string().required().messages({
        'any.required': '物品分类是必填项'
    }),
    condition: joi_1.default.string().valid('new', 'like_new', 'good', 'fair', 'poor').required().messages({
        'any.required': '物品状况是必填项',
        'string.valid': '物品状况只能是new、like_new、good、fair或poor'
    }),
    tags: joi_1.default.array().items(joi_1.default.string().max(20)).max(10).allow(null).allow(''),
    images: joi_1.default.array().items(joi_1.default.string()).min(1).max(9).required().messages({
        'array.min': '至少需要上传1张图片',
        'array.max': '最多只能上传9张图片',
        'any.required': '图片是必填项'
    }),
    location: joi_1.default.object({
        latitude: joi_1.default.number().required(),
        longitude: joi_1.default.number().required(),
        address: joi_1.default.string().required()
    }).required().messages({
        'any.required': '物品位置是必填项'
    }),
    isExchangeable: joi_1.default.boolean().required().messages({
        'any.required': '是否可交换是必填项'
    }),
    isDonation: joi_1.default.boolean().required().messages({
        'any.required': '是否捐赠是必填项'
    }),
    expectedValue: joi_1.default.number().min(0).allow(null).allow('').messages({
        'number.min': '期望价值不能为负数'
    }),
    communityId: joi_1.default.string().allow('')
});
exports.updateItemSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).messages({
        'string.min': '物品名称长度不能少于2个字符',
        'string.max': '物品名称长度不能超过100个字符'
    }),
    description: joi_1.default.string().max(1000).messages({
        'string.max': '物品描述不能超过1000个字符'
    }),
    categoryId: joi_1.default.string(),
    condition: joi_1.default.string().valid('new', 'like_new', 'good', 'fair', 'poor').messages({
        'string.valid': '物品状况只能是new、like_new、good、fair或poor'
    }),
    tags: joi_1.default.array().items(joi_1.default.string().max(20)).max(10).allow(null).allow(''),
    images: joi_1.default.array().items(joi_1.default.string()).max(9).messages({
        'array.max': '最多只能上传9张图片'
    }),
    location: joi_1.default.object({
        latitude: joi_1.default.number(),
        longitude: joi_1.default.number(),
        address: joi_1.default.string()
    }),
    isExchangeable: joi_1.default.boolean(),
    isDonation: joi_1.default.boolean(),
    expectedValue: joi_1.default.number().min(0).allow(null).allow('').messages({
        'number.min': '期望价值不能为负数'
    }),
    communityId: joi_1.default.string().allow(''),
    status: joi_1.default.string().valid('active', 'inactive', 'exchanged', 'donated').messages({
        'string.valid': '物品状态只能是active、inactive、exchanged或donated'
    })
});
exports.searchItemSchema = joi_1.default.object({
    keyword: joi_1.default.string().allow(''),
    categoryId: joi_1.default.string().allow(''),
    condition: joi_1.default.string().valid('new', 'like_new', 'good', 'fair', 'poor').allow('').messages({
        'string.valid': '物品状况只能是new、like_new、good、fair或poor'
    }),
    isExchangeable: joi_1.default.boolean().allow(null),
    isDonation: joi_1.default.boolean().allow(null),
    minPrice: joi_1.default.number().min(0).allow(null).allow('').messages({
        'number.min': '最低价格不能为负数'
    }),
    maxPrice: joi_1.default.number().min(0).allow(null).allow('').messages({
        'number.min': '最高价格不能为负数'
    }),
    sortBy: joi_1.default.string().valid('createdAt', 'distance', 'price').default('createdAt').messages({
        'string.valid': '排序字段只能是createdAt、distance或price'
    }),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc').messages({
        'string.valid': '排序顺序只能是asc或desc'
    }),
    page: joi_1.default.number().integer().min(1).default(1).messages({
        'number.integer': '页码必须是整数',
        'number.min': '页码最小为1'
    }),
    pageSize: joi_1.default.number().integer().min(1).max(100).default(10).messages({
        'number.integer': '每页数量必须是整数',
        'number.min': '每页数量最小为1',
        'number.max': '每页数量最大为100'
    }),
    location: joi_1.default.object({
        latitude: joi_1.default.number(),
        longitude: joi_1.default.number()
    }).allow(null)
});
//# sourceMappingURL=itemValidators.js.map