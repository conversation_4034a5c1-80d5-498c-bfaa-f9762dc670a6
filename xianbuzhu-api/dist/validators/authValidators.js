"use strict";
/**
 * 认证相关验证器
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyIdentitySchema = exports.bindPhoneSchema = exports.loginSchema = exports.adminLoginSchema = void 0;
const joi_1 = __importDefault(require("joi"));
// 管理后台登录验证 schema
exports.adminLoginSchema = joi_1.default.object({
    username: joi_1.default.string().required().min(3).max(20).description('用户名'),
    password: joi_1.default.string().required().min(6).max(20).description('密码')
});
// 微信登录验证 schema
exports.loginSchema = joi_1.default.object({
    code: joi_1.default.string().required().description('微信登录code')
});
// 绑定手机号验证 schema
exports.bindPhoneSchema = joi_1.default.object({
    encryptedData: joi_1.default.string().required().description('加密的手机号数据'),
    iv: joi_1.default.string().required().description('加密向量')
});
// 身份验证验证 schema
exports.verifyIdentitySchema = joi_1.default.object({
    name: joi_1.default.string().required().description('姓名'),
    idCard: joi_1.default.string().required().pattern(/^[1-9]\d{5}(19|20)\d{2}(0\d|1[0-2])([0-2]\d|3[0-1])\d{3}(\d|X)$/).description('身份证号码')
});
//# sourceMappingURL=authValidators.js.map