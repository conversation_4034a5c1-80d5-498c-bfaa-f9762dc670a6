"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.joinCommunitySchema = exports.updateCommunitySchema = exports.createCommunitySchema = void 0;
const joi_1 = __importDefault(require("joi"));
/**
 * 社区验证器
 */
exports.createCommunitySchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(50).required().messages({
        'string.empty': '社区名称不能为空',
        'string.min': '社区名称长度不能少于2个字符',
        'string.max': '社区名称长度不能超过50个字符',
        'any.required': '社区名称是必填项'
    }),
    description: joi_1.default.string().max(500).allow('').messages({
        'string.max': '社区描述不能超过500个字符'
    }),
    avatar: joi_1.default.string().allow(''),
    coverImage: joi_1.default.string().allow(''),
    location: joi_1.default.object({
        latitude: joi_1.default.number().required(),
        longitude: joi_1.default.number().required(),
        address: joi_1.default.string().required()
    }).required().messages({
        'any.required': '社区位置信息是必填项'
    }),
    rules: joi_1.default.string().max(1000).allow('').messages({
        'string.max': '社区规则不能超过1000个字符'
    })
});
exports.updateCommunitySchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(50).messages({
        'string.min': '社区名称长度不能少于2个字符',
        'string.max': '社区名称长度不能超过50个字符'
    }),
    description: joi_1.default.string().max(500).allow('').messages({
        'string.max': '社区描述不能超过500个字符'
    }),
    avatar: joi_1.default.string().allow(''),
    coverImage: joi_1.default.string().allow(''),
    location: joi_1.default.object({
        latitude: joi_1.default.number().required(),
        longitude: joi_1.default.number().required(),
        address: joi_1.default.string().required()
    }).messages({
        'any.required': '社区位置信息是必填项'
    }),
    rules: joi_1.default.string().max(1000).allow('').messages({
        'string.max': '社区规则不能超过1000个字符'
    }),
    status: joi_1.default.string().valid('active', 'inactive').messages({
        'string.valid': '社区状态只能是active或inactive'
    })
});
exports.joinCommunitySchema = joi_1.default.object({
    communityId: joi_1.default.string().required().messages({
        'any.required': '社区ID是必填项'
    }),
    reason: joi_1.default.string().max(200).allow('').messages({
        'string.max': '加入理由不能超过200个字符'
    })
});
//# sourceMappingURL=communityValidators.js.map