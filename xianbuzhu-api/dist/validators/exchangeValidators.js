"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.disputeExchangeSchema = exports.rateExchangeSchema = exports.completeExchangeSchema = exports.confirmExchangeSchema = exports.respondExchangeSchema = exports.requestExchangeSchema = void 0;
const joi_1 = __importDefault(require("joi"));
/**
 * 交换验证器
 */
exports.requestExchangeSchema = joi_1.default.object({
    itemId: joi_1.default.string().required().messages({
        'any.required': '物品ID是必填项'
    }),
    targetItemId: joi_1.default.string().allow('').messages({
        'string.empty': '目标物品ID可以为空'
    }),
    message: joi_1.default.string().max(500).allow('').messages({
        'string.max': '留言不能超过500个字符'
    }),
    meetLocation: joi_1.default.object({
        latitude: joi_1.default.number().required(),
        longitude: joi_1.default.number().required(),
        address: joi_1.default.string().required()
    }).required().messages({
        'any.required': '见面地点是必填项'
    }),
    meetTime: joi_1.default.date().required().messages({
        'any.required': '见面时间是必填项'
    })
});
exports.respondExchangeSchema = joi_1.default.object({
    exchangeId: joi_1.default.string().required().messages({
        'any.required': '交换ID是必填项'
    }),
    status: joi_1.default.string().valid('accepted', 'rejected').required().messages({
        'any.required': '必须指定接受或拒绝',
        'string.valid': '状态只能是accepted或rejected'
    }),
    message: joi_1.default.string().max(500).allow('').messages({
        'string.max': '留言不能超过500个字符'
    }),
    meetLocation: joi_1.default.object({
        latitude: joi_1.default.number(),
        longitude: joi_1.default.number(),
        address: joi_1.default.string()
    }).when('status', {
        is: 'accepted',
        then: joi_1.default.required().messages({
            'any.required': '接受交换时必须指定见面地点'
        })
    }),
    meetTime: joi_1.default.date().when('status', {
        is: 'accepted',
        then: joi_1.default.required().messages({
            'any.required': '接受交换时必须指定见面时间'
        })
    })
});
exports.confirmExchangeSchema = joi_1.default.object({
    exchangeId: joi_1.default.string().required().messages({
        'any.required': '交换ID是必填项'
    })
});
exports.completeExchangeSchema = joi_1.default.object({
    exchangeId: joi_1.default.string().required().messages({
        'any.required': '交换ID是必填项'
    })
});
exports.rateExchangeSchema = joi_1.default.object({
    exchangeId: joi_1.default.string().required().messages({
        'any.required': '交换ID是必填项'
    }),
    rating: joi_1.default.number().integer().min(1).max(5).required().messages({
        'number.base': '评分必须是数字',
        'number.integer': '评分必须是整数',
        'number.min': '评分最低为1分',
        'number.max': '评分最高为5分',
        'any.required': '评分是必填项'
    }),
    comment: joi_1.default.string().max(500).allow('').messages({
        'string.max': '评论不能超过500个字符'
    })
});
exports.disputeExchangeSchema = joi_1.default.object({
    exchangeId: joi_1.default.string().required().messages({
        'any.required': '交换ID是必填项'
    }),
    reason: joi_1.default.string().required().max(500).messages({
        'any.required': '争议理由是必填项',
        'string.max': '争议理由不能超过500个字符'
    }),
    evidence: joi_1.default.array().items(joi_1.default.string()).allow(null).allow('')
});
//# sourceMappingURL=exchangeValidators.js.map