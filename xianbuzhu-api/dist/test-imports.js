"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 测试模块导入
const logger_1 = require("./utils/logger");
const errorHandler_1 = require("./middleware/errorHandler");
// 测试代码
console.log('测试导入成功');
logger_1.logger.info('测试logger');
const error = new errorHandler_1.AppError('测试错误', 400);
const authResult = { token: 'test', user: { id: '1', name: 'test', avatar: 'test', trustLevel: { level: 1, score: 100, exchangeCount: 0, successRate: 100, averageRating: 5 }, points: 0, isVerified: false, isActive: true, createdAt: new Date(), updatedAt: new Date() }, isNewUser: true };
console.log('authResult:', authResult);
//# sourceMappingURL=test-imports.js.map