"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = require("dotenv");
// 加载测试环境变量
(0, dotenv_1.config)({ path: '.env.test' });
// 导入Jest全局变量类型
require("@jest/globals");
// 设置测试环境
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/test_db';
process.env.REDIS_URL = process.env.TEST_REDIS_URL || 'redis://localhost:6379/1';
// 全局测试超时
jest.setTimeout(10000);
// 测试前后的清理工作
beforeAll(() => __awaiter(void 0, void 0, void 0, function* () {
    // 这里可以添加全局测试前的设置
    console.log('🧪 开始测试...');
}));
afterAll(() => __awaiter(void 0, void 0, void 0, function* () {
    // 这里可以添加全局测试后的清理
    console.log('✅ 测试完成');
}));
// 每个测试前后的清理
beforeEach(() => {
    // 清理模拟函数
    jest.clearAllMocks();
});
afterEach(() => {
    // 测试后清理
});
// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});
//# sourceMappingURL=setup.js.map