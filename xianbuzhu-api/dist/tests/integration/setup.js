"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = require("dotenv");
const prisma_1 = require("../../src/lib/prisma");
// 加载测试环境变量
(0, dotenv_1.config)({ path: '.env.test' });
// 设置测试环境
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/test_db';
process.env.REDIS_URL = process.env.TEST_REDIS_URL || 'redis://localhost:6379/1';
// 全局测试超时
jest.setTimeout(30000);
// 测试前的全局设置
beforeAll(() => __awaiter(void 0, void 0, void 0, function* () {
    console.log('🧪 开始集成测试...');
    // 确保数据库连接正常
    try {
        yield prisma_1.prisma.$connect();
        console.log('✅ 数据库连接成功');
    }
    catch (error) {
        console.error('❌ 数据库连接失败:', error);
        throw error;
    }
}));
// 测试后的全局清理
afterAll(() => __awaiter(void 0, void 0, void 0, function* () {
    console.log('🧹 清理集成测试环境...');
    try {
        // 清理测试数据
        yield cleanupTestData();
        // 断开数据库连接
        yield prisma_1.prisma.$disconnect();
        console.log('✅ 集成测试完成');
    }
    catch (error) {
        console.error('❌ 清理测试环境失败:', error);
        throw error;
    }
}));
// 每个测试前的清理
beforeEach(() => __awaiter(void 0, void 0, void 0, function* () {
    // 清理测试数据，确保每个测试都有干净的环境
    yield cleanupTestData();
}));
// 清理测试数据的函数
function cleanupTestData() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // 按照外键依赖顺序删除数据
            yield prisma_1.prisma.exchange.deleteMany({});
            yield prisma_1.prisma.itemImage.deleteMany({});
            yield prisma_1.prisma.item.deleteMany({});
            yield prisma_1.prisma.user.deleteMany({});
            yield prisma_1.prisma.community.deleteMany({});
            yield prisma_1.prisma.category.deleteMany({});
        }
        catch (error) {
            console.warn('清理测试数据时出现警告:', error);
        }
    });
}
// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});
//# sourceMappingURL=setup.js.map