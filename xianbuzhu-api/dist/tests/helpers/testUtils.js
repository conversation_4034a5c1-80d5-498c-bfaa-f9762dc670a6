"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMockRequest = createMockRequest;
exports.createMockResponse = createMockResponse;
exports.createMockNext = createMockNext;
exports.generateTestToken = generateTestToken;
exports.createTestUser = createTestUser;
exports.createTestItem = createTestItem;
exports.createTestCommunity = createTestCommunity;
exports.createTestCategory = createTestCategory;
exports.createTestExchange = createTestExchange;
exports.waitFor = waitFor;
exports.mockTransaction = mockTransaction;
exports.createPaginatedResult = createPaginatedResult;
exports.expectSuccessResponse = expectSuccessResponse;
exports.expectErrorResponse = expectErrorResponse;
exports.cleanupTestData = cleanupTestData;
exports.setupTestEnvironment = setupTestEnvironment;
exports.teardownTestEnvironment = teardownTestEnvironment;
const jsonwebtoken_1 = require("jsonwebtoken");
const config_1 = require("../../src/config");
/**
 * 创建模拟的Express Request对象
 */
function createMockRequest(overrides = {}) {
    return Object.assign({ body: {}, params: {}, query: {}, headers: {}, user: undefined }, overrides);
}
/**
 * 创建模拟的Express Response对象
 */
function createMockResponse() {
    const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis(),
        cookie: jest.fn().mockReturnThis(),
        clearCookie: jest.fn().mockReturnThis(),
        redirect: jest.fn().mockReturnThis(),
    };
    return res;
}
/**
 * 创建模拟的Next函数
 */
function createMockNext() {
    return jest.fn();
}
/**
 * 生成测试用的JWT Token
 */
function generateTestToken(payload) {
    return (0, jsonwebtoken_1.sign)(payload, config_1.config.jwt.secret, { expiresIn: '1h' });
}
/**
 * 创建测试用户数据
 */
function createTestUser(overrides = {}) {
    return Object.assign({ id: 'test-user-id', name: '测试用户', phone: '13800138000', avatar: 'https://example.com/avatar.jpg', openid: 'test-openid', unionid: 'test-unionid', sessionKey: 'test-session-key', communityId: 'test-community-id', isVerified: false, isActive: true, createdAt: new Date('2024-01-01T00:00:00Z'), updatedAt: new Date('2024-01-01T00:00:00Z') }, overrides);
}
/**
 * 创建测试物品数据
 */
function createTestItem(overrides = {}) {
    return Object.assign({ id: 'test-item-id', title: '测试物品', description: '这是一个测试物品', categoryId: 'test-category-id', condition: 'good', estimatedValue: 100, publisherId: 'test-user-id', communityId: 'test-community-id', status: 'available', isActive: true, latitude: 39.9042, longitude: 116.4074, address: '北京市朝阳区', images: [], desiredItems: ['书籍', '电子产品'], tags: ['测试', '物品'], createdAt: new Date('2024-01-01T00:00:00Z'), updatedAt: new Date('2024-01-01T00:00:00Z') }, overrides);
}
/**
 * 创建测试社区数据
 */
function createTestCommunity(overrides = {}) {
    return Object.assign({ id: 'test-community-id', name: '测试社区', description: '这是一个测试社区', address: '北京市朝阳区测试小区', latitude: 39.9042, longitude: 116.4074, isActive: true, createdAt: new Date('2024-01-01T00:00:00Z'), updatedAt: new Date('2024-01-01T00:00:00Z') }, overrides);
}
/**
 * 创建测试分类数据
 */
function createTestCategory(overrides = {}) {
    return Object.assign({ id: 'test-category-id', name: '测试分类', description: '这是一个测试分类', icon: 'test-icon', order: 1, isActive: true, createdAt: new Date('2024-01-01T00:00:00Z'), updatedAt: new Date('2024-01-01T00:00:00Z') }, overrides);
}
/**
 * 创建测试交换数据
 */
function createTestExchange(overrides = {}) {
    return Object.assign({ id: 'test-exchange-id', itemId: 'test-item-id', requesterId: 'test-requester-id', offeredItemId: 'test-offered-item-id', status: 'pending', message: '我想要这个物品', createdAt: new Date('2024-01-01T00:00:00Z'), updatedAt: new Date('2024-01-01T00:00:00Z') }, overrides);
}
/**
 * 等待异步操作完成
 */
function waitFor(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * 模拟数据库事务
 */
function mockTransaction(callback) {
    return callback;
}
/**
 * 创建分页查询结果
 */
function createPaginatedResult(items, page = 1, limit = 10, total) {
    const actualTotal = total !== null && total !== void 0 ? total : items.length;
    const totalPages = Math.ceil(actualTotal / limit);
    return {
        items,
        total: actualTotal,
        page,
        limit,
        totalPages,
    };
}
/**
 * 验证API响应格式
 */
function expectSuccessResponse(response, data) {
    expect(response.success).toBe(true);
    if (data !== undefined) {
        expect(response.data).toEqual(data);
    }
}
/**
 * 验证错误响应格式
 */
function expectErrorResponse(response, message, statusCode) {
    expect(response.success).toBe(false);
    if (message) {
        expect(response.message).toBe(message);
    }
    if (statusCode) {
        expect(response.statusCode).toBe(statusCode);
    }
}
/**
 * 清理测试数据
 */
function cleanupTestData() {
    return __awaiter(this, void 0, void 0, function* () {
        // 这里可以添加清理测试数据的逻辑
        // 比如清理测试数据库、重置模拟对象等
    });
}
/**
 * 设置测试环境
 */
function setupTestEnvironment() {
    // 设置测试环境变量
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET = 'test-secret';
    // 模拟时间
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-01-01T00:00:00Z'));
}
/**
 * 清理测试环境
 */
function teardownTestEnvironment() {
    // 恢复真实时间
    jest.useRealTimers();
    // 清理环境变量
    delete process.env.JWT_SECRET;
}
//# sourceMappingURL=testUtils.js.map