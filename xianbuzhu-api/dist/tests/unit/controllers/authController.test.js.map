{"version": 3, "file": "authController.test.js", "sourceRoot": "", "sources": ["../../../../tests/unit/controllers/authController.test.ts"], "names": [], "mappings": ";;;;;;;;;;;AACA,wFAAqF;AACrF,uEAAoE;AACpE,mEAAgE;AAChE,+CAAoC;AAGpC,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;AACjD,IAAI,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;AAC/C,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC1B,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,EAAE,CAAC,CAAC;IACtC,MAAM,EAAE;QACN,GAAG,EAAE;YACH,MAAM,EAAE,aAAa;YACrB,SAAS,EAAE,IAAI;SAChB;KACF;CACF,CAAC,CAAC,CAAC;AAEJ,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,IAAI,WAA6B,CAAC;IAClC,IAAI,YAA+B,CAAC;IACpC,IAAI,QAAsB,CAAC;IAE3B,MAAM,iBAAiB,GAAG,6BAAkD,CAAC;IAC7E,MAAM,eAAe,GAAG,yBAA8C,CAAC;IACvE,MAAM,QAAQ,GAAG,mBAAwC,CAAC;IAE1D,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG;YACZ,IAAI,EAAE,EAAE;YACR,OAAO,EAAE,EAAE;SACZ,CAAC;QACF,YAAY,GAAG;YACb,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAClC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAChC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;SACnC,CAAC;QACF,QAAQ,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;QACrB,EAAE,CAAC,WAAW,EAAE,GAAS,EAAE;YACzB,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM;oBAChB,SAAS,EAAE,gCAAgC;iBAC5C;aACF,CAAC;YAEF,MAAM,cAAc,GAAG;gBACrB,MAAM,EAAE,aAAa;gBACrB,WAAW,EAAE,kBAAkB;gBAC/B,OAAO,EAAE,cAAc;aACxB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,gCAAgC;gBACxC,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,KAAK;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC;YAC7B,iBAAiB,CAAC,cAAc,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACnE,eAAe,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACrD,eAAe,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAClD,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAE3C,MAAM,+BAAc,CAAC,KAAK,CACxB,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC3E,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YACzE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClD,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,gCAAgC;gBACxC,MAAM,EAAE,aAAa;gBACrB,OAAO,EAAE,cAAc;gBACvB,UAAU,EAAE,kBAAkB;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC;gBACE,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,MAAM;aACb,EACD,aAAa,EACb,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;YACF,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,gBAAgB;oBACvB,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC,CAAC;QACL,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,aAAa,EAAE,GAAS,EAAE;YAC3B,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,WAAW;aAClB,CAAC;YAEF,MAAM,cAAc,GAAG;gBACrB,MAAM,EAAE,aAAa;gBACrB,WAAW,EAAE,kBAAkB;aAChC,CAAC;YAEF,MAAM,YAAY,GAAG;gBACnB,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,gCAAgC;gBACxC,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC;YAC7B,iBAAiB,CAAC,cAAc,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACnE,eAAe,CAAC,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC7D,eAAe,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YACvD,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAE3C,MAAM,+BAAc,CAAC,KAAK,CACxB,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC3E,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YACzE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,QAAQ,EAAE;gBAC5D,UAAU,EAAE,kBAAkB;gBAC9B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC9B,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC;gBACE,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,MAAM;aACb,EACD,aAAa,EACb,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;YACF,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,gBAAgB;oBACvB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;QACL,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,eAAe,EAAE,GAAS,EAAE;YAC7B,WAAW,CAAC,IAAI,GAAG,EAAE,CAAC;YAEtB,MAAM,+BAAc,CAAC,KAAK,CACxB,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,cAAc;aACxB,CAAC,CACH,CAAC;QACJ,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,iBAAiB,EAAE,GAAS,EAAE;YAC/B,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,cAAc;aACrB,CAAC;YAEF,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC;YAC7B,iBAAiB,CAAC,cAAc,CAAC,iBAAiB,CAChD,IAAI,KAAK,CAAC,cAAc,CAAC,CAC1B,CAAC;YAEF,MAAM,+BAAc,CAAC,KAAK,CACxB,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,QAAQ;aAClB,CAAC,CACH,CAAC;QACJ,CAAC,CAAA,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,aAAa,EAAE,GAAS,EAAE;YAC3B,MAAM,IAAI,GAAG;gBACX,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,IAAI;aACf,CAAC;YAEF,WAAW,CAAC,IAAI,GAAG;gBACjB,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACjD,QAAQ,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAE1C,MAAM,+BAAc,CAAC,YAAY,CAC/B,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAChE,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC;gBACE,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,MAAM;aACb,EACD,aAAa,EACb,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;YACF,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,KAAK,EAAE,eAAe;oBACtB,IAAI,EAAE,IAAI;iBACX;aACF,CAAC,CAAC;QACL,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,cAAc,EAAE,GAAS,EAAE;YAC5B,WAAW,CAAC,IAAI,GAAG;gBACjB,EAAE,EAAE,cAAc;gBAClB,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,+BAAc,CAAC,YAAY,CAC/B,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,OAAO;aACjB,CAAC,CACH,CAAC;QACJ,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,cAAc,EAAE,GAAS,EAAE;YAC5B,MAAM,YAAY,GAAG;gBACnB,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,KAAK;aAChB,CAAC;YAEF,WAAW,CAAC,IAAI,GAAG;gBACjB,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAEzD,MAAM,+BAAc,CAAC,YAAY,CAC/B,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,QAAQ;aAClB,CAAC,CACH,CAAC;QACJ,CAAC,CAAA,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,QAAQ,EAAE,GAAS,EAAE;YACtB,MAAM,+BAAc,CAAC,MAAM,CACzB,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,MAAM;aAChB,CAAC,CAAC;QACL,CAAC,CAAA,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,UAAU,EAAE,GAAS,EAAE;YACxB,MAAM,IAAI,GAAG;gBACX,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,gCAAgC;gBACxC,KAAK,EAAE,aAAa;gBACpB,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE;oBACT,EAAE,EAAE,aAAa;oBACjB,IAAI,EAAE,MAAM;iBACb;aACF,CAAC;YAEF,WAAW,CAAC,IAAI,GAAG;gBACjB,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,+BAAc,CAAC,UAAU,CAC7B,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAChE,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,IAAI;aACX,CAAC,CAAC;QACL,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,cAAc,EAAE,GAAS,EAAE;YAC5B,WAAW,CAAC,IAAI,GAAG;gBACjB,EAAE,EAAE,cAAc;gBAClB,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,+BAAc,CAAC,UAAU,CAC7B,WAAsB,EACtB,YAAwB,EACxB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,OAAO;aACjB,CAAC,CACH,CAAC;QACJ,CAAC,CAAA,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}