"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const authController_1 = require("../../../src/controllers/miniprogram/authController");
const wechatService_1 = require("../../../src/services/wechatService");
const userService_1 = require("../../../src/services/userService");
const jsonwebtoken_1 = require("jsonwebtoken");
// Mock dependencies
jest.mock('../../../src/services/wechatService');
jest.mock('../../../src/services/userService');
jest.mock('jsonwebtoken');
jest.mock('../../../src/config', () => ({
    config: {
        jwt: {
            secret: 'test-secret',
            expiresIn: '7d',
        },
    },
}));
describe('AuthController', () => {
    let mockRequest;
    let mockResponse;
    let mockNext;
    const mockWechatService = wechatService_1.wechatService;
    const mockUserService = userService_1.userService;
    const mockSign = jsonwebtoken_1.sign;
    beforeEach(() => {
        mockRequest = {
            body: {},
            headers: {},
        };
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis(),
            cookie: jest.fn().mockReturnThis(),
        };
        mockNext = jest.fn();
        jest.clearAllMocks();
    });
    describe('login', () => {
        it('应该成功登录新用户', () => __awaiter(void 0, void 0, void 0, function* () {
            const loginData = {
                code: 'test-code',
                userInfo: {
                    nickName: '测试用户',
                    avatarUrl: 'https://example.com/avatar.jpg',
                },
            };
            const wechatUserInfo = {
                openid: 'test-openid',
                session_key: 'test-session-key',
                unionid: 'test-unionid',
            };
            const newUser = {
                id: 'user-1',
                name: '测试用户',
                avatar: 'https://example.com/avatar.jpg',
                openid: 'test-openid',
                isActive: true,
                isVerified: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockRequest.body = loginData;
            mockWechatService.getSessionInfo.mockResolvedValue(wechatUserInfo);
            mockUserService.findByOpenid.mockResolvedValue(null);
            mockUserService.create.mockResolvedValue(newUser);
            mockSign.mockReturnValue('test-jwt-token');
            yield authController_1.authController.login(mockRequest, mockResponse, mockNext);
            expect(mockWechatService.getSessionInfo).toHaveBeenCalledWith('test-code');
            expect(mockUserService.findByOpenid).toHaveBeenCalledWith('test-openid');
            expect(mockUserService.create).toHaveBeenCalledWith({
                name: '测试用户',
                avatar: 'https://example.com/avatar.jpg',
                openid: 'test-openid',
                unionid: 'test-unionid',
                sessionKey: 'test-session-key',
            });
            expect(mockSign).toHaveBeenCalledWith({
                id: 'user-1',
                openid: 'test-openid',
                role: 'user',
            }, 'test-secret', { expiresIn: '7d' });
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: true,
                message: '登录成功',
                data: {
                    user: newUser,
                    token: 'test-jwt-token',
                    isNewUser: true,
                },
            });
        }));
        it('应该成功登录已存在用户', () => __awaiter(void 0, void 0, void 0, function* () {
            const loginData = {
                code: 'test-code',
            };
            const wechatUserInfo = {
                openid: 'test-openid',
                session_key: 'test-session-key',
            };
            const existingUser = {
                id: 'user-1',
                name: '已存在用户',
                avatar: 'https://example.com/avatar.jpg',
                openid: 'test-openid',
                isActive: true,
                isVerified: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockRequest.body = loginData;
            mockWechatService.getSessionInfo.mockResolvedValue(wechatUserInfo);
            mockUserService.findByOpenid.mockResolvedValue(existingUser);
            mockUserService.update.mockResolvedValue(existingUser);
            mockSign.mockReturnValue('test-jwt-token');
            yield authController_1.authController.login(mockRequest, mockResponse, mockNext);
            expect(mockWechatService.getSessionInfo).toHaveBeenCalledWith('test-code');
            expect(mockUserService.findByOpenid).toHaveBeenCalledWith('test-openid');
            expect(mockUserService.update).toHaveBeenCalledWith('user-1', {
                sessionKey: 'test-session-key',
                lastLoginAt: expect.any(Date),
            });
            expect(mockSign).toHaveBeenCalledWith({
                id: 'user-1',
                openid: 'test-openid',
                role: 'user',
            }, 'test-secret', { expiresIn: '7d' });
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: true,
                message: '登录成功',
                data: {
                    user: existingUser,
                    token: 'test-jwt-token',
                    isNewUser: false,
                },
            });
        }));
        it('缺少code时应该返回错误', () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.body = {};
            yield authController_1.authController.login(mockRequest, mockResponse, mockNext);
            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                statusCode: 400,
                message: '缺少必要参数: code',
            }));
        }));
        it('微信服务调用失败时应该返回错误', () => __awaiter(void 0, void 0, void 0, function* () {
            const loginData = {
                code: 'invalid-code',
            };
            mockRequest.body = loginData;
            mockWechatService.getSessionInfo.mockRejectedValue(new Error('Invalid code'));
            yield authController_1.authController.login(mockRequest, mockResponse, mockNext);
            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                statusCode: 401,
                message: '微信登录失败',
            }));
        }));
    });
    describe('refreshToken', () => {
        it('应该成功刷新token', () => __awaiter(void 0, void 0, void 0, function* () {
            const user = {
                id: 'user-1',
                name: '测试用户',
                openid: 'test-openid',
                isActive: true,
            };
            mockRequest.user = {
                id: 'user-1',
                openid: 'test-openid',
                role: 'user',
            };
            mockUserService.findById.mockResolvedValue(user);
            mockSign.mockReturnValue('new-jwt-token');
            yield authController_1.authController.refreshToken(mockRequest, mockResponse, mockNext);
            expect(mockUserService.findById).toHaveBeenCalledWith('user-1');
            expect(mockSign).toHaveBeenCalledWith({
                id: 'user-1',
                openid: 'test-openid',
                role: 'user',
            }, 'test-secret', { expiresIn: '7d' });
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: true,
                message: 'Token刷新成功',
                data: {
                    token: 'new-jwt-token',
                    user: user,
                },
            });
        }));
        it('用户不存在时应该返回错误', () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.user = {
                id: 'non-existent',
                openid: 'test-openid',
                role: 'user',
            };
            mockUserService.findById.mockResolvedValue(null);
            yield authController_1.authController.refreshToken(mockRequest, mockResponse, mockNext);
            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                statusCode: 404,
                message: '用户不存在',
            }));
        }));
        it('用户被禁用时应该返回错误', () => __awaiter(void 0, void 0, void 0, function* () {
            const inactiveUser = {
                id: 'user-1',
                name: '测试用户',
                openid: 'test-openid',
                isActive: false,
            };
            mockRequest.user = {
                id: 'user-1',
                openid: 'test-openid',
                role: 'user',
            };
            mockUserService.findById.mockResolvedValue(inactiveUser);
            yield authController_1.authController.refreshToken(mockRequest, mockResponse, mockNext);
            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                statusCode: 403,
                message: '用户已被禁用',
            }));
        }));
    });
    describe('logout', () => {
        it('应该成功登出', () => __awaiter(void 0, void 0, void 0, function* () {
            yield authController_1.authController.logout(mockRequest, mockResponse, mockNext);
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: true,
                message: '登出成功',
            });
        }));
    });
    describe('getProfile', () => {
        it('应该返回用户信息', () => __awaiter(void 0, void 0, void 0, function* () {
            const user = {
                id: 'user-1',
                name: '测试用户',
                avatar: 'https://example.com/avatar.jpg',
                phone: '13800138000',
                isVerified: true,
                community: {
                    id: 'community-1',
                    name: '测试社区',
                },
            };
            mockRequest.user = {
                id: 'user-1',
                openid: 'test-openid',
                role: 'user',
            };
            mockUserService.findById.mockResolvedValue(user);
            yield authController_1.authController.getProfile(mockRequest, mockResponse, mockNext);
            expect(mockUserService.findById).toHaveBeenCalledWith('user-1');
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(mockResponse.json).toHaveBeenCalledWith({
                success: true,
                data: user,
            });
        }));
        it('用户不存在时应该返回错误', () => __awaiter(void 0, void 0, void 0, function* () {
            mockRequest.user = {
                id: 'non-existent',
                openid: 'test-openid',
                role: 'user',
            };
            mockUserService.findById.mockResolvedValue(null);
            yield authController_1.authController.getProfile(mockRequest, mockResponse, mockNext);
            expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
                statusCode: 404,
                message: '用户不存在',
            }));
        }));
    });
});
//# sourceMappingURL=authController.test.js.map