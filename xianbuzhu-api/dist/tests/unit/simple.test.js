"use strict";
/**
 * 简单测试，验证Jest配置是否正常工作
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
describe('Simple Test', () => {
    it('应该能够运行基本测试', () => {
        expect(1 + 1).toBe(2);
    });
    it('应该能够测试异步函数', () => __awaiter(void 0, void 0, void 0, function* () {
        const result = yield Promise.resolve('test');
        expect(result).toBe('test');
    }));
    it('应该能够使用Jest模拟', () => {
        const mockFn = jest.fn();
        mockFn('test');
        expect(mockFn).toHaveBeenCalledWith('test');
    });
});
//# sourceMappingURL=simple.test.js.map