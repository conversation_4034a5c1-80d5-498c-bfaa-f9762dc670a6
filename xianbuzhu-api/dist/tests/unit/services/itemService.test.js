"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const itemService_1 = require("../../../src/services/itemService");
const prisma_1 = require("../../../src/lib/prisma");
const logger_1 = require("../../../src/utils/logger");
// Mock dependencies
jest.mock('../../../src/lib/prisma', () => ({
    prisma: {
        item: {
            findUnique: jest.fn(),
            findMany: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            count: jest.fn(),
        },
        itemImage: {
            createMany: jest.fn(),
            deleteMany: jest.fn(),
        },
        $transaction: jest.fn(),
    },
}));
jest.mock('../../../src/utils/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    },
}));
describe('ItemService', () => {
    let itemService;
    const mockPrisma = prisma_1.prisma;
    beforeEach(() => {
        itemService = new itemService_1.ItemService();
        jest.clearAllMocks();
    });
    describe('findById', () => {
        it('应该根据ID查找物品', () => __awaiter(void 0, void 0, void 0, function* () {
            const mockItem = {
                id: 'item-1',
                title: '测试物品',
                description: '测试描述',
                categoryId: 'category-1',
                condition: 'good',
                estimatedValue: 100,
                publisherId: 'user-1',
                communityId: 'community-1',
                status: 'available',
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date(),
                images: [],
                publisher: {
                    id: 'user-1',
                    name: '发布者',
                },
                category: {
                    id: 'category-1',
                    name: '测试分类',
                },
            };
            mockPrisma.item.findUnique.mockResolvedValue(mockItem);
            const result = yield itemService.findById('item-1');
            expect(mockPrisma.item.findUnique).toHaveBeenCalledWith({
                where: { id: 'item-1' },
                include: {
                    images: true,
                    publisher: {
                        select: {
                            id: true,
                            name: true,
                            avatar: true,
                            isVerified: true,
                        },
                    },
                    category: true,
                    community: true,
                    exchanges: {
                        where: { status: { in: ['pending', 'accepted'] } },
                        include: {
                            requester: {
                                select: {
                                    id: true,
                                    name: true,
                                    avatar: true,
                                },
                            },
                        },
                    },
                },
            });
            expect(result).toEqual(mockItem);
        }));
        it('物品不存在时应该返回null', () => __awaiter(void 0, void 0, void 0, function* () {
            mockPrisma.item.findUnique.mockResolvedValue(null);
            const result = yield itemService.findById('non-existent');
            expect(result).toBeNull();
        }));
        it('数据库错误时应该抛出异常', () => __awaiter(void 0, void 0, void 0, function* () {
            const error = new Error('Database error');
            mockPrisma.item.findUnique.mockRejectedValue(error);
            yield expect(itemService.findById('item-1')).rejects.toThrow('Database error');
            expect(logger_1.logger.error).toHaveBeenCalledWith('查找物品失败:', error);
        }));
    });
    describe('create', () => {
        it('应该创建新物品', () => __awaiter(void 0, void 0, void 0, function* () {
            const createData = {
                title: '新物品',
                description: '新物品描述',
                categoryId: 'category-1',
                condition: 'good',
                estimatedValue: 200,
                publisherId: 'user-1',
                communityId: 'community-1',
                images: ['image1.jpg', 'image2.jpg'],
                tags: ['tag1', 'tag2'],
            };
            const mockCreatedItem = {
                id: 'item-2',
                title: createData.title,
                description: createData.description,
                categoryId: createData.categoryId,
                condition: createData.condition,
                estimatedValue: createData.estimatedValue,
                publisherId: createData.publisherId,
                communityId: createData.communityId,
                status: 'available',
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.$transaction.mockImplementation((callback) => __awaiter(void 0, void 0, void 0, function* () {
                return yield callback(mockPrisma);
            }));
            mockPrisma.item.create.mockResolvedValue(mockCreatedItem);
            mockPrisma.itemImage.createMany.mockResolvedValue({ count: 2 });
            const result = yield itemService.create(createData);
            expect(mockPrisma.$transaction).toHaveBeenCalled();
            expect(mockPrisma.item.create).toHaveBeenCalledWith({
                data: {
                    title: createData.title,
                    description: createData.description,
                    categoryId: createData.categoryId,
                    condition: createData.condition,
                    estimatedValue: createData.estimatedValue,
                    publisherId: createData.publisherId,
                    communityId: createData.communityId,
                    latitude: createData.latitude,
                    longitude: createData.longitude,
                    address: createData.address,
                    desiredItems: createData.desiredItems,
                    tags: createData.tags,
                    status: 'available',
                    isActive: true,
                },
                include: {
                    images: true,
                    publisher: {
                        select: {
                            id: true,
                            name: true,
                            avatar: true,
                            isVerified: true,
                        },
                    },
                    category: true,
                    community: true,
                },
            });
            expect(mockPrisma.itemImage.createMany).toHaveBeenCalledWith({
                data: [
                    { itemId: 'item-2', url: 'image1.jpg', order: 0 },
                    { itemId: 'item-2', url: 'image2.jpg', order: 1 },
                ],
            });
            expect(result).toEqual(mockCreatedItem);
            expect(logger_1.logger.info).toHaveBeenCalledWith('创建物品成功:', mockCreatedItem.id);
        }));
        it('创建物品失败时应该抛出异常', () => __awaiter(void 0, void 0, void 0, function* () {
            const createData = {
                title: '新物品',
                description: '新物品描述',
                categoryId: 'category-1',
                condition: 'good',
                estimatedValue: 200,
                publisherId: 'user-1',
                communityId: 'community-1',
            };
            const error = new Error('Create failed');
            mockPrisma.$transaction.mockRejectedValue(error);
            yield expect(itemService.create(createData)).rejects.toThrow('Create failed');
            expect(logger_1.logger.error).toHaveBeenCalledWith('创建物品失败:', error);
        }));
    });
    describe('update', () => {
        it('应该更新物品信息', () => __awaiter(void 0, void 0, void 0, function* () {
            const updateData = {
                title: '更新后的物品',
                estimatedValue: 300,
                images: ['new-image.jpg'],
            };
            const mockUpdatedItem = {
                id: 'item-1',
                title: '更新后的物品',
                description: '测试描述',
                categoryId: 'category-1',
                condition: 'good',
                estimatedValue: 300,
                publisherId: 'user-1',
                communityId: 'community-1',
                status: 'available',
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.$transaction.mockImplementation((callback) => __awaiter(void 0, void 0, void 0, function* () {
                return yield callback(mockPrisma);
            }));
            mockPrisma.item.update.mockResolvedValue(mockUpdatedItem);
            mockPrisma.itemImage.deleteMany.mockResolvedValue({ count: 1 });
            mockPrisma.itemImage.createMany.mockResolvedValue({ count: 1 });
            const result = yield itemService.update('item-1', updateData);
            expect(mockPrisma.$transaction).toHaveBeenCalled();
            expect(mockPrisma.itemImage.deleteMany).toHaveBeenCalledWith({
                where: { itemId: 'item-1' },
            });
            expect(mockPrisma.itemImage.createMany).toHaveBeenCalledWith({
                data: [{ itemId: 'item-1', url: 'new-image.jpg', order: 0 }],
            });
            expect(mockPrisma.item.update).toHaveBeenCalledWith({
                where: { id: 'item-1' },
                data: {
                    title: updateData.title,
                    estimatedValue: updateData.estimatedValue,
                },
                include: {
                    images: true,
                    publisher: {
                        select: {
                            id: true,
                            name: true,
                            avatar: true,
                            isVerified: true,
                        },
                    },
                    category: true,
                    community: true,
                },
            });
            expect(result).toEqual(mockUpdatedItem);
            expect(logger_1.logger.info).toHaveBeenCalledWith('更新物品成功:', 'item-1');
        }));
    });
    describe('list', () => {
        it('应该返回物品列表', () => __awaiter(void 0, void 0, void 0, function* () {
            const mockItems = [
                {
                    id: 'item-1',
                    title: '物品1',
                    description: '描述1',
                    categoryId: 'category-1',
                    condition: 'good',
                    estimatedValue: 100,
                    publisherId: 'user-1',
                    communityId: 'community-1',
                    status: 'available',
                    isActive: true,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            ];
            mockPrisma.item.findMany.mockResolvedValue(mockItems);
            mockPrisma.item.count.mockResolvedValue(1);
            const result = yield itemService.list({ page: 1, limit: 10 });
            expect(mockPrisma.item.findMany).toHaveBeenCalledWith({
                skip: 0,
                take: 10,
                where: { isActive: true },
                include: {
                    images: { take: 1 },
                    publisher: {
                        select: {
                            id: true,
                            name: true,
                            avatar: true,
                            isVerified: true,
                        },
                    },
                    category: true,
                    community: true,
                },
                orderBy: { createdAt: 'desc' },
            });
            expect(result).toEqual({
                items: mockItems,
                total: 1,
                page: 1,
                limit: 10,
                totalPages: 1,
            });
        }));
        it('应该支持搜索和过滤', () => __awaiter(void 0, void 0, void 0, function* () {
            const mockItems = [];
            mockPrisma.item.findMany.mockResolvedValue(mockItems);
            mockPrisma.item.count.mockResolvedValue(0);
            yield itemService.list({
                page: 1,
                limit: 10,
                search: '测试',
                categoryId: 'category-1',
                condition: 'good',
                communityId: 'community-1',
                publisherId: 'user-1',
                status: 'available',
            });
            expect(mockPrisma.item.findMany).toHaveBeenCalledWith({
                skip: 0,
                take: 10,
                where: {
                    AND: [
                        { isActive: true },
                        {
                            OR: [
                                { title: { contains: '测试', mode: 'insensitive' } },
                                { description: { contains: '测试', mode: 'insensitive' } },
                                { tags: { has: '测试' } },
                            ],
                        },
                        { categoryId: 'category-1' },
                        { condition: 'good' },
                        { communityId: 'community-1' },
                        { publisherId: 'user-1' },
                        { status: 'available' },
                    ],
                },
                include: {
                    images: { take: 1 },
                    publisher: {
                        select: {
                            id: true,
                            name: true,
                            avatar: true,
                            isVerified: true,
                        },
                    },
                    category: true,
                    community: true,
                },
                orderBy: { createdAt: 'desc' },
            });
        }));
    });
    describe('delete', () => {
        it('应该软删除物品', () => __awaiter(void 0, void 0, void 0, function* () {
            const mockUpdatedItem = {
                id: 'item-1',
                isActive: false,
                updatedAt: new Date(),
            };
            mockPrisma.item.update.mockResolvedValue(mockUpdatedItem);
            const result = yield itemService.delete('item-1');
            expect(mockPrisma.item.update).toHaveBeenCalledWith({
                where: { id: 'item-1' },
                data: { isActive: false },
            });
            expect(result).toBe(true);
            expect(logger_1.logger.info).toHaveBeenCalledWith('删除物品成功:', 'item-1');
        }));
        it('删除不存在的物品时应该返回false', () => __awaiter(void 0, void 0, void 0, function* () {
            const error = new Error('Record not found');
            mockPrisma.item.update.mockRejectedValue(error);
            const result = yield itemService.delete('non-existent');
            expect(result).toBe(false);
            expect(logger_1.logger.error).toHaveBeenCalledWith('删除物品失败:', error);
        }));
    });
});
//# sourceMappingURL=itemService.test.js.map