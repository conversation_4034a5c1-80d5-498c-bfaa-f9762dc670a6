"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const userService_1 = require("../../../src/services/userService");
const prisma_1 = require("../../../src/lib/prisma");
const logger_1 = require("../../../src/utils/logger");
// Mock dependencies
jest.mock('../../../src/lib/prisma', () => ({
    prisma: {
        user: {
            findUnique: jest.fn(),
            findMany: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            count: jest.fn(),
        },
    },
}));
jest.mock('../../../src/utils/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    },
}));
describe('UserService', () => {
    let userService;
    const mockPrisma = prisma_1.prisma;
    beforeEach(() => {
        userService = new userService_1.UserService();
        jest.clearAllMocks();
    });
    describe('findById', () => {
        it('应该根据ID查找用户', () => __awaiter(void 0, void 0, void 0, function* () {
            const mockUser = {
                id: 'user-1',
                name: '测试用户',
                phone: '13800138000',
                avatar: 'avatar.jpg',
                communityId: 'community-1',
                isVerified: true,
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            const result = yield userService.findById('user-1');
            expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
                where: { id: 'user-1' },
                include: {
                    community: true,
                    items: {
                        where: { isActive: true },
                        orderBy: { createdAt: 'desc' },
                        take: 5,
                    },
                },
            });
            expect(result).toEqual(mockUser);
        }));
        it('用户不存在时应该返回null', () => __awaiter(void 0, void 0, void 0, function* () {
            mockPrisma.user.findUnique.mockResolvedValue(null);
            const result = yield userService.findById('non-existent');
            expect(result).toBeNull();
        }));
        it('数据库错误时应该抛出异常', () => __awaiter(void 0, void 0, void 0, function* () {
            const error = new Error('Database error');
            mockPrisma.user.findUnique.mockRejectedValue(error);
            yield expect(userService.findById('user-1')).rejects.toThrow('Database error');
            expect(logger_1.logger.error).toHaveBeenCalledWith('查找用户失败:', error);
        }));
    });
    describe('findByPhone', () => {
        it('应该根据手机号查找用户', () => __awaiter(void 0, void 0, void 0, function* () {
            const mockUser = {
                id: 'user-1',
                name: '测试用户',
                phone: '13800138000',
                avatar: 'avatar.jpg',
                communityId: 'community-1',
                isVerified: true,
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            const result = yield userService.findByPhone('13800138000');
            expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
                where: { phone: '13800138000' },
            });
            expect(result).toEqual(mockUser);
        }));
    });
    describe('create', () => {
        it('应该创建新用户', () => __awaiter(void 0, void 0, void 0, function* () {
            const createData = {
                name: '新用户',
                phone: '13800138001',
                avatar: 'new-avatar.jpg',
                communityId: 'community-1',
            };
            const mockCreatedUser = Object.assign(Object.assign({ id: 'user-2' }, createData), { isVerified: false, isActive: true, createdAt: new Date(), updatedAt: new Date() });
            mockPrisma.user.create.mockResolvedValue(mockCreatedUser);
            const result = yield userService.create(createData);
            expect(mockPrisma.user.create).toHaveBeenCalledWith({
                data: createData,
                include: {
                    community: true,
                },
            });
            expect(result).toEqual(mockCreatedUser);
            expect(logger_1.logger.info).toHaveBeenCalledWith('创建用户成功:', mockCreatedUser.id);
        }));
        it('创建用户失败时应该抛出异常', () => __awaiter(void 0, void 0, void 0, function* () {
            const createData = {
                name: '新用户',
                phone: '13800138001',
            };
            const error = new Error('Create failed');
            mockPrisma.user.create.mockRejectedValue(error);
            yield expect(userService.create(createData)).rejects.toThrow('Create failed');
            expect(logger_1.logger.error).toHaveBeenCalledWith('创建用户失败:', error);
        }));
    });
    describe('update', () => {
        it('应该更新用户信息', () => __awaiter(void 0, void 0, void 0, function* () {
            const updateData = {
                name: '更新后的用户名',
                isVerified: true,
            };
            const mockUpdatedUser = {
                id: 'user-1',
                name: '更新后的用户名',
                phone: '13800138000',
                avatar: 'avatar.jpg',
                communityId: 'community-1',
                isVerified: true,
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            mockPrisma.user.update.mockResolvedValue(mockUpdatedUser);
            const result = yield userService.update('user-1', updateData);
            expect(mockPrisma.user.update).toHaveBeenCalledWith({
                where: { id: 'user-1' },
                data: updateData,
                include: {
                    community: true,
                },
            });
            expect(result).toEqual(mockUpdatedUser);
            expect(logger_1.logger.info).toHaveBeenCalledWith('更新用户成功:', 'user-1');
        }));
    });
    describe('delete', () => {
        it('应该删除用户', () => __awaiter(void 0, void 0, void 0, function* () {
            mockPrisma.user.delete.mockResolvedValue({
                id: 'user-1',
                name: '测试用户',
                phone: '13800138000',
                avatar: 'avatar.jpg',
                communityId: 'community-1',
                isVerified: true,
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            const result = yield userService.delete('user-1');
            expect(mockPrisma.user.delete).toHaveBeenCalledWith({
                where: { id: 'user-1' },
            });
            expect(result).toBe(true);
            expect(logger_1.logger.info).toHaveBeenCalledWith('删除用户成功:', 'user-1');
        }));
        it('删除不存在的用户时应该返回false', () => __awaiter(void 0, void 0, void 0, function* () {
            const error = new Error('Record not found');
            mockPrisma.user.delete.mockRejectedValue(error);
            const result = yield userService.delete('non-existent');
            expect(result).toBe(false);
            expect(logger_1.logger.error).toHaveBeenCalledWith('删除用户失败:', error);
        }));
    });
    describe('list', () => {
        it('应该返回用户列表', () => __awaiter(void 0, void 0, void 0, function* () {
            const mockUsers = [
                {
                    id: 'user-1',
                    name: '用户1',
                    phone: '13800138000',
                    avatar: 'avatar1.jpg',
                    communityId: 'community-1',
                    isVerified: true,
                    isActive: true,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                {
                    id: 'user-2',
                    name: '用户2',
                    phone: '13800138001',
                    avatar: 'avatar2.jpg',
                    communityId: 'community-1',
                    isVerified: false,
                    isActive: true,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            ];
            mockPrisma.user.findMany.mockResolvedValue(mockUsers);
            mockPrisma.user.count.mockResolvedValue(2);
            const result = yield userService.list({ page: 1, limit: 10 });
            expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
                skip: 0,
                take: 10,
                include: {
                    community: true,
                },
                orderBy: { createdAt: 'desc' },
            });
            expect(mockPrisma.user.count).toHaveBeenCalled();
            expect(result).toEqual({
                users: mockUsers,
                total: 2,
                page: 1,
                limit: 10,
                totalPages: 1,
            });
        }));
        it('应该支持搜索过滤', () => __awaiter(void 0, void 0, void 0, function* () {
            const mockUsers = [];
            mockPrisma.user.findMany.mockResolvedValue(mockUsers);
            mockPrisma.user.count.mockResolvedValue(0);
            yield userService.list({
                page: 1,
                limit: 10,
                search: '测试',
                communityId: 'community-1',
                isVerified: true
            });
            expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
                skip: 0,
                take: 10,
                where: {
                    AND: [
                        {
                            OR: [
                                { name: { contains: '测试', mode: 'insensitive' } },
                                { phone: { contains: '测试', mode: 'insensitive' } },
                            ],
                        },
                        { communityId: 'community-1' },
                        { isVerified: true },
                    ],
                },
                include: {
                    community: true,
                },
                orderBy: { createdAt: 'desc' },
            });
        }));
    });
});
//# sourceMappingURL=userService.test.js.map