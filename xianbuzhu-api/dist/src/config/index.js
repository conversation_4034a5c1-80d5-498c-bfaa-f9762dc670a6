"use strict";
/**
 * API网关配置文件
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
// 加载环境变量
const result = dotenv_1.default.config();
if (result.error) {
    console.warn('未找到.env文件，尝试加载上级目录的.env.local文件');
    const fallbackResult = dotenv_1.default.config({ path: '../.env.local' });
    if (fallbackResult.error) {
        console.error('加载环境变量失败:', fallbackResult.error);
    }
    else {
        console.log('成功加载上级目录的环境变量');
    }
}
else {
    console.log('成功加载环境变量');
}
exports.config = {
    // 服务配置
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: parseInt(process.env.PORT || '3000'),
    API_VERSION: process.env.API_VERSION || 'v1',
    ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS,
    // 数据库配置
    DATABASE_URL: process.env.DATABASE_URL || '',
    // Redis配置
    REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379',
    // JWT配置
    JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key',
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
    // 微信配置
    WECHAT_APP_ID: process.env.WECHAT_APP_ID || '',
    WECHAT_APP_SECRET: process.env.WECHAT_APP_SECRET || '',
    // 文件存储配置
    COS_SECRET_ID: process.env.COS_SECRET_ID || '',
    COS_SECRET_KEY: process.env.COS_SECRET_KEY || '',
    COS_BUCKET: process.env.COS_BUCKET || '',
    COS_REGION: process.env.COS_REGION || '',
    // 地图服务配置
    TENCENT_MAP_KEY: process.env.TENCENT_MAP_KEY || '',
    // 内容审核配置
    CONTENT_AUDIT_SECRET_ID: process.env.CONTENT_AUDIT_SECRET_ID || '',
    CONTENT_AUDIT_SECRET_KEY: process.env.CONTENT_AUDIT_SECRET_KEY || '',
    // 聊天服务配置
    CHAT_SERVICE_URL: process.env.CHAT_SERVICE_URL || 'http://localhost:3001',
    CHAT_SERVICE_SECRET: process.env.CHAT_SERVICE_SECRET || '',
    // 日志配置
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    LOG_FILE: process.env.LOG_FILE || 'logs/app.log',
    // 监控配置
    METRICS_ENABLED: process.env.METRICS_ENABLED === 'true',
    METRICS_PORT: parseInt(process.env.METRICS_PORT || '9090'),
};
//# sourceMappingURL=index.js.map