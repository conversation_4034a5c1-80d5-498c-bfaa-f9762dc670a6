"use strict";
/**
 * 健康检查路由
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const prisma_1 = require("../lib/prisma");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
// 基础健康检查
router.get('/', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 检查数据库连接
        yield prisma_1.prisma.$queryRaw `SELECT 1`;
        res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.env.npm_package_version || '1.0.0',
        });
    }
    catch (error) {
        logger_1.logger.error('健康检查失败', { error });
        res.status(503).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}));
// 详细健康检查
router.get('/detailed', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const checks = {
        database: false,
        memory: false,
        uptime: false,
    };
    try {
        // 数据库检查
        yield prisma_1.prisma.$queryRaw `SELECT 1`;
        checks.database = true;
    }
    catch (error) {
        logger_1.logger.error('数据库健康检查失败', { error });
    }
    // 内存检查
    const memoryUsage = process.memoryUsage();
    checks.memory = memoryUsage.heapUsed < memoryUsage.heapTotal * 0.9;
    // 运行时间检查
    checks.uptime = process.uptime() > 0;
    const isHealthy = Object.values(checks).every(check => check);
    res.status(isHealthy ? 200 : 503).json({
        status: isHealthy ? 'healthy' : 'unhealthy',
        checks,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: memoryUsage,
    });
}));
exports.default = router;
//# sourceMappingURL=health.js.map