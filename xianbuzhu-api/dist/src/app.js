"use strict";
/**
 * API网关服务主入口文件
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const errorHandler_1 = require("./middleware/errorHandler");
const requestLogger_1 = require("./middleware/requestLogger");
const metrics_1 = require("./middleware/metrics");
// 路由导入
const miniprogram_1 = __importDefault(require("./routes/miniprogram"));
const health_1 = __importDefault(require("./routes/health"));
const admin_1 = __importDefault(require("./routes/admin"));
const app = (0, express_1.default)();
// 基础中间件
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: ((_a = config_1.config.ALLOWED_ORIGINS) === null || _a === void 0 ? void 0 : _a.split(',')) || ['http://localhost:3001'],
    credentials: true
}));
// 请求限制
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 1000, // 限制每个IP 15分钟内最多1000个请求
    message: '请求过于频繁，请稍后再试',
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
// 请求解析
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// 请求日志
app.use(requestLogger_1.requestLogger);
// 性能监控
app.use(metrics_1.metricsMiddleware);
// 路由注册
app.use('/api/miniprogram', miniprogram_1.default);
app.use('/api/admin', admin_1.default);
app.use('/api/health', health_1.default);
// 根路径
app.get('/', (req, res) => {
    res.json({
        name: 'Xianbuzhu API Gateway',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});
// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在',
        path: req.originalUrl
    });
});
// 错误处理
app.use(errorHandler_1.errorHandler);
// 启动服务器
const PORT = config_1.config.PORT || 3000;
const server = app.listen(PORT, () => {
    logger_1.logger.info(`🚀 API网关服务启动成功`);
    logger_1.logger.info(`📡 服务地址: http://localhost:${PORT}`);
    logger_1.logger.info(`🌍 环境: ${config_1.config.NODE_ENV}`);
});
// 优雅关闭
process.on('SIGTERM', () => {
    logger_1.logger.info('收到SIGTERM信号，开始优雅关闭...');
    server.close(() => {
        logger_1.logger.info('服务器已关闭');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger_1.logger.info('收到SIGINT信号，开始优雅关闭...');
    server.close(() => {
        logger_1.logger.info('服务器已关闭');
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=app.js.map