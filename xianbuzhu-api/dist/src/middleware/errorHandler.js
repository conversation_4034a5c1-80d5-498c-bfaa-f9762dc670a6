"use strict";
/**
 * 错误处理中间件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.AppError = void 0;
const logger_1 = require("../utils/logger");
class AppError extends Error {
    constructor(message, statusCode = 500) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
const errorHandler = (error, req, res, next) => {
    let statusCode = 500;
    let message = '服务器内部错误';
    let isOperational = false;
    // 处理自定义错误
    if (error instanceof AppError) {
        statusCode = error.statusCode;
        message = error.message;
        isOperational = error.isOperational;
    }
    // 处理Prisma错误
    else if (error.name === 'PrismaClientKnownRequestError') {
        statusCode = 400;
        message = '数据库操作失败';
    }
    // 处理JWT错误
    else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = '无效的访问令牌';
    }
    // 处理JWT过期错误
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        message = '访问令牌已过期';
    }
    // 处理验证错误
    else if (error.name === 'ValidationError') {
        statusCode = 400;
        message = '请求参数验证失败';
    }
    // 记录错误日志
    if (statusCode >= 500 || !isOperational) {
        logger_1.logger.error('服务器错误', {
            error: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
        });
    }
    else {
        logger_1.logger.warn('客户端错误', {
            error: error.message,
            url: req.url,
            method: req.method,
            ip: req.ip,
        });
    }
    // 返回错误响应
    res.status(statusCode).json(Object.assign({ success: false, message }, (process.env.NODE_ENV === 'development' && {
        stack: error.stack,
        details: error
    })));
};
exports.errorHandler = errorHandler;
//# sourceMappingURL=errorHandler.js.map