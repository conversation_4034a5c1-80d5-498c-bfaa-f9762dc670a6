{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAGH,4CAAwC;AAExC,MAAa,QAAS,SAAQ,KAAK;IAIjC,YAAY,OAAe,EAAE,aAAqB,GAAG;QACnD,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QAEzB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACjD,CAAC;CACF;AAXD,4BAWC;AAEM,MAAM,YAAY,GAAG,CAC1B,KAAuB,EACvB,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,UAAU,GAAG,GAAG,CAAA;IACpB,IAAI,OAAO,GAAG,SAAS,CAAA;IACvB,IAAI,aAAa,GAAG,KAAK,CAAA;IAEzB,UAAU;IACV,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,UAAU,GAAG,KAAK,CAAC,UAAU,CAAA;QAC7B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAA;QACvB,aAAa,GAAG,KAAK,CAAC,aAAa,CAAA;IACrC,CAAC;IAED,aAAa;SACR,IAAI,KAAK,CAAC,IAAI,KAAK,+BAA+B,EAAE,CAAC;QACxD,UAAU,GAAG,GAAG,CAAA;QAChB,OAAO,GAAG,SAAS,CAAA;IACrB,CAAC;IAED,UAAU;SACL,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC5C,UAAU,GAAG,GAAG,CAAA;QAChB,OAAO,GAAG,SAAS,CAAA;IACrB,CAAC;IAED,YAAY;SACP,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC5C,UAAU,GAAG,GAAG,CAAA;QAChB,OAAO,GAAG,SAAS,CAAA;IACrB,CAAC;IAED,SAAS;SACJ,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAC1C,UAAU,GAAG,GAAG,CAAA;QAChB,OAAO,GAAG,UAAU,CAAA;IACtB,CAAC;IAED,SAAS;IACT,IAAI,UAAU,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,eAAM,CAAC,KAAK,CAAC,OAAO,EAAE;YACpB,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC,CAAC,CAAA;IACJ,CAAC;SAAM,CAAC;QACN,eAAM,CAAC,IAAI,CAAC,OAAO,EAAE;YACnB,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,EAAE,EAAE,GAAG,CAAC,EAAE;SACX,CAAC,CAAA;IACJ,CAAC;IAED,SAAS;IACT,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,iBACzB,OAAO,EAAE,KAAK,EACd,OAAO,IACJ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI;QAC5C,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,OAAO,EAAE,KAAK;KACf,CAAC,EACF,CAAA;AACJ,CAAC,CAAA;AArEY,QAAA,YAAY,gBAqExB"}