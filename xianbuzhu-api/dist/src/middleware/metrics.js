"use strict";
/**
 * 性能监控中间件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.metricsMiddleware = void 0;
const metricsMiddleware = (req, res, next) => {
    const startTime = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        // 添加响应头
        res.set('X-Response-Time', `${duration}ms`);
    });
    next();
};
exports.metricsMiddleware = metricsMiddleware;
//# sourceMappingURL=metrics.js.map