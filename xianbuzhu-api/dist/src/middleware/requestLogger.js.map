{"version": 3, "file": "requestLogger.js", "sourceRoot": "", "sources": ["../../../src/middleware/requestLogger.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAGH,4CAAwC;AAEjC,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC/E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IAE5B,SAAS;IACT,eAAM,CAAC,IAAI,CAAC,MAAM,EAAE;QAClB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;KACjC,CAAC,CAAA;IAEF,SAAS;IACT,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QAEvC,eAAM,CAAC,IAAI,CAAC,MAAM,EAAE;YAClB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,QAAQ,EAAE,GAAG,QAAQ,IAAI;YACzB,EAAE,EAAE,GAAG,CAAC,EAAE;SACX,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AAzBY,QAAA,aAAa,iBAyBzB"}