"use strict";
/**
 * 物品服务
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemService = exports.ItemService = void 0;
const prisma_1 = require("../lib/prisma");
const logger_1 = require("../utils/logger");
class ItemService {
    /**
     * 根据ID查找物品
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const item = yield prisma_1.prisma.item.findUnique({
                    where: { id },
                    include: {
                        images: true,
                        publisher: {
                            select: {
                                id: true,
                                name: true,
                                avatar: true,
                                isVerified: true,
                            },
                        },
                        category: true,
                        community: true,
                        exchanges: {
                            where: { status: { in: ['pending', 'accepted'] } },
                            include: {
                                requester: {
                                    select: {
                                        id: true,
                                        name: true,
                                        avatar: true,
                                    },
                                },
                            },
                        },
                    },
                });
                return item;
            }
            catch (error) {
                logger_1.logger.error('查找物品失败:', error);
                throw error;
            }
        });
    }
    /**
     * 创建物品
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                    // 创建物品
                    const item = yield tx.item.create({
                        data: {
                            title: data.title,
                            description: data.description,
                            categoryId: data.categoryId,
                            condition: data.condition,
                            estimatedValue: data.estimatedValue,
                            publisherId: data.publisherId,
                            communityId: data.communityId,
                            latitude: data.latitude,
                            longitude: data.longitude,
                            address: data.address,
                            status: 'available',
                        },
                        include: {
                            images: true,
                            publisher: {
                                select: {
                                    id: true,
                                    name: true,
                                    avatar: true,
                                    isVerified: true,
                                },
                            },
                            category: true,
                            community: true,
                        },
                    });
                    // 批量创建图片
                    if (data.images && data.images.length > 0) {
                        yield tx.itemImage.createMany({
                            data: data.images.map((url, index) => ({
                                itemId: item.id,
                                url,
                                order: index,
                            })),
                        });
                    }
                    return item;
                }));
                logger_1.logger.info('创建物品成功:', result.id);
                return result;
            }
            catch (error) {
                logger_1.logger.error('创建物品失败:', error);
                throw error;
            }
        });
    }
    /**
     * 更新物品
     */
    update(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield prisma_1.prisma.$transaction((tx) => __awaiter(this, void 0, void 0, function* () {
                    // 更新物品
                    const updateData = {};
                    if (data.title)
                        updateData.title = data.title;
                    if (data.description)
                        updateData.description = data.description;
                    if (data.condition)
                        updateData.condition = data.condition;
                    if (data.estimatedValue)
                        updateData.estimatedValue = data.estimatedValue;
                    if (data.latitude)
                        updateData.latitude = data.latitude;
                    if (data.longitude)
                        updateData.longitude = data.longitude;
                    if (data.address)
                        updateData.address = data.address;
                    if (data.status)
                        updateData.status = data.status;
                    const item = yield tx.item.update({
                        where: { id },
                        data: updateData,
                        include: {
                            images: true,
                            publisher: {
                                select: {
                                    id: true,
                                    name: true,
                                    avatar: true,
                                    isVerified: true,
                                },
                            },
                            category: true,
                            community: true,
                        },
                    });
                    // 更新图片
                    if (data.images) {
                        // 删除旧图片
                        yield tx.itemImage.deleteMany({
                            where: { itemId: id },
                        });
                        // 创建新图片
                        if (data.images.length > 0) {
                            yield tx.itemImage.createMany({
                                data: data.images.map((url, index) => ({
                                    itemId: id,
                                    url,
                                    order: index,
                                })),
                            });
                        }
                    }
                    return item;
                }));
                logger_1.logger.info('更新物品成功:', id);
                return result;
            }
            catch (error) {
                logger_1.logger.error('更新物品失败:', error);
                throw error;
            }
        });
    }
    /**
     * 删除物品（软删除）
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 软删除 - 更新状态而不是真正删除
                yield prisma_1.prisma.item.update({
                    where: { id },
                    data: { status: 'deleted' },
                });
                logger_1.logger.info('删除物品成功:', id);
                return true;
            }
            catch (error) {
                logger_1.logger.error('删除物品失败:', error);
                return false;
            }
        });
    }
    /**
     * 获取物品列表
     */
    list() {
        return __awaiter(this, arguments, void 0, function* (params = {}) {
            try {
                const { page = 1, limit = 20, search, categoryId, communityId, publisherId, condition, status = 'available', } = params;
                const skip = (page - 1) * limit;
                const where = {
                    status,
                };
                if (search) {
                    where.OR = [
                        { title: { contains: search, mode: 'insensitive' } },
                        { description: { contains: search, mode: 'insensitive' } },
                    ];
                }
                if (categoryId)
                    where.categoryId = categoryId;
                if (communityId)
                    where.communityId = communityId;
                if (publisherId)
                    where.publisherId = publisherId;
                if (condition)
                    where.condition = condition;
                const [items, total] = yield Promise.all([
                    prisma_1.prisma.item.findMany({
                        where,
                        skip,
                        take: limit,
                        include: {
                            images: { take: 1 },
                            publisher: {
                                select: {
                                    id: true,
                                    name: true,
                                    avatar: true,
                                    isVerified: true,
                                },
                            },
                            category: true,
                            community: true,
                        },
                        orderBy: { createdAt: 'desc' },
                    }),
                    prisma_1.prisma.item.count({ where }),
                ]);
                return {
                    items,
                    total,
                    page,
                    limit,
                    totalPages: Math.ceil(total / limit),
                };
            }
            catch (error) {
                logger_1.logger.error('获取物品列表失败:', error);
                throw error;
            }
        });
    }
}
exports.ItemService = ItemService;
exports.itemService = new ItemService();
//# sourceMappingURL=itemService.js.map