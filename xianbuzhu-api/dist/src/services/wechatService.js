"use strict";
/**
 * 微信服务
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wechatService = exports.WechatService = void 0;
const axios_1 = __importDefault(require("axios"));
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
class WechatService {
    /**
     * 获取微信用户session信息
     */
    getSessionInfo(code) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = 'https://api.weixin.qq.com/sns/jscode2session';
                const params = {
                    appid: config_1.config.WECHAT_APP_ID,
                    secret: config_1.config.WECHAT_APP_SECRET,
                    js_code: code,
                    grant_type: 'authorization_code',
                };
                const response = yield axios_1.default.get(url, { params });
                const result = response.data;
                if (result.errcode) {
                    throw new Error(`微信API错误: ${result.errmsg}`);
                }
                logger_1.logger.info('获取微信session成功', { openid: result.openid });
                return result;
            }
            catch (error) {
                logger_1.logger.error('获取微信session失败:', error);
                throw error;
            }
        });
    }
    /**
     * 获取微信访问令牌
     */
    getAccessToken() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = 'https://api.weixin.qq.com/cgi-bin/token';
                const params = {
                    grant_type: 'client_credential',
                    appid: config_1.config.WECHAT_APP_ID,
                    secret: config_1.config.WECHAT_APP_SECRET,
                };
                const response = yield axios_1.default.get(url, { params });
                const result = response.data;
                if (result.errcode) {
                    throw new Error(`获取访问令牌失败: ${result.errmsg}`);
                }
                return result.access_token;
            }
            catch (error) {
                logger_1.logger.error('获取微信访问令牌失败:', error);
                throw error;
            }
        });
    }
    /**
     * 发送模板消息
     */
    sendTemplateMessage(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const accessToken = yield this.getAccessToken();
                const url = `https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=${accessToken}`;
                const response = yield axios_1.default.post(url, data);
                const result = response.data;
                if (result.errcode !== 0) {
                    throw new Error(`发送模板消息失败: ${result.errmsg}`);
                }
                logger_1.logger.info('发送模板消息成功', { msgid: result.msgid });
                return result;
            }
            catch (error) {
                logger_1.logger.error('发送模板消息失败:', error);
                throw error;
            }
        });
    }
}
exports.WechatService = WechatService;
exports.wechatService = new WechatService();
//# sourceMappingURL=wechatService.js.map