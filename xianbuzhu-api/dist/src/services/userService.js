"use strict";
/**
 * 用户服务
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userService = exports.UserService = void 0;
const prisma_1 = require("../lib/prisma");
const logger_1 = require("../utils/logger");
class UserService {
    /**
     * 根据ID查找用户
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield prisma_1.prisma.user.findUnique({
                    where: { id },
                    include: {
                        community: true,
                    },
                });
                return user;
            }
            catch (error) {
                logger_1.logger.error('查找用户失败:', error);
                throw error;
            }
        });
    }
    /**
     * 根据手机号查找用户
     */
    findByPhone(phone) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield prisma_1.prisma.user.findUnique({
                    where: { phone },
                });
                return user;
            }
            catch (error) {
                logger_1.logger.error('根据手机号查找用户失败:', error);
                throw error;
            }
        });
    }
    /**
     * 根据openid查找用户
     */
    findByOpenid(openid) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 注意：这里需要根据实际的数据库模型调整
                // 如果User模型中没有openid字段，需要通过其他方式查找
                const user = yield prisma_1.prisma.user.findFirst({
                    where: {
                        // 假设通过某个字段存储openid，需要根据实际情况调整
                        id: openid // 临时解决方案
                    },
                });
                return user;
            }
            catch (error) {
                logger_1.logger.error('根据openid查找用户失败:', error);
                throw error;
            }
        });
    }
    /**
     * 创建用户
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield prisma_1.prisma.user.create({
                    data,
                    include: {
                        community: true,
                    },
                });
                logger_1.logger.info('创建用户成功:', user.id);
                return user;
            }
            catch (error) {
                logger_1.logger.error('创建用户失败:', error);
                throw error;
            }
        });
    }
    /**
     * 更新用户
     */
    update(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield prisma_1.prisma.user.update({
                    where: { id },
                    data,
                    include: {
                        community: true,
                    },
                });
                logger_1.logger.info('更新用户成功:', id);
                return user;
            }
            catch (error) {
                logger_1.logger.error('更新用户失败:', error);
                throw error;
            }
        });
    }
    /**
     * 删除用户
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield prisma_1.prisma.user.delete({
                    where: { id },
                });
                logger_1.logger.info('删除用户成功:', id);
                return true;
            }
            catch (error) {
                logger_1.logger.error('删除用户失败:', error);
                return false;
            }
        });
    }
    /**
     * 获取用户列表
     */
    list() {
        return __awaiter(this, arguments, void 0, function* (params = {}) {
            try {
                const { page = 1, limit = 20, search, communityId, isVerified } = params;
                const skip = (page - 1) * limit;
                const where = {};
                if (search) {
                    where.OR = [
                        { name: { contains: search, mode: 'insensitive' } },
                        { phone: { contains: search, mode: 'insensitive' } },
                    ];
                }
                if (communityId) {
                    where.communityId = communityId;
                }
                if (isVerified !== undefined) {
                    where.isVerified = isVerified;
                }
                const [users, total] = yield Promise.all([
                    prisma_1.prisma.user.findMany({
                        where,
                        skip,
                        take: limit,
                        include: {
                            community: true,
                        },
                        orderBy: { createdAt: 'desc' },
                    }),
                    prisma_1.prisma.user.count({ where }),
                ]);
                return {
                    users,
                    total,
                    page,
                    limit,
                    totalPages: Math.ceil(total / limit),
                };
            }
            catch (error) {
                logger_1.logger.error('获取用户列表失败:', error);
                throw error;
            }
        });
    }
}
exports.UserService = UserService;
exports.userService = new UserService();
//# sourceMappingURL=userService.js.map