"use strict";
/**
 * 应用程序配置
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const config = {
    // 应用程序信息
    APP_NAME: process.env.APP_NAME || 'Community Exchange API Gateway',
    VERSION: process.env.VERSION || '1.0.0',
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: process.env.PORT || 3000,
    // 安全配置
    SECRET_KEY: process.env.SECRET_KEY || 'your-secret-key',
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
    ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS || 'http://localhost:3001',
    // 数据库配置
    DATABASE_URL: process.env.DATABASE_URL || '',
    REDIS_URL: process.env.REDIS_URL || '',
    // API服务配置
    CHAT_SERVICE_URL: process.env.CHAT_SERVICE_URL || 'http://localhost:3002',
    FILE_SERVICE_URL: process.env.FILE_SERVICE_URL || 'http://localhost:3003',
    // 微信小程序配置
    WECHAT_APPID: process.env.WECHAT_APPID || '',
    WECHAT_SECRET: process.env.WECHAT_SECRET || '',
    // 日志配置
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    LOG_DIR: process.env.LOG_DIR || './logs',
};
exports.config = config;
// 验证必要的配置
if (!config.SECRET_KEY) {
    throw new Error('SECRET_KEY 环境变量未设置');
}
//# sourceMappingURL=config.js.map