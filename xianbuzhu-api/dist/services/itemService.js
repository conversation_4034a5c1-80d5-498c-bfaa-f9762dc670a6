"use strict";
/**
 * 物品服务
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemService = exports.ItemService = void 0;
const prisma_1 = require("../lib/prisma");
const logger_1 = require("../utils/logger");
class ItemService {
    /**
     * 创建物品
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { images = [], desiredItems = [], tags = [] } = data, itemData = __rest(data, ["images", "desiredItems", "tags"]);
                const item = yield prisma_1.prisma.item.create({
                    data: Object.assign(Object.assign({}, itemData), { status: 'available', viewCount: 0, favoriteCount: 0, images: {
                            create: images.map((url, index) => ({
                                url,
                                isPrimary: index === 0
                            }))
                        }, desiredItems: {
                            create: desiredItems.map(description => ({ description }))
                        }, tags: {
                            create: tags.map(tagName => ({
                                tag: {
                                    connectOrCreate: {
                                        where: { name: tagName },
                                        create: { name: tagName }
                                    }
                                }
                            }))
                        } }),
                    include: {
                        category: true,
                        publisher: {
                            select: {
                                id: true,
                                name: true,
                                avatar: true,
                                trustLevel: true,
                                trustScore: true
                            }
                        },
                        images: true,
                        desiredItems: true,
                        tags: {
                            include: {
                                tag: true
                            }
                        },
                        _count: {
                            select: {
                                favorites: true,
                                exchanges: true
                            }
                        }
                    }
                });
                logger_1.logger.info(`物品创建成功: ${item.id}`);
                return item;
            }
            catch (error) {
                logger_1.logger.error('创建物品失败:', error);
                throw new Error('创建物品失败');
            }
        });
    }
    /**
     * 根据ID获取物品详情
     */
    findById(id, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const item = yield prisma_1.prisma.item.findUnique({
                    where: { id },
                    include: {
                        category: true,
                        publisher: {
                            select: {
                                id: true,
                                name: true,
                                avatar: true,
                                trustLevel: true,
                                trustScore: true
                            }
                        },
                        images: true,
                        desiredItems: true,
                        tags: {
                            include: {
                                tag: true
                            }
                        },
                        _count: {
                            select: {
                                favorites: true,
                                exchanges: true
                            }
                        }
                    }
                });
                if (item && userId && userId !== item.publisherId) {
                    // 增加浏览次数
                    yield this.incrementViewCount(id);
                }
                return item;
            }
            catch (error) {
                logger_1.logger.error('获取物品详情失败:', error);
                throw new Error('获取物品详情失败');
            }
        });
    }
    /**
     * 获取物品列表
     */
    findMany(params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { skip = 0, take = 20, communityId, categoryId, publisherId, status = 'available', search, condition, minValue, maxValue, sortBy = 'createdAt', sortOrder = 'desc' } = params;
                const where = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({ status }, (communityId && { communityId })), (categoryId && { categoryId })), (publisherId && { publisherId })), (condition && { condition })), (minValue !== undefined && { estimatedValue: { gte: minValue } })), (maxValue !== undefined && {
                    estimatedValue: Object.assign({}, (minValue !== undefined ? { gte: minValue, lte: maxValue } : { lte: maxValue }))
                }));
                if (search) {
                    where.OR = [
                        { title: { contains: search, mode: 'insensitive' } },
                        { description: { contains: search, mode: 'insensitive' } }
                    ];
                }
                const [items, total] = yield Promise.all([
                    prisma_1.prisma.item.findMany({
                        where,
                        skip,
                        take,
                        include: {
                            category: true,
                            publisher: {
                                select: {
                                    id: true,
                                    name: true,
                                    avatar: true,
                                    trustLevel: true,
                                    trustScore: true
                                }
                            },
                            images: true,
                            desiredItems: true,
                            tags: {
                                include: {
                                    tag: true
                                }
                            },
                            _count: {
                                select: {
                                    favorites: true,
                                    exchanges: true
                                }
                            }
                        },
                        orderBy: { [sortBy]: sortOrder }
                    }),
                    prisma_1.prisma.item.count({ where })
                ]);
                return { items: items, total };
            }
            catch (error) {
                logger_1.logger.error('获取物品列表失败:', error);
                throw new Error('获取物品列表失败');
            }
        });
    }
    /**
     * 更新物品
     */
    update(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const item = yield prisma_1.prisma.item.update({
                    where: { id },
                    data,
                    include: {
                        category: true,
                        publisher: {
                            select: {
                                id: true,
                                name: true,
                                avatar: true,
                                trustLevel: true,
                                trustScore: true
                            }
                        },
                        images: true,
                        desiredItems: true,
                        tags: {
                            include: {
                                tag: true
                            }
                        },
                        _count: {
                            select: {
                                favorites: true,
                                exchanges: true
                            }
                        }
                    }
                });
                logger_1.logger.info(`物品更新成功: ${id}`);
                return item;
            }
            catch (error) {
                logger_1.logger.error('更新物品失败:', error);
                throw new Error('更新物品失败');
            }
        });
    }
    /**
     * 删除物品（软删除）
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield prisma_1.prisma.item.update({
                    where: { id },
                    data: { status: 'deleted' }
                });
                logger_1.logger.info(`物品删除成功: ${id}`);
            }
            catch (error) {
                logger_1.logger.error('删除物品失败:', error);
                throw new Error('删除物品失败');
            }
        });
    }
    /**
     * 增加浏览次数
     */
    incrementViewCount(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield prisma_1.prisma.item.update({
                    where: { id },
                    data: {
                        viewCount: {
                            increment: 1
                        }
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('增加浏览次数失败:', error);
            }
        });
    }
    /**
     * 切换收藏状态
     */
    toggleFavorite(itemId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const existingFavorite = yield prisma_1.prisma.favorite.findUnique({
                    where: {
                        userId_itemId: {
                            userId,
                            itemId
                        }
                    }
                });
                if (existingFavorite) {
                    // 取消收藏
                    yield prisma_1.prisma.favorite.delete({
                        where: {
                            userId_itemId: {
                                userId,
                                itemId
                            }
                        }
                    });
                    // 减少收藏数
                    yield prisma_1.prisma.item.update({
                        where: { id: itemId },
                        data: {
                            favoriteCount: {
                                decrement: 1
                            }
                        }
                    });
                    return false;
                }
                else {
                    // 添加收藏
                    yield prisma_1.prisma.favorite.create({
                        data: {
                            userId,
                            itemId
                        }
                    });
                    // 增加收藏数
                    yield prisma_1.prisma.item.update({
                        where: { id: itemId },
                        data: {
                            favoriteCount: {
                                increment: 1
                            }
                        }
                    });
                    return true;
                }
            }
            catch (error) {
                logger_1.logger.error('切换收藏状态失败:', error);
                throw new Error('切换收藏状态失败');
            }
        });
    }
}
exports.ItemService = ItemService;
exports.itemService = new ItemService();
//# sourceMappingURL=itemService.js.map