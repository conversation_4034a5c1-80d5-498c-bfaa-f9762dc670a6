"use strict";
/**
 * 用户服务
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userService = exports.UserService = void 0;
const prisma_1 = require("../lib/prisma");
const logger_1 = require("../utils/logger");
class UserService {
    /**
     * 根据ID查找用户
     */
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                return yield prisma_1.prisma.user.findUnique({
                    where: { id },
                    include: {
                        community: true,
                        items: {
                            take: 5,
                            orderBy: { createdAt: 'desc' }
                        }
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('查找用户失败:', error);
                throw new Error('查找用户失败');
            }
        });
    }
    /**
     * 根据手机号查找用户
     */
    findByPhone(phone) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                return yield prisma_1.prisma.user.findUnique({
                    where: { phone }
                });
            }
            catch (error) {
                logger_1.logger.error('根据手机号查找用户失败:', error);
                throw new Error('查找用户失败');
            }
        });
    }
    /**
     * 创建新用户
     */
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                return yield prisma_1.prisma.user.create({
                    data: Object.assign(Object.assign({}, data), { trustLevel: 1, trustScore: 0, exchangeCount: 0, successRate: 0, averageRating: 0, isVerified: false, isActive: true })
                });
            }
            catch (error) {
                logger_1.logger.error('创建用户失败:', error);
                throw new Error('创建用户失败');
            }
        });
    }
    /**
     * 更新用户信息
     */
    update(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                return yield prisma_1.prisma.user.update({
                    where: { id },
                    data
                });
            }
            catch (error) {
                logger_1.logger.error('更新用户失败:', error);
                throw new Error('更新用户失败');
            }
        });
    }
    /**
     * 删除用户（软删除）
     */
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield prisma_1.prisma.user.update({
                    where: { id },
                    data: { isActive: false }
                });
            }
            catch (error) {
                logger_1.logger.error('删除用户失败:', error);
                throw new Error('删除用户失败');
            }
        });
    }
    /**
     * 获取用户列表
     */
    findMany(params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { skip = 0, take = 20, communityId, isActive = true } = params;
                const where = Object.assign({ isActive }, (communityId && { communityId }));
                const [users, total] = yield Promise.all([
                    prisma_1.prisma.user.findMany({
                        where,
                        skip,
                        take,
                        include: {
                            community: true
                        },
                        orderBy: { createdAt: 'desc' }
                    }),
                    prisma_1.prisma.user.count({ where })
                ]);
                return { users, total };
            }
            catch (error) {
                logger_1.logger.error('获取用户列表失败:', error);
                throw new Error('获取用户列表失败');
            }
        });
    }
    /**
     * 更新用户信任分数
     */
    updateTrustScore(id, rating) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield this.findById(id);
                if (!user) {
                    throw new Error('用户不存在');
                }
                // 计算新的平均评分和信任分数
                const newExchangeCount = user.exchangeCount + 1;
                const newAverageRating = (user.averageRating * user.exchangeCount + rating) / newExchangeCount;
                const newTrustScore = Math.min(100, newAverageRating * 20); // 简单的信任分数计算
                return yield prisma_1.prisma.user.update({
                    where: { id },
                    data: {
                        exchangeCount: newExchangeCount,
                        averageRating: newAverageRating,
                        trustScore: newTrustScore,
                        trustLevel: Math.ceil(newTrustScore / 20) // 1-5级
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('更新用户信任分数失败:', error);
                throw new Error('更新用户信任分数失败');
            }
        });
    }
}
exports.UserService = UserService;
exports.userService = new UserService();
//# sourceMappingURL=userService.js.map