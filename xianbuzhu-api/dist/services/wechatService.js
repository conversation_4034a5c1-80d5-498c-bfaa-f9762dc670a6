"use strict";
/**
 * 微信服务
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wechatService = exports.WechatService = void 0;
const axios_1 = __importDefault(require("axios"));
const index_1 = require("../config/index");
const logger_1 = require("../utils/logger");
class WechatService {
    constructor() {
        this.appId = index_1.config.WECHAT_APP_ID || '';
        this.appSecret = index_1.config.WECHAT_APP_SECRET || '';
        if (!this.appId || !this.appSecret) {
            logger_1.logger.warn('微信配置不完整，部分功能可能无法使用');
        }
    }
    /**
     * 微信小程序登录
     * @param code 微信登录凭证
     */
    login(code) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = 'https://api.weixin.qq.com/sns/jscode2session';
                const params = {
                    appid: this.appId,
                    secret: this.appSecret,
                    js_code: code,
                    grant_type: 'authorization_code'
                };
                const response = yield axios_1.default.get(url, { params });
                const result = response.data;
                if (result.errcode) {
                    logger_1.logger.error('微信登录失败:', result.errmsg);
                    throw new Error(`微信登录失败: ${result.errmsg}`);
                }
                logger_1.logger.info('微信登录成功:', { openid: result.openid });
                return result;
            }
            catch (error) {
                logger_1.logger.error('微信登录请求失败:', error);
                throw new Error('微信登录失败');
            }
        });
    }
    /**
     * 获取微信Access Token
     */
    getAccessToken() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const url = 'https://api.weixin.qq.com/cgi-bin/token';
                const params = {
                    grant_type: 'client_credential',
                    appid: this.appId,
                    secret: this.appSecret
                };
                const response = yield axios_1.default.get(url, { params });
                const result = response.data;
                if (result.errcode) {
                    throw new Error(`获取Access Token失败: ${result.errmsg}`);
                }
                return result.access_token;
            }
            catch (error) {
                logger_1.logger.error('获取微信Access Token失败:', error);
                throw new Error('获取微信Access Token失败');
            }
        });
    }
    /**
     * 发送模板消息
     */
    sendTemplateMessage(params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const accessToken = yield this.getAccessToken();
                const url = `https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=${accessToken}`;
                const response = yield axios_1.default.post(url, params);
                const result = response.data;
                if (result.errcode !== 0) {
                    throw new Error(`发送模板消息失败: ${result.errmsg}`);
                }
                logger_1.logger.info('模板消息发送成功');
            }
            catch (error) {
                logger_1.logger.error('发送模板消息失败:', error);
                throw new Error('发送模板消息失败');
            }
        });
    }
    /**
     * 内容安全检测
     */
    checkContent(content) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const accessToken = yield this.getAccessToken();
                const url = `https://api.weixin.qq.com/wxa/msg_sec_check?access_token=${accessToken}`;
                const response = yield axios_1.default.post(url, { content });
                const result = response.data;
                if (result.errcode === 0) {
                    return true; // 内容安全
                }
                else if (result.errcode === 87014) {
                    return false; // 内容违规
                }
                else {
                    logger_1.logger.warn('内容检测异常:', result.errmsg);
                    return true; // 检测异常时默认通过
                }
            }
            catch (error) {
                logger_1.logger.error('内容安全检测失败:', error);
                return true; // 检测失败时默认通过
            }
        });
    }
    /**
     * 图片内容安全检测
     */
    checkImage(imageBuffer) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const accessToken = yield this.getAccessToken();
                const url = `https://api.weixin.qq.com/wxa/img_sec_check?access_token=${accessToken}`;
                const formData = new FormData();
                formData.append('media', new Blob([new Uint8Array(imageBuffer)]), 'image.jpg');
                const response = yield axios_1.default.post(url, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });
                const result = response.data;
                if (result.errcode === 0) {
                    return true; // 图片安全
                }
                else if (result.errcode === 87014) {
                    return false; // 图片违规
                }
                else {
                    logger_1.logger.warn('图片检测异常:', result.errmsg);
                    return true; // 检测异常时默认通过
                }
            }
            catch (error) {
                logger_1.logger.error('图片安全检测失败:', error);
                return true; // 检测失败时默认通过
            }
        });
    }
    /**
     * 生成小程序码
     */
    generateQRCode(params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const accessToken = yield this.getAccessToken();
                const url = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${accessToken}`;
                const response = yield axios_1.default.post(url, {
                    scene: params.scene,
                    page: params.page || 'pages/home/<USER>',
                    width: params.width || 430
                }, {
                    responseType: 'arraybuffer'
                });
                return Buffer.from(response.data);
            }
            catch (error) {
                logger_1.logger.error('生成小程序码失败:', error);
                throw new Error('生成小程序码失败');
            }
        });
    }
}
exports.WechatService = WechatService;
exports.wechatService = new WechatService();
//# sourceMappingURL=wechatService.js.map