"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.exchangeController = void 0;
const logger_1 = require("../../utils/logger");
const errorHandler_1 = require("../../middleware/errorHandler");
// 模拟数据库操作 - 实际项目中应替换为真实的数据库交互
const mockExchangeDB = {
    exchanges: new Map(),
    findById(id) {
        return this.exchanges.get(id);
    },
    findByUserId(userId) {
        return Array.from(this.exchanges.values()).filter(exchange => exchange.requesterId === userId || exchange.responderId === userId);
    },
    create(exchange) {
        const id = `exchange_${Date.now()}`;
        this.exchanges.set(id, Object.assign(Object.assign({}, exchange), { id, createdAt: new Date(), updatedAt: new Date() }));
        return this.exchanges.get(id);
    },
    update(id, data) {
        if (!this.exchanges.has(id))
            return null;
        const exchange = Object.assign(Object.assign(Object.assign({}, this.exchanges.get(id)), data), { updatedAt: new Date() });
        this.exchanges.set(id, exchange);
        return exchange;
    }
};
// 模拟物品数据库
const mockItemDB = {
    items: new Map(),
    findById(id) {
        return this.items.get(id);
    },
    updateStatus(id, status) {
        const item = this.items.get(id);
        if (!item)
            return null;
        item.status = status;
        this.items.set(id, item);
        return item;
    }
};
// 模拟用户数据库
const mockUserDB = {
    users: new Map(),
    findById(id) {
        return this.users.get(id);
    },
    updateTrustLevel(id, trustLevel) {
        const user = this.users.get(id);
        if (!user)
            return null;
        user.trustLevel = trustLevel;
        this.users.set(id, user);
        return user;
    }
};
class ExchangeController {
    /**
     * 请求交换
     */
    requestExchange(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { itemId, targetItemId, message, meetLocation, meetTime } = req.body;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找物品
                const item = mockItemDB.findById(itemId);
                if (!item) {
                    throw new errorHandler_1.AppError('物品不存在', 404);
                }
                // 检查物品是否属于请求者
                if (item.userId !== userId) {
                    throw new errorHandler_1.AppError('不能请求交换不属于自己的物品', 403);
                }
                // 检查物品状态
                if (item.status !== 'active') {
                    throw new errorHandler_1.AppError('物品当前不可交换', 400);
                }
                // 查找目标物品（如果有）
                let targetItem = null;
                if (targetItemId) {
                    targetItem = mockItemDB.findById(targetItemId);
                    if (!targetItem) {
                        throw new errorHandler_1.AppError('目标物品不存在', 404);
                    }
                    if (targetItem.status !== 'active') {
                        throw new errorHandler_1.AppError('目标物品当前不可交换', 400);
                    }
                    if (targetItem.userId === userId) {
                        throw new errorHandler_1.AppError('不能与自己交换物品', 400);
                    }
                }
                // 创建交换请求
                const exchange = mockExchangeDB.create({
                    requesterId: userId,
                    responderId: targetItem ? targetItem.userId : null,
                    itemId,
                    targetItemId,
                    message,
                    meetLocation,
                    meetTime,
                    status: 'pending', // pending, accepted, rejected, confirmed, completed, disputed
                    rating: null,
                    comment: null
                });
                logger_1.logger.info(`创建交换请求成功: ${exchange.id}`, { requesterId: userId });
                res.status(201).json({
                    success: true,
                    data: exchange
                });
            }
            catch (error) {
                logger_1.logger.error('创建交换请求失败', { error });
                next(error);
            }
        });
    }
    /**
     * 回应交换请求
     */
    respondExchange(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { exchangeId, status, message, meetLocation, meetTime } = req.body;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找交换请求
                const exchange = mockExchangeDB.findById(exchangeId);
                if (!exchange) {
                    throw new errorHandler_1.AppError('交换请求不存在', 404);
                }
                // 检查是否是回应者
                if (exchange.responderId !== userId) {
                    throw new errorHandler_1.AppError('没有权限回应此交换请求', 403);
                }
                // 检查交换状态
                if (exchange.status !== 'pending') {
                    throw new errorHandler_1.AppError('交换请求当前不可回应', 400);
                }
                // 更新交换请求
                const updateData = {
                    status,
                    message,
                    respondedAt: new Date()
                };
                if (status === 'accept') {
                    if (!meetLocation || !meetTime) {
                        throw new errorHandler_1.AppError('接受交换时必须指定见面地点和时间', 400);
                    }
                    updateData.meetLocation = meetLocation;
                    updateData.meetTime = meetTime;
                }
                const updatedExchange = mockExchangeDB.update(exchangeId, updateData);
                logger_1.logger.info(`回应交换请求成功: ${exchangeId}, 状态: ${status}`, { responderId: userId });
                res.json({
                    success: true,
                    data: updatedExchange
                });
            }
            catch (error) {
                logger_1.logger.error('回应交换请求失败', { error });
                next(error);
            }
        });
    }
    /**
     * 确认交换
     */
    confirmExchange(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找交换请求
                const exchange = mockExchangeDB.findById(id);
                if (!exchange) {
                    throw new errorHandler_1.AppError('交换请求不存在', 404);
                }
                // 检查是否是请求者或回应者
                if (exchange.requesterId !== userId && exchange.responderId !== userId) {
                    throw new errorHandler_1.AppError('没有权限确认此交换', 403);
                }
                // 检查交换状态
                if (exchange.status !== 'accepted') {
                    throw new errorHandler_1.AppError('交换请求当前不可确认', 400);
                }
                // 更新交换请求
                const updatedExchange = mockExchangeDB.update(id, {
                    status: 'confirmed',
                    confirmedBy: userId,
                    confirmedAt: new Date()
                });
                logger_1.logger.info(`确认交换成功: ${id}`, { confirmedBy: userId });
                res.json({
                    success: true,
                    data: updatedExchange
                });
            }
            catch (error) {
                logger_1.logger.error('确认交换失败', { error });
                next(error);
            }
        });
    }
    /**
     * 完成交换
     */
    completeExchange(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找交换请求
                const exchange = mockExchangeDB.findById(id);
                if (!exchange) {
                    throw new errorHandler_1.AppError('交换请求不存在', 404);
                }
                // 检查是否是请求者或回应者
                if (exchange.requesterId !== userId && exchange.responderId !== userId) {
                    throw new errorHandler_1.AppError('没有权限完成此交换', 403);
                }
                // 检查交换状态
                if (exchange.status !== 'confirmed') {
                    throw new errorHandler_1.AppError('交换请求当前不可完成', 400);
                }
                // 更新交换请求
                const updatedExchange = mockExchangeDB.update(id, {
                    status: 'completed',
                    completedBy: userId,
                    completedAt: new Date()
                });
                // 更新物品状态
                if (exchange.itemId) {
                    mockItemDB.updateStatus(exchange.itemId, 'exchanged');
                }
                if (exchange.targetItemId) {
                    mockItemDB.updateStatus(exchange.targetItemId, 'exchanged');
                }
                // 更新用户信任等级
                // 实际项目中应根据交换结果计算信任等级变化
                const requester = mockUserDB.findById(exchange.requesterId);
                if (requester && requester.trustLevel) {
                    mockUserDB.updateTrustLevel(exchange.requesterId, Object.assign(Object.assign({}, requester.trustLevel), { exchangeCount: requester.trustLevel.exchangeCount + 1, successRate: ((requester.trustLevel.successRate * requester.trustLevel.exchangeCount) + 1) / (requester.trustLevel.exchangeCount + 1) }));
                }
                const responder = mockUserDB.findById(exchange.responderId);
                if (responder && responder.trustLevel) {
                    mockUserDB.updateTrustLevel(exchange.responderId, Object.assign(Object.assign({}, responder.trustLevel), { exchangeCount: responder.trustLevel.exchangeCount + 1, successRate: ((responder.trustLevel.successRate * responder.trustLevel.exchangeCount) + 1) / (responder.trustLevel.exchangeCount + 1) }));
                }
                logger_1.logger.info(`完成交换成功: ${id}`, { completedBy: userId });
                res.json({
                    success: true,
                    data: updatedExchange
                });
            }
            catch (error) {
                logger_1.logger.error('完成交换失败', { error });
                next(error);
            }
        });
    }
    /**
     * 评价交换
     */
    rateExchange(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const { rating, comment } = req.body;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找交换请求
                const exchange = mockExchangeDB.findById(id);
                if (!exchange) {
                    throw new errorHandler_1.AppError('交换请求不存在', 404);
                }
                // 检查是否是请求者或回应者
                if (exchange.requesterId !== userId && exchange.responderId !== userId) {
                    throw new errorHandler_1.AppError('没有权限评价此交换', 403);
                }
                // 检查交换状态
                if (exchange.status !== 'completed') {
                    throw new errorHandler_1.AppError('只能评价已完成的交换', 400);
                }
                // 检查是否已经评价
                if (exchange.rating !== null) {
                    throw new errorHandler_1.AppError('已经评价过此交换', 400);
                }
                // 更新交换请求
                const updatedExchange = mockExchangeDB.update(id, {
                    rating,
                    comment,
                    ratedBy: userId,
                    ratedAt: new Date()
                });
                // 更新被评价用户的信任等级
                const targetUserId = exchange.requesterId === userId ? exchange.responderId : exchange.requesterId;
                const targetUser = mockUserDB.findById(targetUserId);
                if (targetUser && targetUser.trustLevel) {
                    const currentCount = targetUser.trustLevel.exchangeCount;
                    const currentRating = targetUser.trustLevel.averageRating;
                    mockUserDB.updateTrustLevel(targetUserId, Object.assign(Object.assign({}, targetUser.trustLevel), { averageRating: ((currentRating * currentCount) + rating) / (currentCount + 1), 
                        // 更新信任等级分数
                        score: targetUser.trustLevel.score + (rating >= 4 ? 5 : (rating === 3 ? 2 : -3)) }));
                }
                logger_1.logger.info(`评价交换成功: ${id}`, { ratedBy: userId });
                res.json({
                    success: true,
                    data: updatedExchange
                });
            }
            catch (error) {
                logger_1.logger.error('评价交换失败', { error });
                next(error);
            }
        });
    }
    /**
     * 发起争议
     */
    disputeExchange(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const { dispute } = req.body;
                const { reason, description, evidence } = dispute;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找交换请求
                const exchange = mockExchangeDB.findById(id);
                if (!exchange) {
                    throw new errorHandler_1.AppError('交换请求不存在', 404);
                }
                // 检查是否是请求者或回应者
                if (exchange.requesterId !== userId && exchange.responderId !== userId) {
                    throw new errorHandler_1.AppError('没有权限对此交换发起争议', 403);
                }
                // 检查交换状态
                if (exchange.status === 'completed' || exchange.status === 'disputed') {
                    throw new errorHandler_1.AppError('当前状态无法发起争议', 400);
                }
                // 更新交换请求
                const updatedExchange = mockExchangeDB.update(id, {
                    status: 'disputed',
                    disputeReason: reason,
                    disputeDescription: description,
                    disputeEvidence: evidence || [],
                    disputedBy: userId,
                    disputedAt: new Date()
                });
                logger_1.logger.info(`发起争议成功: ${id}`, { disputedBy: userId });
                res.json({
                    success: true,
                    data: updatedExchange
                });
            }
            catch (error) {
                logger_1.logger.error('发起争议失败', { error });
                next(error);
            }
        });
    }
    /**
     * 获取用户交换列表
     */
    getUserExchanges(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { page = 1, pageSize = 10, status } = req.query;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 获取用户的所有交换
                let exchanges = mockExchangeDB.findByUserId(userId);
                // 按状态筛选
                if (status) {
                    exchanges = exchanges.filter(exchange => exchange.status === status);
                }
                // 按创建时间排序（最新的在前）
                exchanges.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
                // 分页处理
                const startIndex = (Number(page) - 1) * Number(pageSize);
                const endIndex = startIndex + Number(pageSize);
                const paginatedExchanges = exchanges.slice(startIndex, endIndex);
                logger_1.logger.info(`获取用户交换列表成功，共 ${exchanges.length} 条记录`, { userId });
                res.json({
                    success: true,
                    data: {
                        items: paginatedExchanges,
                        total: exchanges.length,
                        page: Number(page),
                        pageSize: Number(pageSize),
                        hasMore: endIndex < exchanges.length
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('获取用户交换列表失败', { error });
                next(error);
            }
        });
    }
    /**
     * 获取交换详情
     */
    getExchangeDetail(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找交换请求
                const exchange = mockExchangeDB.findById(id);
                if (!exchange) {
                    throw new errorHandler_1.AppError('交换请求不存在', 404);
                }
                // 检查是否有权限查看
                if (exchange.requesterId !== userId && exchange.responderId !== userId) {
                    throw new errorHandler_1.AppError('没有权限查看此交换详情', 403);
                }
                logger_1.logger.info(`获取交换详情成功: ${id}`, { userId });
                res.json({
                    success: true,
                    data: exchange
                });
            }
            catch (error) {
                logger_1.logger.error('获取交换详情失败', { error });
                next(error);
            }
        });
    }
}
exports.exchangeController = new ExchangeController();
//# sourceMappingURL=exchangeController.js.map