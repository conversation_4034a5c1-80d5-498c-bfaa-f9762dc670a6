"use strict";
/**
 * 小程序用户控制器
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userController = void 0;
const errorHandler_1 = require("../../middleware/errorHandler");
const logger_1 = require("../../utils/logger");
// 模拟用户数据库
const mockUserDB = {
    // 存储用户数据的对象
    users: new Map([
        ['user123', {
                id: 'user123',
                openId: 'oXPrX5Z7...',
                phone: '13800138000',
                name: '张三',
                avatar: 'https://example.com/avatar.jpg',
                idCard: '110101199001011234',
                trustLevel: 4.8,
                createdAt: new Date(),
                updatedAt: new Date()
            }]
    ]),
    // 获取用户信息
    getUserById(id) {
        return this.users.get(id);
    },
    // 更新用户信息
    updateUser(id, data) {
        if (!this.users.has(id))
            return null;
        const user = Object.assign(Object.assign(Object.assign({}, this.users.get(id)), data), { updatedAt: new Date() });
        this.users.set(id, user);
        return user;
    },
    // 获取用户信任等级
    getUserTrustLevel(id) {
        const user = this.users.get(id);
        return user ? user.trustLevel : null;
    }
};
class UserController {
    /**
     * 获取用户资料
     */
    getProfile(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'] || 'user123';
                const user = mockUserDB.getUserById(userId);
                if (!user) {
                    return next(new errorHandler_1.AppError('用户不存在', 404));
                }
                res.json({
                    success: true,
                    data: {
                        id: user.id,
                        name: user.name,
                        avatar: user.avatar,
                        phone: user.phone,
                        trustLevel: user.trustLevel
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('获取用户资料失败', error);
                next(error);
            }
        });
    }
    /**
     * 更新用户资料
     */
    updateProfile(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'] || 'user123';
                const updatedUser = mockUserDB.updateUser(userId, req.body);
                if (!updatedUser) {
                    return next(new errorHandler_1.AppError('用户不存在', 404));
                }
                res.json({
                    success: true,
                    data: {
                        id: updatedUser.id,
                        name: updatedUser.name,
                        avatar: updatedUser.avatar,
                        phone: updatedUser.phone
                    },
                    message: '资料更新成功'
                });
            }
            catch (error) {
                logger_1.logger.error('更新用户资料失败', error);
                next(error);
            }
        });
    }
    /**
     * 获取用户信任等级
     */
    getTrustLevel(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { userId } = req.params;
                const trustLevel = mockUserDB.getUserTrustLevel(userId);
                if (trustLevel === null) {
                    return next(new errorHandler_1.AppError('用户不存在', 404));
                }
                res.json({
                    success: true,
                    data: {
                        userId,
                        trustLevel
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('获取用户信任等级失败', error);
                next(error);
            }
        });
    }
    /**
     * 提交用户反馈
     */
    submitFeedback(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { content, contactInfo } = req.body;
                // 在实际应用中，这里会将反馈存储到数据库
                logger_1.logger.info('收到用户反馈', { content, contactInfo });
                res.json({
                    success: true,
                    message: '反馈提交成功，我们将尽快处理',
                    data: {
                        feedbackId: `fb-${Date.now()}`,
                        timestamp: new Date()
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('提交用户反馈失败', error);
                next(error);
            }
        });
    }
}
exports.userController = new UserController();
//# sourceMappingURL=userController.js.map