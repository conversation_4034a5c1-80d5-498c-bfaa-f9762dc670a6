"use strict";
/**
 * 物品控制器 - 使用真实数据库
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.itemController = void 0;
const logger_1 = require("../../utils/logger");
const errorHandler_1 = require("../../middleware/errorHandler");
const itemService_1 = require("../../services/itemService");
class ItemController {
    /**
     * 创建物品
     */
    createItem(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const itemData = req.body;
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 验证必需字段
                if (!itemData.title || !itemData.description || !itemData.categoryId || !itemData.condition || !itemData.estimatedValue) {
                    throw new errorHandler_1.AppError('缺少必需字段', 400);
                }
                // 创建物品
                const item = yield itemService_1.itemService.create(Object.assign(Object.assign({}, itemData), { publisherId: userId }));
                logger_1.logger.info('物品创建成功: ' + item.id);
                res.status(201).json({
                    success: true,
                    data: item
                });
            }
            catch (error) {
                logger_1.logger.error('创建物品失败', { error });
                next(error);
            }
        });
    }
    /**
     * 获取物品详情
     */
    getItemDetail(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { id } = req.params;
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
                const item = yield itemService_1.itemService.findById(id, userId);
                if (!item) {
                    throw new errorHandler_1.AppError('物品不存在', 404);
                }
                res.json({
                    success: true,
                    data: item
                });
            }
            catch (error) {
                logger_1.logger.error('获取物品详情失败', { error });
                next(error);
            }
        });
    }
    /**
     * 搜索物品
     */
    searchItems(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { keyword, categoryId, communityId, condition, minValue, maxValue, sortBy = 'createdAt', sortOrder = 'desc', page = 1, limit = 20 } = req.query;
                const skip = (page - 1) * limit;
                const result = yield itemService_1.itemService.findMany({
                    skip,
                    take: limit,
                    communityId,
                    categoryId,
                    search: keyword,
                    condition,
                    minValue: minValue ? parseFloat(minValue) : undefined,
                    maxValue: maxValue ? parseFloat(maxValue) : undefined,
                    sortBy,
                    sortOrder
                });
                res.json({
                    success: true,
                    data: {
                        items: result.items,
                        total: result.total,
                        page: parseInt(page),
                        limit: parseInt(limit),
                        totalPages: Math.ceil(result.total / limit)
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('搜索物品失败', { error });
                next(error);
            }
        });
    }
    /**
     * 更新物品
     */
    updateItem(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { id } = req.params;
                const updateData = req.body;
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 检查物品是否存在且属于当前用户
                const existingItem = yield itemService_1.itemService.findById(id);
                if (!existingItem) {
                    throw new errorHandler_1.AppError('物品不存在', 404);
                }
                if (existingItem.publisherId !== userId) {
                    throw new errorHandler_1.AppError('无权限修改此物品', 403);
                }
                const updatedItem = yield itemService_1.itemService.update(id, updateData);
                logger_1.logger.info('物品更新成功: ' + id);
                res.json({
                    success: true,
                    data: updatedItem
                });
            }
            catch (error) {
                logger_1.logger.error('更新物品失败', { error });
                next(error);
            }
        });
    }
    /**
     * 删除物品
     */
    deleteItem(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { id } = req.params;
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 检查物品是否存在且属于当前用户
                const existingItem = yield itemService_1.itemService.findById(id);
                if (!existingItem) {
                    throw new errorHandler_1.AppError('物品不存在', 404);
                }
                if (existingItem.publisherId !== userId) {
                    throw new errorHandler_1.AppError('无权限删除此物品', 403);
                }
                yield itemService_1.itemService.delete(id);
                logger_1.logger.info('物品删除成功: ' + id);
                res.json({
                    success: true,
                    message: '物品删除成功'
                });
            }
            catch (error) {
                logger_1.logger.error('删除物品失败', { error });
                next(error);
            }
        });
    }
    /**
     * 切换收藏状态
     */
    toggleFavorite(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { id } = req.params;
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                const isFavorited = yield itemService_1.itemService.toggleFavorite(id, userId);
                res.json({
                    success: true,
                    data: {
                        isFavorited,
                        message: isFavorited ? '收藏成功' : '取消收藏成功'
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('切换收藏状态失败', { error });
                next(error);
            }
        });
    }
    /**
     * 获取用户发布的物品
     */
    getUserItems(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
                const { page = 1, limit = 20, status = 'available' } = req.query;
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                const skip = (page - 1) * limit;
                const result = yield itemService_1.itemService.findMany({
                    skip,
                    take: limit,
                    publisherId: userId,
                    status
                });
                res.json({
                    success: true,
                    data: {
                        items: result.items,
                        total: result.total,
                        page: parseInt(page),
                        limit: parseInt(limit),
                        totalPages: Math.ceil(result.total / limit)
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('获取用户物品失败', { error });
                next(error);
            }
        });
    }
}
exports.itemController = new ItemController();
//# sourceMappingURL=itemController.js.map