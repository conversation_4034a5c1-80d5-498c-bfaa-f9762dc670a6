"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.communityController = void 0;
const logger_1 = require("../../utils/logger");
const errorHandler_1 = require("../../middleware/errorHandler");
// 模拟数据库操作 - 实际项目中应替换为真实的数据库交互
const mockCommunityDB = {
    communities: new Map(),
    communityMembers: new Map(),
    findById(id) {
        return this.communities.get(id);
    },
    findByLocation(location, radius = 5) {
        // 模拟按位置查找社区
        return Array.from(this.communities.values()).filter(community => {
            // 简化的距离计算
            const dx = community.location.latitude - location.latitude;
            const dy = community.location.longitude - location.longitude;
            const distance = Math.sqrt(dx * dx + dy * dy) * 111; // 约略转换为公里
            return distance <= radius;
        });
    },
    create(community) {
        const id = `community_${Date.now()}`;
        this.communities.set(id, Object.assign(Object.assign({}, community), { id, createdAt: new Date(), updatedAt: new Date(), memberCount: 0 }));
        return this.communities.get(id);
    },
    update(id, data) {
        if (!this.communities.has(id))
            return null;
        const community = Object.assign(Object.assign(Object.assign({}, this.communities.get(id)), data), { updatedAt: new Date() });
        this.communities.set(id, community);
        return community;
    },
    join(userId, communityId, reason) {
        const membershipId = `membership_${userId}_${communityId}`;
        this.communityMembers.set(membershipId, {
            id: membershipId,
            userId,
            communityId,
            joinReason: reason,
            joinDate: new Date(),
            status: 'active'
        });
        // 更新社区成员数量
        const community = this.communities.get(communityId);
        if (community) {
            community.memberCount += 1;
            this.communities.set(communityId, community);
        }
        return this.communityMembers.get(membershipId);
    },
    leave(userId, communityId) {
        const membershipId = `membership_${userId}_${communityId}`;
        this.communityMembers.delete(membershipId);
        // 更新社区成员数量
        const community = this.communities.get(communityId);
        if (community) {
            community.memberCount = Math.max(0, community.memberCount - 1);
            this.communities.set(communityId, community);
        }
        return true;
    },
    isMember(userId, communityId) {
        const membershipId = `membership_${userId}_${communityId}`;
        return this.communityMembers.has(membershipId);
    },
    getCommunityItems(communityId) {
        // 模拟获取社区物品
        return [];
    }
};
class CommunityController {
    /**
     * 获取社区列表
     */
    getCommunityList(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { page = 1, pageSize = 10, radius = 5 } = req.query;
                const location = req.body.location || req.query.location;
                if (!location) {
                    throw new errorHandler_1.AppError('位置信息是必填项', 400);
                }
                // 按位置查找社区
                const communities = mockCommunityDB.findByLocation(location, Number(radius));
                // 分页处理
                const startIndex = (Number(page) - 1) * Number(pageSize);
                const endIndex = startIndex + Number(pageSize);
                const paginatedCommunities = communities.slice(startIndex, endIndex);
                logger_1.logger.info(`获取社区列表成功，共 ${communities.length} 个社区`);
                res.json({
                    success: true,
                    data: {
                        items: paginatedCommunities,
                        total: communities.length,
                        page: Number(page),
                        pageSize: Number(pageSize),
                        hasMore: endIndex < communities.length
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('获取社区列表失败', { error });
                next(error);
            }
        });
    }
    /**
     * 创建社区
     */
    createCommunity(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const communityData = req.body;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 创建社区
                const community = mockCommunityDB.create(Object.assign(Object.assign({}, communityData), { creatorId: userId, status: 'active' }));
                // 创建者自动加入社区
                mockCommunityDB.join(userId, community.id, '创建者');
                logger_1.logger.info(`创建社区成功: ${community.id}`, { creatorId: userId });
                res.status(201).json({
                    success: true,
                    data: community
                });
            }
            catch (error) {
                logger_1.logger.error('创建社区失败', { error });
                next(error);
            }
        });
    }
    /**
     * 获取社区详情
     */
    getCommunityDetail(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                // 查找社区
                const community = mockCommunityDB.findById(id);
                if (!community) {
                    throw new errorHandler_1.AppError('社区不存在', 404);
                }
                logger_1.logger.info(`获取社区详情成功: ${id}`);
                res.json({
                    success: true,
                    data: community
                });
            }
            catch (error) {
                logger_1.logger.error('获取社区详情失败', { error });
                next(error);
            }
        });
    }
    /**
     * 更新社区
     */
    updateCommunity(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const updateData = req.body;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找社区
                const community = mockCommunityDB.findById(id);
                if (!community) {
                    throw new errorHandler_1.AppError('社区不存在', 404);
                }
                // 检查权限 - 只有创建者可以更新社区
                if (community.creatorId !== userId) {
                    throw new errorHandler_1.AppError('没有权限更新此社区', 403);
                }
                // 更新社区
                const updatedCommunity = mockCommunityDB.update(id, updateData);
                logger_1.logger.info(`更新社区成功: ${id}`, { updaterId: userId });
                res.json({
                    success: true,
                    data: updatedCommunity
                });
            }
            catch (error) {
                logger_1.logger.error('更新社区失败', { error });
                next(error);
            }
        });
    }
    /**
     * 获取社区物品
     */
    getCommunityItems(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const { page = 1, pageSize = 10 } = req.query;
                // 查找社区
                const community = mockCommunityDB.findById(id);
                if (!community) {
                    throw new errorHandler_1.AppError('社区不存在', 404);
                }
                // 获取社区物品
                const items = mockCommunityDB.getCommunityItems(id);
                // 分页处理
                const startIndex = (Number(page) - 1) * Number(pageSize);
                const endIndex = startIndex + Number(pageSize);
                const paginatedItems = items.slice(startIndex, endIndex);
                logger_1.logger.info(`获取社区物品成功: ${id}, 共 ${items.length} 个物品`);
                res.json({
                    success: true,
                    data: {
                        items: paginatedItems,
                        total: items.length,
                        page: Number(page),
                        pageSize: Number(pageSize),
                        hasMore: endIndex < items.length
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('获取社区物品失败', { error });
                next(error);
            }
        });
    }
    /**
     * 加入社区
     */
    joinCommunity(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { communityId, reason } = req.body;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找社区
                const community = mockCommunityDB.findById(communityId);
                if (!community) {
                    throw new errorHandler_1.AppError('社区不存在', 404);
                }
                // 检查是否已经是社区成员
                if (mockCommunityDB.isMember(userId, communityId)) {
                    throw new errorHandler_1.AppError('已经是社区成员', 400);
                }
                // 加入社区
                const membership = mockCommunityDB.join(userId, communityId, reason || '');
                logger_1.logger.info(`用户加入社区成功: ${userId} -> ${communityId}`);
                res.json({
                    success: true,
                    data: membership
                });
            }
            catch (error) {
                logger_1.logger.error('加入社区失败', { error });
                next(error);
            }
        });
    }
    /**
     * 退出社区
     */
    leaveCommunity(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                // 从请求头获取用户ID - 实际项目中应从JWT令牌中解析
                const userId = req.headers['x-user-id'];
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找社区
                const community = mockCommunityDB.findById(id);
                if (!community) {
                    throw new errorHandler_1.AppError('社区不存在', 404);
                }
                // 检查是否是社区成员
                if (!mockCommunityDB.isMember(userId, id)) {
                    throw new errorHandler_1.AppError('不是社区成员', 400);
                }
                // 检查是否是创建者
                if (community.creatorId === userId) {
                    throw new errorHandler_1.AppError('创建者不能退出社区', 400);
                }
                // 退出社区
                const result = mockCommunityDB.leave(userId, id);
                logger_1.logger.info(`用户退出社区成功: ${userId} <- ${id}`);
                res.json({
                    success: true,
                    data: result
                });
            }
            catch (error) {
                logger_1.logger.error('退出社区失败', { error });
                next(error);
            }
        });
    }
}
exports.communityController = new CommunityController();
//# sourceMappingURL=communityController.js.map