"use strict";
/**
 * 小程序端用户认证控制器
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authController = void 0;
const jsonwebtoken_1 = require("jsonwebtoken");
const index_1 = require("../../config/index");
const logger_1 = require("../../utils/logger");
const errorHandler_1 = require("../../middleware/errorHandler");
const wechatService_1 = require("../../services/wechatService");
const userService_1 = require("../../services/userService");
class AuthController {
    /**
     * 处理微信登录
     */
    wechatLogin(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { code, userInfo } = req.body;
                if (!code) {
                    throw new errorHandler_1.AppError('缺少登录凭证', 400);
                }
                // 1. 使用微信服务获取openid
                const wechatResult = yield wechatService_1.wechatService.login(code);
                if (!wechatResult.openid) {
                    throw new errorHandler_1.AppError('微信登录失败', 400);
                }
                // 2. 查找或创建用户
                let user = yield userService_1.userService.findByPhone(wechatResult.openid); // 临时使用openid作为唯一标识
                const isNewUser = !user;
                if (!user) {
                    // 创建新用户
                    user = yield userService_1.userService.create({
                        phone: wechatResult.openid, // 临时使用openid
                        name: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.nickName) || '微信用户',
                        avatar: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.avatarUrl) || ''
                    });
                }
                else {
                    // 更新用户信息
                    user = yield userService_1.userService.update(user.id, {
                        name: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.nickName) || user.name,
                        avatar: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.avatarUrl) || user.avatar || undefined
                    });
                }
                // 3. 生成JWT token
                const jwtSecret = index_1.config.JWT_SECRET || 'fallback-secret-key';
                const token = (0, jsonwebtoken_1.sign)({
                    id: user.id,
                    openid: wechatResult.openid,
                    role: 'user'
                }, jwtSecret, { expiresIn: '7d' });
                // 4. 构造响应数据
                const authResult = {
                    token,
                    user: {
                        id: user.id,
                        name: user.name,
                        avatar: user.avatar,
                        phone: user.phone,
                        communityId: user.communityId,
                        trustLevel: user.trustLevel,
                        trustScore: user.trustScore,
                        isVerified: user.isVerified,
                        isActive: user.isActive
                    },
                    isNewUser
                };
                logger_1.logger.info(`用户登录成功: ${user.id}`, { isNewUser });
                res.json({
                    success: true,
                    data: authResult
                });
            }
            catch (error) {
                logger_1.logger.error('微信登录失败', { error });
                next(error);
            }
        });
    }
    /**
     * 绑定手机号
     */
    bindPhone(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { encryptedData, iv } = req.body;
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找用户
                const user = yield userService_1.userService.findById(userId);
                if (!user) {
                    throw new errorHandler_1.AppError('用户不存在', 404);
                }
                // 解密手机号 - 实际项目中应使用微信提供的解密算法
                // 这里为了演示，我们直接模拟解密结果
                const phoneNumber = '13800138000';
                // 更新用户信息
                const updatedUser = yield userService_1.userService.update(userId, {
                    phone: phoneNumber
                });
                logger_1.logger.info(`用户绑定手机号成功: ${userId}`, { phone: phoneNumber });
                res.json({
                    success: true,
                    data: {
                        phone: phoneNumber
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('绑定手机号失败', { error });
                next(error);
            }
        });
    }
    /**
     * 身份验证
     */
    verifyIdentity(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
                if (!userId) {
                    throw new errorHandler_1.AppError('未授权访问', 401);
                }
                // 查找用户
                const user = yield userService_1.userService.findById(userId);
                if (!user) {
                    throw new errorHandler_1.AppError('用户不存在', 404);
                }
                // 实际项目中应验证身份证信息
                const { name, idCard } = req.body;
                // 更新用户信息
                const updatedUser = yield userService_1.userService.update(userId, {
                    isVerified: true,
                    name: name || user.name
                });
                logger_1.logger.info(`用户身份验证成功: ${userId}`);
                res.json({
                    success: true,
                    data: {
                        isVerified: true
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('身份验证失败', { error });
                next(error);
            }
        });
    }
}
exports.authController = new AuthController();
//# sourceMappingURL=authController.js.map