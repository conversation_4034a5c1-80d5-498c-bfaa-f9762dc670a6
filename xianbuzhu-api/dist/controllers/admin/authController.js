"use strict";
/**
 * 管理后台认证控制器
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authController = void 0;
const logger_1 = require("../../utils/logger");
const bcryptjs_1 = require("bcryptjs");
const jsonwebtoken_1 = require("jsonwebtoken");
const index_1 = require("../../config/index");
// 模拟管理员用户数据
// 实际应用中应该从数据库查询
const adminUsers = [
    {
        id: '1',
        username: 'admin',
        password: '$2b$10$EixZaYVK1fsbw1Zfbx3OXePA3KZ76DQqB4lRZjgtjOzU5uP3IThh', // 密码: admin123
        name: '超级管理员',
        role: 'superadmin',
        permissions: ['all'],
        email: '<EMAIL>'
    }
];
class AuthController {
    /**
     * 管理员登录
     * @param req 请求对象
     * @param res 响应对象
     */
    login(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { username, password } = req.body;
                // 查找管理员用户
                const admin = adminUsers.find(u => u.username === username);
                if (!admin) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'AUTH_FAILED',
                            message: '用户名或密码错误'
                        }
                    });
                    return;
                }
                // 验证密码
                const isPasswordValid = yield (0, bcryptjs_1.compare)(password, admin.password);
                if (!isPasswordValid) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'AUTH_FAILED',
                            message: '用户名或密码错误'
                        }
                    });
                    return;
                }
                // 生成JWT令牌
                const token = (0, jsonwebtoken_1.sign)({
                    id: admin.id,
                    username: admin.username,
                    role: admin.role,
                    permissions: admin.permissions
                }, index_1.config.JWT_SECRET || 'your-secret-key', {
                    expiresIn: '24h'
                });
                // 返回登录成功响应
                res.json({
                    success: true,
                    data: {
                        token,
                        admin: {
                            id: admin.id,
                            username: admin.username,
                            name: admin.name,
                            role: admin.role,
                            permissions: admin.permissions,
                            email: admin.email
                        }
                    },
                    message: '登录成功'
                });
                logger_1.logger.info(`管理员 ${username} 登录成功`);
            }
            catch (error) {
                logger_1.logger.error('管理员登录失败:', error);
                res.status(500).json({
                    success: false,
                    error: {
                        code: 'SERVER_ERROR',
                        message: '服务器内部错误'
                    }
                });
            }
        });
    }
    /**
     * 管理员登出
     * @param req 请求对象
     * @param res 响应对象
     */
    logout(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 前端应该删除本地存储的token
                res.json({
                    success: true,
                    message: '登出成功'
                });
                logger_1.logger.info('管理员登出成功');
            }
            catch (error) {
                logger_1.logger.error('管理员登出失败:', error);
                res.status(500).json({
                    success: false,
                    error: {
                        code: 'SERVER_ERROR',
                        message: '服务器内部错误'
                    }
                });
            }
        });
    }
}
exports.authController = new AuthController();
//# sourceMappingURL=authController.js.map