"use strict";
/**
 * 管理后台路由
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = require("../controllers/admin/authController");
const validation_1 = require("../middleware/validation");
const authValidators_1 = require("../validators/authValidators");
const router = (0, express_1.Router)();
// 认证路由
router.post('/auth/login', (0, validation_1.validateRequest)(authValidators_1.adminLoginSchema), authController_1.authController.login);
router.post('/auth/logout', authController_1.authController.logout);
// 未来可以添加更多管理后台路由
// router.use('/users', userRoutes)
// router.use('/items', itemRoutes)
// router.use('/exchanges', exchangeRoutes)
exports.default = router;
//# sourceMappingURL=admin.js.map