"use strict";
/**
 * 小程序端路由
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
// 导入子路由
const auth_1 = __importDefault(require("./miniprogram/auth"));
const user_1 = __importDefault(require("./miniprogram/user"));
const item_1 = __importDefault(require("./miniprogram/item"));
const exchange_1 = __importDefault(require("./miniprogram/exchange"));
const community_1 = __importDefault(require("./miniprogram/community"));
const router = (0, express_1.Router)();
// 挂载子路由
router.use('/auth', auth_1.default);
router.use('/users', user_1.default);
router.use('/items', item_1.default);
router.use('/exchanges', exchange_1.default);
router.use('/communities', community_1.default);
exports.default = router;
//# sourceMappingURL=miniprogram.js.map