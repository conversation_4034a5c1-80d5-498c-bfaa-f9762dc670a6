"use strict";
/**
 * 小程序物品路由
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../../middleware/validation");
const itemController_1 = require("../../controllers/miniprogram/itemController");
const itemValidators_1 = require("../../validators/itemValidators");
const router = (0, express_1.Router)();
// 获取用户物品列表
router.get('/', itemController_1.itemController.getUserItems);
// 创建物品
router.post('/', (0, validation_1.validateRequest)(itemValidators_1.createItemSchema), itemController_1.itemController.createItem);
// 获取物品详情
router.get('/:id', itemController_1.itemController.getItemDetail);
// 更新物品
router.put('/:id', (0, validation_1.validateRequest)(itemValidators_1.updateItemSchema), itemController_1.itemController.updateItem);
// 删除物品
router.delete('/:id', itemController_1.itemController.deleteItem);
// 搜索物品
router.get('/search', (0, validation_1.validateRequest)(itemValidators_1.searchItemSchema), itemController_1.itemController.searchItems);
// 收藏物品
router.post('/:id/favorite', itemController_1.itemController.toggleFavorite);
// 获取用户物品
router.get('/user', itemController_1.itemController.getUserItems);
exports.default = router;
//# sourceMappingURL=item.js.map