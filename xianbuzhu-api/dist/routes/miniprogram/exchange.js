"use strict";
/**
 * 小程序交换路由
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../../middleware/validation");
const exchangeController_1 = require("../../controllers/miniprogram/exchangeController");
const exchangeValidators_1 = require("../../validators/exchangeValidators");
const router = (0, express_1.Router)();
// 请求交换
router.post('/request', (0, validation_1.validateRequest)(exchangeValidators_1.requestExchangeSchema), exchangeController_1.exchangeController.requestExchange);
// 回应交换请求
router.put('/:id/respond', (0, validation_1.validateRequest)(exchangeValidators_1.respondExchangeSchema), exchangeController_1.exchangeController.respondExchange);
// 确认交换
router.put('/:id/confirm', exchangeController_1.exchangeController.confirmExchange);
// 完成交换
router.put('/:id/complete', exchangeController_1.exchangeController.completeExchange);
// 评价交换
router.post('/:id/rate', (0, validation_1.validateRequest)(exchangeValidators_1.rateExchangeSchema), exchangeController_1.exchangeController.rateExchange);
// 发起争议
router.post('/:id/dispute', exchangeController_1.exchangeController.disputeExchange);
// 获取用户的交换列表
router.get('/my', exchangeController_1.exchangeController.getUserExchanges);
// 获取交换详情
router.get('/:id', exchangeController_1.exchangeController.getExchangeDetail);
exports.default = router;
//# sourceMappingURL=exchange.js.map