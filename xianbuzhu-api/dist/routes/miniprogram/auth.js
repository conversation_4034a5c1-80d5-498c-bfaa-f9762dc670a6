"use strict";
/**
 * 小程序端用户认证路由
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = require("../../controllers/miniprogram/authController");
const validation_1 = require("../../middleware/validation");
const authValidators_1 = require("../../validators/authValidators");
const router = (0, express_1.Router)();
// 微信登录
router.post('/wechat-login', (0, validation_1.validateRequest)(authValidators_1.loginSchema), authController_1.authController.wechatLogin);
// 绑定手机号
router.post('/bind-phone', (0, validation_1.validateRequest)(authValidators_1.bindPhoneSchema), authController_1.authController.bindPhone);
// 身份验证
router.post('/verify-identity', authController_1.authController.verifyIdentity);
exports.default = router;
//# sourceMappingURL=auth.js.map