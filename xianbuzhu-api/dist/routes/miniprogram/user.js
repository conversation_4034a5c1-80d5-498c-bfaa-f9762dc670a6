"use strict";
/**
 * 小程序用户路由
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../../middleware/validation");
const userController_1 = require("../../controllers/miniprogram/userController");
const userValidators_1 = require("../../validators/userValidators");
const router = (0, express_1.Router)();
// 获取用户资料
router.get('/profile', userController_1.userController.getProfile);
// 更新用户资料
router.put('/profile', (0, validation_1.validateRequest)(userValidators_1.updateProfileSchema), userController_1.userController.updateProfile);
// 获取用户信任等级
router.get('/:userId/trust', userController_1.userController.getTrustLevel);
// 提交用户反馈
router.post('/feedback', userController_1.userController.submitFeedback);
exports.default = router;
//# sourceMappingURL=user.js.map