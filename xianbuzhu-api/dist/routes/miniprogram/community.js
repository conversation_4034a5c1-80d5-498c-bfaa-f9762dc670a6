"use strict";
/**
 * 小程序社区路由
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../../middleware/validation");
const communityController_1 = require("../../controllers/miniprogram/communityController");
const communityValidators_1 = require("../../validators/communityValidators");
const router = (0, express_1.Router)();
// 获取社区列表
router.get('/', communityController_1.communityController.getCommunityList);
// 创建社区
router.post('/', (0, validation_1.validateRequest)(communityValidators_1.createCommunitySchema), communityController_1.communityController.createCommunity);
// 获取社区详情
router.get('/:id', communityController_1.communityController.getCommunityDetail);
// 更新社区
router.put('/:id', (0, validation_1.validateRequest)(communityValidators_1.updateCommunitySchema), communityController_1.communityController.updateCommunity);
// 获取社区物品
router.get('/:id/items', communityController_1.communityController.getCommunityItems);
// 获取社区地图 (暂时移除，控制器中无此方法)
// router.get('/map', communityController.getCommunityMap)
// 加入社区
router.post('/:id/join', communityController_1.communityController.joinCommunity);
// 退出社区
router.post('/:id/leave', communityController_1.communityController.leaveCommunity);
exports.default = router;
//# sourceMappingURL=community.js.map