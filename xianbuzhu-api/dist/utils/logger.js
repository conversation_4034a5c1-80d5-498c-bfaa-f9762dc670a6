"use strict";
/**
 * 日志工具
 */
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const config_1 = require("../config");
// 定义日志级别
const levels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    verbose: 4,
    debug: 5,
    silly: 6
};
// 定义基础格式（无颜色）
const baseFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }), winston_1.default.format.printf((_a) => {
    var { timestamp, level, message } = _a, meta = __rest(_a, ["timestamp", "level", "message"]);
    if (typeof message === 'object') {
        message = JSON.stringify(message, null, 2);
    }
    return `[${timestamp}] ${level}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
}));
// 定义控制台格式（带颜色）
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize({ all: true }), baseFormat);
// 定义日志传输
const transports = [
    new winston_1.default.transports.Console({
        format: consoleFormat,
        level: config_1.config.LOG_LEVEL || 'info'
    })
];
// 如果是生产环境，添加文件传输
if (config_1.config.NODE_ENV === 'production') {
    transports.push(new winston_1.default.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: baseFormat
    }), new winston_1.default.transports.File({
        filename: 'logs/combined.log',
        format: baseFormat
    }));
}
// 创建logger实例
const loggerOptions = {
    levels,
    transports
};
const logger = winston_1.default.createLogger(loggerOptions);
exports.logger = logger;
//# sourceMappingURL=logger.js.map