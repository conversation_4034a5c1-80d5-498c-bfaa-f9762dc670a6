"use strict";
/**
 * API网关服务主入口文件
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const index_1 = require("./config/index");
const logger_1 = require("./utils/logger");
const errorHandler_1 = require("./middleware/errorHandler");
const requestLogger_1 = require("./middleware/requestLogger");
const metrics_1 = require("./middleware/metrics");
// 路由导入
const miniprogram_1 = __importDefault(require("./routes/miniprogram"));
const health_1 = __importDefault(require("./routes/health"));
const admin_1 = __importDefault(require("./routes/admin"));
const app = (0, express_1.default)();
// 基础中间件
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: ((_a = index_1.config.ALLOWED_ORIGINS) === null || _a === void 0 ? void 0 : _a.split(',')) || ['http://localhost:3001'],
    credentials: true
}));
// 请求限制
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 1000, // 限制每个IP 15分钟内最多1000个请求
    message: {
        success: false,
        error: {
            code: 'RATE_LIMIT_ERROR',
            message: '请求过于频繁，请稍后再试'
        }
    }
});
app.use('/api/', limiter);
// 解析中间件
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// 日志和监控中间件
app.use(requestLogger_1.requestLogger);
app.use(metrics_1.metricsMiddleware);
// 路由注册
app.use('/api/miniprogram', miniprogram_1.default);
app.use('/api/health', health_1.default);
app.use('/api/admin', admin_1.default);
// 根路径
app.get('/', (req, res) => {
    res.json({
        name: 'Community Exchange API Gateway',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});
// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            code: 'NOT_FOUND_ERROR',
            message: `路径 ${req.originalUrl} 不存在`
        }
    });
});
// 错误处理中间件
app.use(errorHandler_1.errorHandler);
// 启动服务器
const PORT = index_1.config.PORT || 3000;
app.listen(PORT, () => {
    logger_1.logger.info(`API Gateway服务启动成功，端口: ${PORT}`);
    logger_1.logger.info(`环境: ${index_1.config.NODE_ENV}`);
    logger_1.logger.info(`数据库: ${index_1.config.DATABASE_URL ? '已连接' : '未配置'}`);
    logger_1.logger.info(`Redis: ${index_1.config.REDIS_URL ? '已连接' : '未配置'}`);
});
// 优雅关闭
process.on('SIGTERM', () => {
    logger_1.logger.info('收到SIGTERM信号，开始优雅关闭...');
    process.exit(0);
});
process.on('SIGINT', () => {
    logger_1.logger.info('收到SIGINT信号，开始优雅关闭...');
    process.exit(0);
});
exports.default = app;
//# sourceMappingURL=app.js.map