{"version": 3, "file": "requestLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/requestLogger.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAGH,4CAAwC;AAExC,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACxE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,GAAG,CAAA;IAEtD,SAAS;IACT,MAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAA;IAE9C,SAAS;IACT,eAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,IAAI,EAAE,EAAE;QAC/B,EAAE;QACF,KAAK;QACL,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE,OAAO,CAAC,YAAY,CAAC;KACjC,CAAC,CAAA;IAEF,SAAS;IACT,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;QACnC,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,GAAG,CAAA;QAEzC,SAAS;QACT,eAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,IAAI,IAAI,UAAU,IAAI,aAAa,EAAE,EAAE;YAC9D,QAAQ,EAAE,GAAG,QAAQ,IAAI;YACzB,UAAU;SACX,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AAyBQ,sCAAa;AAvBtB,SAAS;AACT,SAAS,mBAAmB,CAAC,IAAS;IACpC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;QAAE,OAAO,IAAI,CAAA;IAElD,MAAM,aAAa,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,CAAC,CAAA;IACjF,MAAM,MAAM,GAAQ,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;IAEjD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YACvB,IAAI,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBAC9C,MAAM,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAA;YAChC,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACvD,MAAM,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAA;YAC1C,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC"}