{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;AAGH,+CAAqC;AACrC,2CAAwC;AACxC,4CAAwC;AACxC,yDAAqD;AAyBrD;;GAEG;AACI,MAAM,iBAAiB,GAAG,CAAO,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACzF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAA;QAC5C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,eAAe;QAEpE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,QAAQ;iBAClB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,UAAU;QACV,MAAM,OAAO,GAAG,IAAA,qBAAM,EAAC,KAAK,EAAE,cAAM,CAAC,UAAU,CAAe,CAAA;QAE9D,cAAc;QACd,MAAM,IAAI,GAAG,MAAM,yBAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QACnD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,YAAY;iBACtB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,eAAe;QACf,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW;SACjC,CAAA;QAED,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;QAEjC,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,mBAAmB,EAAE,CAAC;YACxC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,SAAS;iBACnB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,mBAAmB,EAAE,CAAC;YACxC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,SAAS;iBACnB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,SAAS;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;AACH,CAAC,CAAA,CAAA;AAtEY,QAAA,iBAAiB,qBAsE7B;AAED;;GAEG;AACI,MAAM,sBAAsB,GAAG,CAAO,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC9F,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAA;QAC5C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAEpD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,QAAQ;iBAClB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,eAAe;QACf,MAAM,OAAO,GAAG,IAAA,qBAAM,EAAC,KAAK,EAAE,cAAM,CAAC,gBAAgB,CAAe,CAAA;QAEpE,aAAa;QACb,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC9D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,MAAM;iBAChB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW;SACjC,CAAA;QAED,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAA;QAEpC,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,mBAAmB,EAAE,CAAC;YACxC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,SAAS;iBACnB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,SAAS;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;AACH,CAAC,CAAA,CAAA;AAzDY,QAAA,sBAAsB,0BAyDlC;AAED;;GAEG;AACI,MAAM,YAAY,GAAG,CAAO,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACpF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAA;QAC5C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAEpD,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,OAAO,GAAG,IAAA,qBAAM,EAAC,KAAK,EAAE,cAAM,CAAC,UAAU,CAAe,CAAA;YAC9D,MAAM,IAAI,GAAG,MAAM,yBAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;YAEnD,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1B,GAAG,CAAC,IAAI,GAAG;oBACT,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,WAAW,EAAE,OAAO,CAAC,WAAW;iBACjC,CAAA;YACH,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,oBAAoB;QACpB,eAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;QAC7B,IAAI,EAAE,CAAA;IACR,CAAC;AACH,CAAC,CAAA,CAAA;AAzBY,QAAA,YAAY,gBAyBxB;AAED;;GAEG;AACI,MAAM,iBAAiB,GAAG,CAAC,UAAkB,EAAE,EAAE;IACtD,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAA;QAElD,cAAc;QACd,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACtE,OAAO,IAAI,EAAE,CAAA;QACf,CAAC;QAED,SAAS;QACT,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,MAAM;iBAChB;aACF,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC,CAAA;AAhCY,QAAA,iBAAiB,qBAgC7B"}