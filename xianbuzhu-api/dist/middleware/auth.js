"use strict";
/**
 * JWT认证中间件
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requirePermission = exports.optionalAuth = exports.authenticateAdminToken = exports.authenticateToken = void 0;
const jsonwebtoken_1 = require("jsonwebtoken");
const index_1 = require("../config/index");
const logger_1 = require("../utils/logger");
const userService_1 = require("../services/userService");
/**
 * 验证JWT token的中间件
 */
const authenticateToken = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
        if (!token) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'MISSING_TOKEN',
                    message: '缺少访问令牌'
                }
            });
        }
        // 验证token
        const decoded = (0, jsonwebtoken_1.verify)(token, index_1.config.JWT_SECRET);
        // 检查用户是否存在且活跃
        const user = yield userService_1.userService.findById(decoded.id);
        if (!user || !user.isActive) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'INVALID_USER',
                    message: '用户不存在或已被禁用'
                }
            });
        }
        // 将用户信息添加到请求对象
        req.user = {
            id: decoded.id,
            openid: decoded.openid,
            role: decoded.role,
            permissions: decoded.permissions
        };
        next();
    }
    catch (error) {
        logger_1.logger.error('Token验证失败:', error);
        if ((error === null || error === void 0 ? void 0 : error.name) === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'TOKEN_EXPIRED',
                    message: '访问令牌已过期'
                }
            });
        }
        if ((error === null || error === void 0 ? void 0 : error.name) === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'INVALID_TOKEN',
                    message: '无效的访问令牌'
                }
            });
        }
        return res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: '服务器内部错误'
            }
        });
    }
});
exports.authenticateToken = authenticateToken;
/**
 * 验证管理员token的中间件
 */
const authenticateAdminToken = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'MISSING_TOKEN',
                    message: '缺少访问令牌'
                }
            });
        }
        // 使用管理员JWT密钥验证
        const decoded = (0, jsonwebtoken_1.verify)(token, index_1.config.ADMIN_JWT_SECRET);
        // 检查是否为管理员角色
        if (decoded.role !== 'admin' && decoded.role !== 'superadmin') {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'INSUFFICIENT_PERMISSIONS',
                    message: '权限不足'
                }
            });
        }
        req.user = {
            id: decoded.id,
            role: decoded.role,
            permissions: decoded.permissions
        };
        next();
    }
    catch (error) {
        logger_1.logger.error('管理员Token验证失败:', error);
        if ((error === null || error === void 0 ? void 0 : error.name) === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'TOKEN_EXPIRED',
                    message: '访问令牌已过期'
                }
            });
        }
        return res.status(401).json({
            success: false,
            error: {
                code: 'INVALID_TOKEN',
                message: '无效的访问令牌'
            }
        });
    }
});
exports.authenticateAdminToken = authenticateAdminToken;
/**
 * 可选的认证中间件（不强制要求token）
 */
const optionalAuth = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (token) {
            const decoded = (0, jsonwebtoken_1.verify)(token, index_1.config.JWT_SECRET);
            const user = yield userService_1.userService.findById(decoded.id);
            if (user && user.isActive) {
                req.user = {
                    id: decoded.id,
                    openid: decoded.openid,
                    role: decoded.role,
                    permissions: decoded.permissions
                };
            }
        }
        next();
    }
    catch (error) {
        // 可选认证失败时不阻止请求，继续执行
        logger_1.logger.warn('可选认证失败:', error);
        next();
    }
});
exports.optionalAuth = optionalAuth;
/**
 * 权限检查中间件
 */
const requirePermission = (permission) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: '未授权访问'
                }
            });
        }
        const userPermissions = req.user.permissions || [];
        // 超级管理员拥有所有权限
        if (req.user.role === 'superadmin' || userPermissions.includes('all')) {
            return next();
        }
        // 检查特定权限
        if (!userPermissions.includes(permission)) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'INSUFFICIENT_PERMISSIONS',
                    message: '权限不足'
                }
            });
        }
        next();
    };
};
exports.requirePermission = requirePermission;
//# sourceMappingURL=auth.js.map