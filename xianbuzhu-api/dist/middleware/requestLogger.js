"use strict";
/**
 * 请求日志中间件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestLogger = void 0;
const logger_1 = require("../utils/logger");
const requestLogger = (req, res, next) => {
    const start = Date.now();
    const { method, path, query, body, ip, headers } = req;
    // 过滤敏感信息
    const filteredBody = filterSensitiveData(body);
    // 记录请求信息
    logger_1.logger.http(`${method} ${path}`, {
        ip,
        query,
        body: filteredBody,
        userAgent: headers['user-agent']
    });
    // 监听响应完成
    res.on('finish', () => {
        const duration = Date.now() - start;
        const { statusCode, statusMessage } = res;
        // 记录响应信息
        logger_1.logger.http(`${method} ${path} ${statusCode} ${statusMessage}`, {
            duration: `${duration}ms`,
            statusCode
        });
    });
    next();
};
exports.requestLogger = requestLogger;
// 过滤敏感数据
function filterSensitiveData(data) {
    if (!data || typeof data !== 'object')
        return data;
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'creditCard', 'pin'];
    const result = Array.isArray(data) ? [] : {};
    for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
            const value = data[key];
            if (sensitiveKeys.includes(key.toLowerCase())) {
                result[key] = '***FILTERED***';
            }
            else if (typeof value === 'object' && value !== null) {
                result[key] = filterSensitiveData(value);
            }
            else {
                result[key] = value;
            }
        }
    }
    return result;
}
//# sourceMappingURL=requestLogger.js.map