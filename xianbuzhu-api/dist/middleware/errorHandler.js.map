{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAGH,4CAAwC;AAExC,SAAS;AACT,MAAM,QAAS,SAAQ,KAAK;IAK1B,YAAY,OAAe,EAAE,UAAkB,EAAE,OAAa;QAC5D,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QAEtB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACjD,CAAC;CACF;AAgFsB,4BAAQ;AA9E/B,UAAU;AACV,MAAM,YAAY,GAAG,CAAC,GAAU,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACnF,aAAa;IACb,IAAI,UAAU,GAAG,GAAG,CAAA;IACpB,IAAI,OAAO,GAAG,SAAS,CAAA;IACvB,IAAI,YAAY,GAAQ,EAAE,CAAA;IAE1B,YAAY;IACZ,IAAI,GAAG,YAAY,QAAQ,EAAE,CAAC;QAC5B,UAAU,GAAG,GAAG,CAAC,UAAU,CAAA;QAC3B,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;QACrB,YAAY,GAAG,GAAG,CAAC,OAAO,IAAI,EAAE,CAAA;QAEhC,gBAAgB;QAChB,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;YACtB,eAAM,CAAC,IAAI,CAAC,SAAS,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAA;QAC5G,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,KAAK,CAAC,SAAS,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAA;QAC5F,CAAC;IACH,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAC1C,gBAAgB;QAChB,UAAU,GAAG,GAAG,CAAA;QAChB,OAAO,GAAG,QAAQ,CAAA;QAClB,YAAY,GAAG,GAAG,CAAA;QAClB,eAAM,CAAC,IAAI,CAAC,WAAW,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAA;IAClG,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC5C,SAAS;QACT,UAAU,GAAG,GAAG,CAAA;QAChB,OAAO,GAAG,WAAW,CAAA;QACrB,YAAY,GAAG,EAAE,aAAa,EAAE,GAAG,CAAC,OAAO,EAAE,CAAA;QAC7C,eAAM,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAA;IAC1E,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC5C,WAAW;QACX,UAAU,GAAG,GAAG,CAAA;QAChB,OAAO,GAAG,WAAW,CAAA;QACrB,YAAY,GAAG,EAAE,aAAa,EAAE,GAAG,CAAC,OAAO,EAAE,CAAA;QAC7C,eAAM,CAAC,IAAI,CAAC,YAAY,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAA;IAC5E,CAAC;SAAM,CAAC;QACN,SAAS;QACT,eAAM,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAA;IAClG,CAAC;IAED,OAAO;IACP,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ,UAAU,EAAE;YAC1B,OAAO;YACP,OAAO,EAAE,YAAY;SACtB;KACF,CAAC,CAAA;AACJ,CAAC,CAAA;AA2BQ,oCAAY;AAzBrB,WAAW;AACX,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,GAAG,EAAE,EAAE;IACtC,eAAM,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;IAC5D,iBAAiB;IACjB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC,EAAE,KAAK,CAAC,CAAA;IACX,CAAC;AACH,CAAC,CAAC,CAAA;AAEF,kBAAkB;AAClB,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAW,EAAE,OAAO,EAAE,EAAE;IACxD,eAAM,CAAC,KAAK,CAAC,kBAAkB,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,KAAI,MAAM,EAAE,EAAE;QAC1D,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK;KACrB,CAAC,CAAA;IACF,iBAAiB;IACjB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC,EAAE,KAAK,CAAC,CAAA;IACX,CAAC;AACH,CAAC,CAAC,CAAA"}