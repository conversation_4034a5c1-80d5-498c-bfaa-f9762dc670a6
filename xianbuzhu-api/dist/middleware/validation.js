"use strict";
/**
 * 请求验证中间件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateRequest = void 0;
const errorHandler_1 = require("./errorHandler");
/**
 * 验证请求数据
 * @param schema Joi验证schema
 * @param source 数据来源 (body, query, params)
 * @returns 中间件函数
 */
const validateRequest = (schema, source = 'body') => {
    return (req, res, next) => {
        try {
            const data = req[source];
            const { error, value } = schema.validate(data, {
                abortEarly: false,
                allowUnknown: true,
                stripUnknown: true
            });
            if (error) {
                // 格式化错误消息
                const errorMessage = error.details
                    .map(detail => detail.message)
                    .join('; ');
                throw new errorHandler_1.AppError(`请求数据验证失败: ${errorMessage}`, 400, {
                    originalError: error,
                    source,
                    data
                });
            }
            // 替换请求数据为验证后的数据
            req[source] = value;
            next();
        }
        catch (err) {
            next(err);
        }
    };
};
exports.validateRequest = validateRequest;
//# sourceMappingURL=validation.js.map