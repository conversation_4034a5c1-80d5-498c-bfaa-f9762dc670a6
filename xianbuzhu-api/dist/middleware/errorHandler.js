"use strict";
/**
 * 错误处理中间件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppError = exports.errorHandler = void 0;
const logger_1 = require("../utils/logger");
// 自定义错误类
class AppError extends Error {
    constructor(message, statusCode, details) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        this.details = details;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
// 错误处理中间件
const errorHandler = (err, req, res, next) => {
    // 默认错误状态码和消息
    let statusCode = 500;
    let message = '服务器内部错误';
    let errorDetails = {};
    // 处理自定义应用错误
    if (err instanceof AppError) {
        statusCode = err.statusCode;
        message = err.message;
        errorDetails = err.details || {};
        // 记录操作错误（非系统错误）
        if (err.isOperational) {
            logger_1.logger.warn(`操作错误: ${message}`, { statusCode, path: req.path, method: req.method, details: errorDetails });
        }
        else {
            logger_1.logger.error(`系统错误: ${message}`, { stack: err.stack, path: req.path, method: req.method });
        }
    }
    else if (err.name === 'ValidationError') {
        // Mongoose 验证错误
        statusCode = 400;
        message = '数据验证失败';
        errorDetails = err;
        logger_1.logger.warn(`数据验证错误: ${message}`, { path: req.path, method: req.method, details: errorDetails });
    }
    else if (err.name === 'JsonWebTokenError') {
        // JWT 错误
        statusCode = 401;
        message = '无效的身份验证令牌';
        errorDetails = { originalError: err.message };
        logger_1.logger.warn(`JWT错误: ${message}`, { path: req.path, method: req.method });
    }
    else if (err.name === 'TokenExpiredError') {
        // JWT 过期错误
        statusCode = 401;
        message = '身份验证令牌已过期';
        errorDetails = { originalError: err.message };
        logger_1.logger.warn(`JWT过期错误: ${message}`, { path: req.path, method: req.method });
    }
    else {
        // 其他系统错误
        logger_1.logger.error(`未处理的错误: ${err.message}`, { stack: err.stack, path: req.path, method: req.method });
    }
    // 响应错误
    res.status(statusCode).json({
        success: false,
        error: {
            code: `HTTP_${statusCode}`,
            message,
            details: errorDetails
        }
    });
};
exports.errorHandler = errorHandler;
// 处理未捕获的异常
process.on('uncaughtException', (err) => {
    logger_1.logger.error(`未捕获的异常: ${err.message}`, { stack: err.stack });
    // 在生产环境，10秒后退出进程
    if (process.env.NODE_ENV === 'production') {
        setTimeout(() => {
            process.exit(1);
        }, 10000);
    }
});
// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error(`未处理的Promise拒绝: ${(reason === null || reason === void 0 ? void 0 : reason.message) || reason}`, {
        promise: promise,
        stack: reason === null || reason === void 0 ? void 0 : reason.stack
    });
    // 在生产环境，10秒后退出进程
    if (process.env.NODE_ENV === 'production') {
        setTimeout(() => {
            process.exit(1);
        }, 10000);
    }
});
//# sourceMappingURL=errorHandler.js.map