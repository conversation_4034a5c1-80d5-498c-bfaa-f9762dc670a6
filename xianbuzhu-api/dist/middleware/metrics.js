"use strict";
/**
 * 指标收集中间件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMetrics = exports.metricsMiddleware = void 0;
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
// 简单的指标收集器
class MetricsCollector {
    constructor() {
        this.metrics = new Map();
        // 定期打印指标（开发环境）
        if (config_1.config.NODE_ENV !== 'production') {
            setInterval(() => {
                this.printMetrics();
            }, 60000); // 每分钟
        }
    }
    // 记录请求指标
    recordRequest(path, duration) {
        const endpoint = this.normalizePath(path);
        if (!this.metrics.has(endpoint)) {
            this.metrics.set(endpoint, { count: 0, totalDuration: 0 });
        }
        const metric = this.metrics.get(endpoint);
        metric.count += 1;
        metric.totalDuration += duration;
    }
    // 标准化路径（移除动态参数）
    normalizePath(path) {
        // 简单的路径标准化，实际项目中可能需要更复杂的处理
        return path.replace(/\/[0-9a-fA-F]{24}|\/\d+/g, '/:id');
    }
    // 打印指标
    printMetrics() {
        logger_1.logger.info('API 性能指标报告:');
        this.metrics.forEach((value, key) => {
            const avgDuration = value.totalDuration / value.count;
            logger_1.logger.info(`  ${key}: 请求数=${value.count}, 平均响应时间=${avgDuration.toFixed(2)}ms`);
        });
    }
    // 获取指标数据（用于Prometheus等监控系统）
    getMetricsData() {
        const data = {};
        this.metrics.forEach((value, key) => {
            const avgDuration = value.totalDuration / value.count;
            data[key] = {
                count: value.count,
                avgDuration
            };
        });
        return data;
    }
}
// 创建指标收集器实例
const metricsCollector = new MetricsCollector();
// 指标中间件
const metricsMiddleware = (req, res, next) => {
    const start = Date.now();
    // 监听响应完成
    res.on('finish', () => {
        const duration = Date.now() - start;
        metricsCollector.recordRequest(req.path, duration);
    });
    next();
};
exports.metricsMiddleware = metricsMiddleware;
// 导出指标数据的路由处理函数
const getMetrics = (req, res) => {
    res.json({
        success: true,
        data: metricsCollector.getMetricsData()
    });
};
exports.getMetrics = getMetrics;
//# sourceMappingURL=metrics.js.map